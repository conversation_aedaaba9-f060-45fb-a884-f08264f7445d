{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IRONFORGE Temporal Query Explorer\n", "Interactive notebook for querying temporal patterns in NetworkX graphs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the query engine\n", "from temporal_query_engine import TemporalQueryEngine\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import networkx as nx\n", "\n", "# Initialize the engine\n", "engine = TemporalQueryEngine()\n", "print(f\"✅ Loaded {len(engine.sessions)} sessions for analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quick Start: Ask Your Questions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ask temporal questions\n", "result = engine.ask(\"What happens after a 40% zone event?\")\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze opening patterns\n", "result = engine.ask(\"When session starts with expansion, what's the final range?\")\n", "\n", "# Visualize the results\n", "if 'pattern_outcomes' in result:\n", "    patterns = result['pattern_outcomes']\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Pattern frequency\n", "    pattern_names = list(patterns.keys())\n", "    pattern_counts = [patterns[p]['count'] for p in pattern_names]\n", "    \n", "    ax1.bar(pattern_names, pattern_counts)\n", "    ax1.set_title('Opening Pattern Frequency')\n", "    ax1.set_ylabel('Number of Sessions')\n", "    plt.setp(ax1.get_xticklabels(), rotation=45)\n", "    \n", "    # Average ranges by pattern\n", "    avg_ranges = [patterns[p]['avg_range'] for p in pattern_names]\n", "    \n", "    ax2.bar(pattern_names, avg_ranges, color='orange')\n", "    ax2.set_title('Average Final Range by Opening Pattern')\n", "    ax2.set_ylabel('Points')\n", "    plt.setp(ax2.get_xticklabels(), rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Graph Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze a specific session's graph structure\n", "session_id = list(engine.sessions.keys())[0]  # Pick first session\n", "graph = engine.graphs[session_id]\n", "nodes = engine.sessions[session_id]\n", "\n", "print(f\"📊 Analyzing session: {session_id}\")\n", "print(f\"Nodes: {graph.number_of_nodes()}, Edges: {graph.number_of_edges()}\")\n", "\n", "# Graph metrics\n", "if graph.number_of_nodes() > 0:\n", "    density = nx.density(graph)\n", "    print(f\"Graph density: {density:.3f}\")\n", "    \n", "    # Degree distribution\n", "    degrees = dict(graph.degree())\n", "    plt.figure(figsize=(10, 6))\n", "    plt.hist(list(degrees.values()), bins=20, alpha=0.7)\n", "    plt.title(f'Degree Distribution - {session_id}')\n", "    plt.xlabel('Node Degree')\n", "    plt.ylabel('Frequency')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom Temporal Queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define your own custom query function\n", "def analyze_price_momentum(session_id, window_minutes=10):\n", "    \"\"\"Analyze price momentum patterns in a session\"\"\"\n", "    nodes = engine.sessions[session_id]\n", "    graph = engine.graphs[session_id]\n", "    \n", "    # Calculate momentum windows\n", "    window_ms = window_minutes * 60 * 1000\n", "    \n", "    momentum_events = []\n", "    for i in range(0, len(nodes) - 1):\n", "        current_node = nodes.iloc[i]\n", "        future_nodes = nodes[\n", "            (nodes['t'] > current_node['t']) & \n", "            (nodes['t'] <= current_node['t'] + window_ms)\n", "        ]\n", "        \n", "        if len(future_nodes) > 0:\n", "            price_change = future_nodes['price'].iloc[-1] - current_node['price']\n", "            momentum_events.append({\n", "                'time': current_node['t'],\n", "                'price': current_node['price'],\n", "                'momentum': price_change,\n", "                'events_ahead': len(future_nodes)\n", "            })\n", "    \n", "    return pd.DataFrame(momentum_events)\n", "\n", "# Analyze momentum for a session\n", "momentum_df = analyze_price_momentum(session_id)\n", "print(f\"🎯 Momentum analysis for {session_id}:\")\n", "print(momentum_df.describe())\n", "\n", "# Plot momentum over time\n", "plt.figure(figsize=(12, 6))\n", "plt.scatter(range(len(momentum_df)), momentum_df['momentum'], alpha=0.6)\n", "plt.axhline(y=0, color='red', linestyle='--', alpha=0.5)\n", "plt.title(f'Price Momentum Over Time - {session_id}')\n", "plt.xlabel('Event Sequence')\n", "plt.ylabel('Price Change (Points)')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Query Widget"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create interactive query widget\n", "from ipywidgets import interact, widgets\n", "\n", "@interact\n", "def interactive_query(\n", "    question=widgets.Text(\n", "        value=\"What happens after a 40% zone event?\",\n", "        description=\"Question:\",\n", "        style={'description_width': 'initial'},\n", "        layout=widgets.Layout(width='500px')\n", "    )\n", "):\n", "    if question.strip():\n", "        result = engine.ask(question)\n", "        \n", "        print(f\"🤔 Query: {question}\")\n", "        print(f\"🎯 Results:\")\n", "        \n", "        for key, value in result.items():\n", "            if isinstance(value, list) and len(value) > 3:\n", "                print(f\"  {key}: {len(value)} items (showing first 3)\")\n", "                for item in value[:3]:\n", "                    print(f\"    • {item}\")\n", "            else:\n", "                print(f\"  {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Session Comparison Tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare multiple sessions\n", "def compare_sessions(session_ids):\n", "    \"\"\"Compare temporal patterns across multiple sessions\"\"\"\n", "    comparison = []\n", "    \n", "    for session_id in session_ids:\n", "        if session_id in engine.sessions:\n", "            info = engine.session_info(session_id)\n", "            comparison.append(info)\n", "    \n", "    return pd.DataFrame(comparison)\n", "\n", "# Compare first 5 sessions\n", "sample_sessions = list(engine.sessions.keys())[:5]\n", "comparison_df = compare_sessions(sample_sessions)\n", "\n", "print(\"📊 Session Comparison:\")\n", "display(comparison_df[['session_id', 'nodes', 'edges', 'price_range']])\n", "\n", "# Visualize comparison\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Price ranges\n", "axes[0,0].bar(range(len(comparison_df)), comparison_df['price_range'])\n", "axes[0,0].set_title('Price Ranges by Session')\n", "axes[0,0].set_ylabel('Points')\n", "\n", "# Node counts\n", "axes[0,1].bar(range(len(comparison_df)), comparison_df['nodes'])\n", "axes[0,1].set_title('Event Counts by Session')\n", "axes[0,1].set_ylabel('Number of Events')\n", "\n", "# Edge counts\n", "axes[1,0].bar(range(len(comparison_df)), comparison_df['edges'])\n", "axes[1,0].set_title('Edge Counts by Session')\n", "axes[1,0].set_ylabel('Number of Edges')\n", "\n", "# Range vs Events scatter\n", "axes[1,1].scatter(comparison_df['nodes'], comparison_df['price_range'])\n", "axes[1,1].set_title('Price Range vs Event Count')\n", "axes[1,1].set_xlabel('Number of Events')\n", "axes[1,1].set_ylabel('Price Range (Points)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}