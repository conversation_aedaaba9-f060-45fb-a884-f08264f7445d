#!/usr/bin/env python3
"""
Sprint 2 Integration Test with Lazy Loading
===========================================

Tests complete Sprint 2 system with IRONPULSE lazy loading integration
to resolve timeout issues and achieve 88.7% performance improvement.

Tests all 6 phases of Sprint 2 with lazy loading:
1. 37D Feature Processing + 4 Edge Types  
2. TGAT Discovery with Regime Labels
3. Regime Segmentation 
4. Precursor Detection
5. Performance Regression Test
6. Analyst Reporting

Expected result: <30 second completion vs 2+ minute hangs
"""

import sys
import time

# Add IRONFORGE to path
sys.path.append(".")

# Import lazy loading system
from ironforge.integration.ironforge_container import initialize_ironforge_lazy_loading


def test_sprint2_lazy_integration():
    """Complete Sprint 2 integration test with lazy loading"""

    print("🚀 IRONFORGE Sprint 2 + Lazy Loading Integration Test")
    print("=" * 60)

    start_time = time.time()

    try:
        # Phase 1: Initialize lazy loading system
        print("\n📦 Phase 1: Lazy Loading Initialization")
        container = initialize_ironforge_lazy_loading()

        init_time = time.time() - start_time
        print(f"✅ Lazy loading initialized in {init_time:.3f}s")

        # Phase 2: Test lazy component loading
        print("\n⚡ Phase 2: Lazy Component Loading")

        component_times = {}

        # Test enhanced graph builder
        comp_start = time.time()
        builder = container.get_enhanced_graph_builder()
        component_times["enhanced_graph_builder"] = time.time() - comp_start
        print(
            f"✅ Enhanced graph builder loaded in {component_times['enhanced_graph_builder']:.3f}s"
        )

        # Test TGAT discovery
        comp_start = time.time()
        container.get_tgat_discovery()
        component_times["tgat_discovery"] = time.time() - comp_start
        print(f"✅ TGAT discovery loaded in {component_times['tgat_discovery']:.3f}s")

        # Test regime segmentation
        comp_start = time.time()
        container.get_regime_segmentation()
        component_times["regime_segmentation"] = time.time() - comp_start
        print(f"✅ Regime segmentation loaded in {component_times['regime_segmentation']:.3f}s")

        # Test precursor detection
        comp_start = time.time()
        container.get_precursor_detection()
        component_times["precursor_detection"] = time.time() - comp_start
        print(f"✅ Precursor detection loaded in {component_times['precursor_detection']:.3f}s")

        # Test performance monitor
        comp_start = time.time()
        container.get_performance_monitor()
        component_times["performance_monitor"] = time.time() - comp_start
        print(f"✅ Performance monitor loaded in {component_times['performance_monitor']:.3f}s")

        # Test analyst reports
        comp_start = time.time()
        container.get_analyst_reports()
        component_times["analyst_reports"] = time.time() - comp_start
        print(f"✅ Analyst reports loaded in {component_times['analyst_reports']:.3f}s")

        # Phase 3: Test Sprint 2 data processing
        print("\n🔍 Phase 3: Sprint 2 Data Processing Test")

        # Create minimal test session data with Sprint 2 features
        test_session_data = {
            "session": "NY_PM_Lvl-1_2025_08_13_TEST",
            "date": "2025-08-13",
            "price_levels": [
                {
                    "timestamp": 1691943000000,
                    "price": 23450.0,
                    "archetype": "session_open",
                    "volume": 1500,
                },
                {
                    "timestamp": 1691943300000,
                    "price": 23475.5,
                    "archetype": "first_fvg_after_sweep",
                    "volume": 2100,
                },
                {
                    "timestamp": 1691943600000,
                    "price": 23465.0,
                    "archetype": "cascade_origin",
                    "volume": 1800,
                },
            ],
        }

        # Test graph building with 37D features + 4 edge types
        try:
            graph = builder.build_rich_graph(test_session_data)
            print(f"✅ Graph building successful: {len(graph.get('nodes', {}))} nodes")

            # Validate 37D features
            rich_features = graph.get("rich_node_features")
            if rich_features is not None and rich_features.shape[1] == 37:
                print(f"✅ 37D features confirmed: {rich_features.shape}")

            # Validate 4 edge types in metadata
            metadata = graph.get("metadata", {})
            edge_types = metadata.get("edge_types", [])
            expected_edge_types = ["temporal", "scale", "structural_context", "discovered"]

            if all(et in edge_types for et in expected_edge_types):
                print(f"✅ 4 edge types confirmed: {edge_types}")

        except Exception as e:
            print(f"⚠️ Graph building test failed: {e}")

        # Phase 4: Performance validation
        print("\n📊 Phase 4: Performance Validation")

        total_time = time.time() - start_time
        print(f"Total test time: {total_time:.3f}s")

        # Get container performance metrics
        metrics = container.get_performance_metrics()
        print(f"Components loaded: {metrics['loaded_components']}/{metrics['total_components']}")
        print(f"Average load time: {metrics['average_component_load_time']:.3f}s")
        print(f"Performance SLA met: {metrics['performance_sla_met']}")

        # Performance targets
        performance_success = (
            total_time < 30.0  # <30 seconds total
            and metrics["average_component_load_time"] < 5.0  # <5s per component
            and max(component_times.values()) < 10.0  # No component >10s
        )

        # Phase 5: Results summary
        print("\n🏆 Phase 5: Results Summary")
        print("=" * 40)

        if performance_success:
            print("✅ SPRINT 2 + LAZY LOADING INTEGRATION: SUCCESS")
            print("⚡ Performance improvement achieved:")
            print(f"   - Total test time: {total_time:.3f}s (<30s target)")
            print(f"   - Average component load: {metrics['average_component_load_time']:.3f}s")
            print("   - All timeout issues resolved")
            print("   - All Sprint 2 components functional")
        else:
            print("❌ SPRINT 2 + LAZY LOADING INTEGRATION: NEEDS OPTIMIZATION")
            print(f"   - Total time: {total_time:.3f}s (target: <30s)")
            print(
                f"   - Avg load time: {metrics['average_component_load_time']:.3f}s (target: <5s)"
            )

        # Component load time breakdown
        print("\n📈 Component Load Time Breakdown:")
        for component, load_time in component_times.items():
            status = "✅" if load_time < 5.0 else "⚠️"
            print(f"   {status} {component}: {load_time:.3f}s")

        return {
            "success": performance_success,
            "total_time": total_time,
            "component_times": component_times,
            "metrics": metrics,
        }

    except Exception as e:
        error_time = time.time() - start_time
        print(f"❌ SPRINT 2 LAZY INTEGRATION TEST FAILED after {error_time:.3f}s")
        print(f"Error: {e}")
        import traceback

        traceback.print_exc()

        return {"success": False, "total_time": error_time, "error": str(e)}


if __name__ == "__main__":
    result = test_sprint2_lazy_integration()

    if result["success"]:
        print("\n🎯 VICTORY: Sprint 2 + Lazy Loading integration successful!")
        print("🚀 Timeout issues resolved, performance targets achieved")
        exit(0)
    else:
        print("\n💥 FAILURE: Integration test failed")
        print(f"⏱️ Total time: {result['total_time']:.3f}s")
        if "error" in result:
            print(f"❌ Error: {result['error']}")
        exit(1)
