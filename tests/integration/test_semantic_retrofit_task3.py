#!/usr/bin/env python3
"""
Test Task 3: Session Context Preservation - Metadata Attachment
"""

import sys

# Add IRONFORGE to path
sys.path.insert(0, "/Users/<USER>/IRONPULSE/IRONFORGE")


def test_session_context_preservation():
    """Test that session metadata is correctly extracted and preserved"""

    print("🧪 Testing Task 3: Session Context Preservation")
    print("=" * 60)

    try:
        # Import IRONFORGE components
        from ironforge.learning.enhanced_graph_builder import EnhancedGraphBuilder

        print("✅ Successfully imported EnhancedGraphBuilder")

        # Create test instance
        builder = EnhancedGraphBuilder()
        print("✅ Created EnhancedGraphBuilder instance")

        # Test 1: Create comprehensive test session data
        test_session_data = {
            "session_metadata": {
                "session_type": "ny_am",
                "session_date": "2025-08-14",
                "session_start": "09:30:00",
                "session_end": "12:00:00",
                "session_duration": 150,
                "session_high": 23600.0,
                "session_low": 23450.0,
                "session_open": 23500.0,
                "session_close": 23580.0,
            },
            "session_fpfvg": {"fpfvg_present": True, "fpfvg_formation": {"gap_size": 15.5}},
            "price_movements": [
                {
                    "timestamp": "09:30:00",
                    "price_level": 23500.0,
                    "movement_type": "open",
                    "normalized_price": 0.33,
                    "context": "NY AM session open with expansion phase",
                },
                {
                    "timestamp": "10:15:00",
                    "price_level": 23575.0,
                    "event_type": "redelivery",
                    "context": "FVG redelivered during expansion",
                },
                {
                    "timestamp": "11:45:00",
                    "price_level": 23580.0,
                    "movement_type": "consolidation_break",
                },
            ],
            "session_liquidity_events": [
                {
                    "timestamp": "10:15:00",
                    "event_type": "redelivery",
                    "target_level": "fvg_premium",
                },
                {"timestamp": "11:30:00", "event_type": "sweep", "target_level": "liquidity_pool"},
            ],
        }

        print("✅ Created comprehensive test session data")

        # Test 2: Build rich graph and extract metadata
        try:
            graph, session_metadata = builder.build_rich_graph(
                test_session_data, session_file_path="test_session.json"
            )
            print("✅ Successfully built graph and extracted session metadata")
        except Exception as e:
            print(f"❌ ERROR: build_rich_graph failed: {e}")
            return False

        # Test 3: Verify metadata structure and content
        required_fields = [
            "session_name",
            "session_type",
            "session_date",
            "session_start",
            "session_end",
            "session_start_time",
            "session_end_time",
            "session_duration",
            "anchor_timeframe",
            "market_characteristics",
            "semantic_events_count",
            "session_quality",
        ]

        print("\n🔍 Session Metadata Verification:")
        for field in required_fields:
            if field in session_metadata:
                value = session_metadata[field]
                print(f"  ✅ {field}: {value}")
            else:
                print(f"  ❌ MISSING: {field}")
                return False

        # Test 4: Verify specific values
        print("\n📊 Metadata Value Validation:")

        # Session name standardization
        if session_metadata["session_name"] == "NY_AM":
            print("  ✅ Session name correctly standardized: ny_am → NY_AM")
        else:
            print(f"  ❌ ERROR: Expected 'NY_AM', got '{session_metadata['session_name']}'")

        # ISO timestamp formatting
        expected_start = "2025-08-14T09:30:00Z"
        if session_metadata["session_start_time"] == expected_start:
            print(f"  ✅ Start time correctly formatted: {expected_start}")
        else:
            print(
                f"  ❌ ERROR: Expected '{expected_start}', got '{session_metadata['session_start_time']}'"
            )

        # Anchor timeframe determination
        if session_metadata["anchor_timeframe"] == "1m":
            print("  ✅ Anchor timeframe correctly determined: 1m (Level 1 data)")
        else:
            print(f"  ❌ UNEXPECTED: Anchor timeframe is '{session_metadata['anchor_timeframe']}'")

        # Test 5: Market characteristics analysis
        market_chars = session_metadata["market_characteristics"]
        print("\n🏪 Market Characteristics:")

        if market_chars["fpfvg_present"]:
            print("  ✅ FPFVG presence correctly detected")
        else:
            print("  ❌ ERROR: FPFVG should be detected as present")

        expansion_count = market_chars["expansion_phases"]
        print(f"  📈 Expansion phases detected: {expansion_count}")

        liquidity_count = market_chars["liquidity_events_count"]
        if liquidity_count == 2:
            print(f"  ✅ Liquidity events correctly counted: {liquidity_count}")
        else:
            print(f"  ❌ ERROR: Expected 2 liquidity events, got {liquidity_count}")

        # Price range calculation
        price_range_pct = market_chars["price_range_pct"]
        expected_range = round(((23600 - 23450) / 23500) * 100, 2)  # ~0.64%
        if abs(price_range_pct - expected_range) < 0.1:
            print(
                f"  ✅ Price range correctly calculated: {price_range_pct}% (expected ~{expected_range}%)"
            )
        else:
            print(
                f"  ❌ ERROR: Price range {price_range_pct}% doesn't match expected {expected_range}%"
            )

        # Test 6: Semantic events counting
        semantic_counts = session_metadata["semantic_events_count"]
        print("\n🎯 Semantic Events Count:")

        expected_redelivery = 2  # One in price_movements context, one in liquidity_events
        if semantic_counts["fvg_redelivery"] >= expected_redelivery:
            print(f"  ✅ FVG redelivery events: {semantic_counts['fvg_redelivery']}")
        else:
            print(
                f"  ❌ ERROR: Expected ≥{expected_redelivery} redelivery events, got {semantic_counts['fvg_redelivery']}"
            )

        if semantic_counts["expansion_mentions"] >= 1:
            print(f"  ✅ Expansion mentions: {semantic_counts['expansion_mentions']}")
        else:
            print(
                f"  ❌ ERROR: Expected ≥1 expansion mention, got {semantic_counts['expansion_mentions']}"
            )

        # Test 7: Session quality assessment
        session_quality = session_metadata["session_quality"]
        print(f"\n🏆 Session Quality Assessment: {session_quality}")

        if session_quality in ["excellent", "good"]:
            print("  ✅ High quality session detected")
        elif session_quality == "adequate":
            print("  ⚠️ Adequate quality session")
        else:
            print("  ❌ Poor quality session - unexpected for comprehensive test data")

        # Test 8: Metadata preservation timestamp
        metadata_timestamp = session_metadata.get("metadata_extracted_at", "")
        if metadata_timestamp and "T" in metadata_timestamp:
            print(f"  ✅ Metadata timestamp preserved: {metadata_timestamp[:19]}...")
        else:
            print(f"  ❌ ERROR: Invalid metadata timestamp: {metadata_timestamp}")

        print("\n" + "=" * 60)
        print("📝 Task 3 Results Summary:")
        print("✅ Session metadata extraction working")
        print("✅ Session name standardization working")
        print("✅ Anchor timeframe determination working")
        print("✅ Market characteristics analysis working")
        print("✅ Semantic events counting working")
        print("✅ Session quality assessment working")
        print("✅ build_rich_graph returns (graph, metadata) tuple")
        print("🎉 TASK 3 COMPLETE: Session Context Preservation successful!")

        return True

    except Exception as e:
        print(f"❌ ERROR in Task 3 testing: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_session_context_preservation()
    exit(0 if success else 1)
