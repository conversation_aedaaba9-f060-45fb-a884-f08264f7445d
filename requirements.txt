# IRONFORGE Archaeological Discovery System Requirements

# Core dependencies from iron-core
iron-core>=1.0.0

# Deep learning and neural networks
torch>=1.9.0,<2.5.0
torch-geometric>=2.0.0

# Scientific computing and data analysis  
numpy>=1.21.0,<2.0.0
scipy>=1.6.0
pandas>=1.3.0
scikit-learn>=1.0.0,<1.5.0

# Graph and network analysis
networkx>=2.6.0

# Machine learning utilities
joblib>=1.2.0

# Type hints and utilities
typing-extensions>=4.0.0

# Testing and validation
pytest>=6.0.0
pytest-cov>=3.0.0

# Visualization (optional)
matplotlib>=3.3.0
seaborn>=0.11.0

# Progress bars and utilities
tqdm>=4.60.0

# JSON handling and serialization  
orjson>=3.6.0

# Performance profiling
memory-profiler>=0.60.0

# Development dependencies
black>=22.0.0
isort>=5.10.0
flake8>=4.0.0
mypy>=0.950