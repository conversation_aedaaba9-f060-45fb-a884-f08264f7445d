"""
IRONFORGE Authentication and Security
====================================

Authentication, authorization, and security components for remote agents.
Handles JWT tokens, API keys, role-based access control, and secure communication.
"""

import hashlib
import hmac
import json
import logging
import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

try:
    import jwt
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa
    from cryptography.hazmat.primitives.serialization import load_pem_private_key, load_pem_public_key
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

logger = logging.getLogger(__name__)


class Role(Enum):
    ADMIN = "admin"
    AGENT = "agent"
    VIEWER = "viewer"
    WORKER = "worker"


class Permission(Enum):
    READ = "read"
    WRITE = "write"
    EXECUTE = "execute"
    ADMIN = "admin"


@dataclass
class User:
    """User account information"""
    user_id: str
    username: str
    email: str
    roles: Set[Role]
    permissions: Set[Permission]
    created_at: datetime
    last_login: Optional[datetime] = None
    is_active: bool = True
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class APIKey:
    """API key for service authentication"""
    key_id: str
    key_hash: str
    name: str
    roles: Set[Role]
    permissions: Set[Permission]
    created_at: datetime
    expires_at: Optional[datetime] = None
    last_used: Optional[datetime] = None
    is_active: bool = True


class AuthenticationManager:
    """
    Manages authentication and authorization for IRONFORGE remote agents.
    Supports JWT tokens, API keys, and role-based access control.
    """
    
    def __init__(self, secret_key: str = None, private_key_path: str = None, 
                 public_key_path: str = None):
        if not CRYPTO_AVAILABLE:
            raise RuntimeError("Cryptography libraries not available")
        
        self.secret_key = secret_key or secrets.token_urlsafe(32)
        self.users: Dict[str, User] = {}
        self.api_keys: Dict[str, APIKey] = {}
        
        # JWT configuration
        self.jwt_algorithm = "HS256"
        self.jwt_expiration = timedelta(hours=24)
        
        # Load RSA keys if provided
        self.private_key = None
        self.public_key = None
        if private_key_path and public_key_path:
            self._load_rsa_keys(private_key_path, public_key_path)
            self.jwt_algorithm = "RS256"
        
        # Role-based permissions
        self.role_permissions = {
            Role.ADMIN: {Permission.READ, Permission.WRITE, Permission.EXECUTE, Permission.ADMIN},
            Role.AGENT: {Permission.READ, Permission.WRITE, Permission.EXECUTE},
            Role.WORKER: {Permission.READ, Permission.EXECUTE},
            Role.VIEWER: {Permission.READ}
        }
    
    def _load_rsa_keys(self, private_key_path: str, public_key_path: str):
        """Load RSA private and public keys"""
        try:
            with open(private_key_path, 'rb') as f:
                self.private_key = load_pem_private_key(f.read(), password=None)
            
            with open(public_key_path, 'rb') as f:
                self.public_key = load_pem_public_key(f.read())
            
            logger.info("RSA keys loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load RSA keys: {e}")
            raise
    
    def generate_rsa_keys(self, private_key_path: str, public_key_path: str):
        """Generate and save RSA key pair"""
        try:
            # Generate private key
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048
            )
            
            # Get public key
            public_key = private_key.public_key()
            
            # Save private key
            with open(private_key_path, 'wb') as f:
                f.write(private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))
            
            # Save public key
            with open(public_key_path, 'wb') as f:
                f.write(public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                ))
            
            self.private_key = private_key
            self.public_key = public_key
            self.jwt_algorithm = "RS256"
            
            logger.info("RSA key pair generated and saved")
            
        except Exception as e:
            logger.error(f"Failed to generate RSA keys: {e}")
            raise
    
    def create_user(self, username: str, email: str, roles: List[str], 
                   password: str = None) -> str:
        """Create a new user account"""
        user_id = secrets.token_urlsafe(16)
        
        # Convert role strings to Role enums
        user_roles = set()
        for role_str in roles:
            try:
                user_roles.add(Role(role_str))
            except ValueError:
                logger.warning(f"Invalid role: {role_str}")
        
        # Calculate permissions from roles
        permissions = set()
        for role in user_roles:
            permissions.update(self.role_permissions.get(role, set()))
        
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            roles=user_roles,
            permissions=permissions,
            created_at=datetime.now()
        )
        
        self.users[user_id] = user
        logger.info(f"User {username} created with ID {user_id}")
        return user_id
    
    def create_api_key(self, name: str, roles: List[str], 
                      expires_in_days: int = None) -> tuple[str, str]:
        """Create a new API key"""
        key_id = secrets.token_urlsafe(16)
        api_key = secrets.token_urlsafe(32)
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Convert role strings to Role enums
        key_roles = set()
        for role_str in roles:
            try:
                key_roles.add(Role(role_str))
            except ValueError:
                logger.warning(f"Invalid role: {role_str}")
        
        # Calculate permissions from roles
        permissions = set()
        for role in key_roles:
            permissions.update(self.role_permissions.get(role, set()))
        
        expires_at = None
        if expires_in_days:
            expires_at = datetime.now() + timedelta(days=expires_in_days)
        
        api_key_obj = APIKey(
            key_id=key_id,
            key_hash=key_hash,
            name=name,
            roles=key_roles,
            permissions=permissions,
            created_at=datetime.now(),
            expires_at=expires_at
        )
        
        self.api_keys[key_id] = api_key_obj
        logger.info(f"API key {name} created with ID {key_id}")
        return key_id, api_key
    
    def generate_jwt_token(self, user_id: str, additional_claims: Dict[str, Any] = None) -> str:
        """Generate JWT token for user"""
        user = self.users.get(user_id)
        if not user or not user.is_active:
            raise ValueError("Invalid or inactive user")
        
        # Update last login
        user.last_login = datetime.now()
        
        # Create token payload
        payload = {
            "user_id": user_id,
            "username": user.username,
            "roles": [role.value for role in user.roles],
            "permissions": [perm.value for perm in user.permissions],
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + self.jwt_expiration
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        # Sign token
        if self.jwt_algorithm == "RS256":
            token = jwt.encode(payload, self.private_key, algorithm=self.jwt_algorithm)
        else:
            token = jwt.encode(payload, self.secret_key, algorithm=self.jwt_algorithm)
        
        return token
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token"""
        try:
            if self.jwt_algorithm == "RS256":
                payload = jwt.decode(token, self.public_key, algorithms=[self.jwt_algorithm])
            else:
                payload = jwt.decode(token, self.secret_key, algorithms=[self.jwt_algorithm])
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid JWT token: {e}")
            return None
    
    def verify_api_key(self, api_key: str) -> Optional[APIKey]:
        """Verify API key and return associated key object"""
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        for key_obj in self.api_keys.values():
            if key_obj.key_hash == key_hash and key_obj.is_active:
                # Check expiration
                if key_obj.expires_at and datetime.now() > key_obj.expires_at:
                    logger.warning(f"API key {key_obj.key_id} expired")
                    return None
                
                # Update last used
                key_obj.last_used = datetime.now()
                return key_obj
        
        return None
    
    def check_permission(self, user_permissions: Set[Permission], 
                        required_permission: Permission) -> bool:
        """Check if user has required permission"""
        return required_permission in user_permissions or Permission.ADMIN in user_permissions
    
    def revoke_api_key(self, key_id: str) -> bool:
        """Revoke an API key"""
        if key_id in self.api_keys:
            self.api_keys[key_id].is_active = False
            logger.info(f"API key {key_id} revoked")
            return True
        return False
    
    def deactivate_user(self, user_id: str) -> bool:
        """Deactivate a user account"""
        if user_id in self.users:
            self.users[user_id].is_active = False
            logger.info(f"User {user_id} deactivated")
            return True
        return False
    
    def get_auth_stats(self) -> Dict[str, Any]:
        """Get authentication statistics"""
        active_users = sum(1 for user in self.users.values() if user.is_active)
        active_keys = sum(1 for key in self.api_keys.values() if key.is_active)
        
        return {
            "total_users": len(self.users),
            "active_users": active_users,
            "total_api_keys": len(self.api_keys),
            "active_api_keys": active_keys,
            "jwt_algorithm": self.jwt_algorithm,
            "timestamp": datetime.now().isoformat()
        }


class SecurityMiddleware:
    """Security middleware for request authentication and authorization"""
    
    def __init__(self, auth_manager: AuthenticationManager):
        self.auth_manager = auth_manager
    
    def authenticate_request(self, authorization_header: str) -> Optional[Dict[str, Any]]:
        """Authenticate request using Authorization header"""
        if not authorization_header:
            return None
        
        parts = authorization_header.split()
        if len(parts) != 2:
            return None
        
        auth_type, credentials = parts
        
        if auth_type.lower() == "bearer":
            # JWT token authentication
            payload = self.auth_manager.verify_jwt_token(credentials)
            if payload:
                return {
                    "type": "jwt",
                    "user_id": payload.get("user_id"),
                    "username": payload.get("username"),
                    "roles": payload.get("roles", []),
                    "permissions": payload.get("permissions", [])
                }
        
        elif auth_type.lower() == "apikey":
            # API key authentication
            api_key_obj = self.auth_manager.verify_api_key(credentials)
            if api_key_obj:
                return {
                    "type": "api_key",
                    "key_id": api_key_obj.key_id,
                    "name": api_key_obj.name,
                    "roles": [role.value for role in api_key_obj.roles],
                    "permissions": [perm.value for perm in api_key_obj.permissions]
                }
        
        return None
    
    def authorize_request(self, auth_info: Dict[str, Any], 
                         required_permission: str) -> bool:
        """Authorize request based on required permission"""
        if not auth_info:
            return False
        
        permissions = auth_info.get("permissions", [])
        return required_permission in permissions or "admin" in permissions


# Convenience functions
def create_auth_manager(secret_key: str = None) -> AuthenticationManager:
    """Create and return an AuthenticationManager instance"""
    return AuthenticationManager(secret_key)


def setup_default_auth() -> AuthenticationManager:
    """Set up authentication manager with default admin user and API key"""
    auth_manager = AuthenticationManager()
    
    # Create admin user
    admin_user_id = auth_manager.create_user(
        username="admin",
        email="<EMAIL>",
        roles=["admin"]
    )
    
    # Create admin API key
    admin_key_id, admin_api_key = auth_manager.create_api_key(
        name="admin-key",
        roles=["admin"]
    )
    
    logger.info(f"Default admin user created: {admin_user_id}")
    logger.info(f"Default admin API key created: {admin_key_id}")
    logger.info(f"Admin API key: {admin_api_key}")
    
    return auth_manager
