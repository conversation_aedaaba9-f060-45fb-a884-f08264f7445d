"""
IRONFORGE Agent Gateway
======================

Central gateway service for managing remote agent connections.
Provides WebSocket and REST API endpoints for agent communication.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from uuid import uuid4

try:
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
    from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

from ironforge.integration.ironforge_container import get_ironforge_container

logger = logging.getLogger(__name__)


class AgentConnection:
    """Represents a connected remote agent"""
    
    def __init__(self, agent_id: str, websocket: WebSocket, metadata: Dict[str, Any]):
        self.agent_id = agent_id
        self.websocket = websocket
        self.metadata = metadata
        self.connected_at = datetime.now()
        self.last_heartbeat = datetime.now()
        self.status = "connected"
        self.task_queue: List[Dict[str, Any]] = []
        
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """Send message to agent"""
        try:
            await self.websocket.send_text(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"Failed to send message to agent {self.agent_id}: {e}")
            self.status = "disconnected"
            return False
    
    def update_heartbeat(self):
        """Update last heartbeat timestamp"""
        self.last_heartbeat = datetime.now()
        
    def is_healthy(self, timeout_seconds: int = 60) -> bool:
        """Check if agent is healthy based on heartbeat"""
        return (datetime.now() - self.last_heartbeat).total_seconds() < timeout_seconds


class AgentGateway:
    """
    Central gateway for managing remote agent connections.
    Handles WebSocket connections, authentication, and message routing.
    """
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8001):
        self.host = host
        self.port = port
        self.app = None
        self.connections: Dict[str, AgentConnection] = {}
        self.security = HTTPBearer() if FASTAPI_AVAILABLE else None
        self.ironforge_container = get_ironforge_container()
        
        if FASTAPI_AVAILABLE:
            self._setup_fastapi_app()
        else:
            raise RuntimeError("FastAPI not available - cannot start Agent Gateway")
    
    def _setup_fastapi_app(self):
        """Setup FastAPI application with endpoints"""
        self.app = FastAPI(
            title="IRONFORGE Agent Gateway",
            description="Gateway service for remote IRONFORGE agents",
            version="1.0.0"
        )
        
        # WebSocket endpoint for agent connections
        @self.app.websocket("/ws/agent/{agent_id}")
        async def agent_websocket(websocket: WebSocket, agent_id: str):
            await self._handle_agent_connection(websocket, agent_id)
        
        # REST endpoints
        @self.app.post("/api/agents/register")
        async def register_agent(agent_data: Dict[str, Any], 
                               credentials: HTTPAuthorizationCredentials = Depends(self.security)):
            return await self._register_agent(agent_data, credentials)
        
        @self.app.get("/api/agents")
        async def list_agents(credentials: HTTPAuthorizationCredentials = Depends(self.security)):
            return await self._list_agents(credentials)
        
        @self.app.post("/api/agents/{agent_id}/tasks")
        async def assign_task(agent_id: str, task: Dict[str, Any],
                            credentials: HTTPAuthorizationCredentials = Depends(self.security)):
            return await self._assign_task(agent_id, task, credentials)
        
        @self.app.get("/api/health")
        async def health_check():
            return {
                "status": "healthy",
                "connected_agents": len(self.connections),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _handle_agent_connection(self, websocket: WebSocket, agent_id: str):
        """Handle WebSocket connection from remote agent"""
        await websocket.accept()
        logger.info(f"Agent {agent_id} connected")
        
        # Create connection object
        connection = AgentConnection(
            agent_id=agent_id,
            websocket=websocket,
            metadata={"connected_at": datetime.now().isoformat()}
        )
        self.connections[agent_id] = connection
        
        try:
            # Send welcome message
            await connection.send_message({
                "type": "welcome",
                "agent_id": agent_id,
                "timestamp": datetime.now().isoformat()
            })
            
            # Handle messages
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                await self._process_agent_message(agent_id, message)
                
        except WebSocketDisconnect:
            logger.info(f"Agent {agent_id} disconnected")
        except Exception as e:
            logger.error(f"Error handling agent {agent_id}: {e}")
        finally:
            # Clean up connection
            if agent_id in self.connections:
                del self.connections[agent_id]
    
    async def _process_agent_message(self, agent_id: str, message: Dict[str, Any]):
        """Process message from remote agent"""
        message_type = message.get("type")
        connection = self.connections.get(agent_id)
        
        if not connection:
            return
        
        if message_type == "heartbeat":
            connection.update_heartbeat()
            await connection.send_message({
                "type": "heartbeat_ack",
                "timestamp": datetime.now().isoformat()
            })
        
        elif message_type == "task_result":
            # Handle task completion
            task_id = message.get("task_id")
            result = message.get("result")
            logger.info(f"Agent {agent_id} completed task {task_id}")
            
            # Send acknowledgment
            await connection.send_message({
                "type": "task_ack",
                "task_id": task_id,
                "timestamp": datetime.now().isoformat()
            })
        
        elif message_type == "status_update":
            # Update agent status
            connection.status = message.get("status", "unknown")
            connection.metadata.update(message.get("metadata", {}))
    
    async def _register_agent(self, agent_data: Dict[str, Any], 
                            credentials: HTTPAuthorizationCredentials) -> Dict[str, Any]:
        """Register a new agent"""
        # TODO: Implement authentication validation
        agent_id = str(uuid4())
        
        return {
            "agent_id": agent_id,
            "status": "registered",
            "websocket_url": f"ws://{self.host}:{self.port}/ws/agent/{agent_id}",
            "timestamp": datetime.now().isoformat()
        }
    
    async def _list_agents(self, credentials: HTTPAuthorizationCredentials) -> Dict[str, Any]:
        """List all connected agents"""
        agents = []
        for agent_id, connection in self.connections.items():
            agents.append({
                "agent_id": agent_id,
                "status": connection.status,
                "connected_at": connection.connected_at.isoformat(),
                "last_heartbeat": connection.last_heartbeat.isoformat(),
                "is_healthy": connection.is_healthy(),
                "metadata": connection.metadata
            })
        
        return {
            "agents": agents,
            "total_count": len(agents),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _assign_task(self, agent_id: str, task: Dict[str, Any],
                         credentials: HTTPAuthorizationCredentials) -> Dict[str, Any]:
        """Assign task to specific agent"""
        connection = self.connections.get(agent_id)
        if not connection:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        if not connection.is_healthy():
            raise HTTPException(status_code=503, detail="Agent not healthy")
        
        task_id = str(uuid4())
        task_message = {
            "type": "task_assignment",
            "task_id": task_id,
            "task": task,
            "timestamp": datetime.now().isoformat()
        }
        
        success = await connection.send_message(task_message)
        if not success:
            raise HTTPException(status_code=503, detail="Failed to send task to agent")
        
        return {
            "task_id": task_id,
            "agent_id": agent_id,
            "status": "assigned",
            "timestamp": datetime.now().isoformat()
        }
    
    async def broadcast_message(self, message: Dict[str, Any], 
                              agent_filter: Optional[callable] = None):
        """Broadcast message to all or filtered agents"""
        for agent_id, connection in self.connections.items():
            if agent_filter and not agent_filter(connection):
                continue
            
            if connection.is_healthy():
                await connection.send_message(message)
    
    def start(self):
        """Start the Agent Gateway server"""
        logger.info(f"Starting IRONFORGE Agent Gateway on {self.host}:{self.port}")
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info"
        )
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        healthy_count = sum(1 for conn in self.connections.values() if conn.is_healthy())
        
        return {
            "total_connections": len(self.connections),
            "healthy_connections": healthy_count,
            "unhealthy_connections": len(self.connections) - healthy_count,
            "connection_details": [
                {
                    "agent_id": agent_id,
                    "status": conn.status,
                    "is_healthy": conn.is_healthy(),
                    "connected_duration": (datetime.now() - conn.connected_at).total_seconds()
                }
                for agent_id, conn in self.connections.items()
            ]
        }
