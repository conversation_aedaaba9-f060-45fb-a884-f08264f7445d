"""
IRONFORGE Service Discovery
==========================

Service discovery and configuration management for remote agents.
Handles service registration, health checks, and dynamic configuration.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)


class ServiceStatus(Enum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    MAINTENANCE = "maintenance"


@dataclass
class ServiceEndpoint:
    """Represents a service endpoint"""
    service_id: str
    service_type: str
    host: str
    port: int
    protocol: str = "http"
    path: str = "/"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def url(self) -> str:
        """Get the full URL for this endpoint"""
        return f"{self.protocol}://{self.host}:{self.port}{self.path}"


@dataclass
class ServiceRegistration:
    """Service registration information"""
    endpoint: ServiceEndpoint
    registered_at: datetime
    last_health_check: datetime
    status: ServiceStatus = ServiceStatus.UNKNOWN
    health_check_url: Optional[str] = None
    tags: Set[str] = field(default_factory=set)
    ttl: int = 300  # Time to live in seconds


class ServiceDiscovery:
    """
    Service discovery system for IRONFORGE remote agents.
    Manages service registration, health monitoring, and configuration distribution.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        self.services: Dict[str, ServiceRegistration] = {}
        self.config_file = Path(config_file) if config_file else None
        self.running = False
        
        # Configuration
        self.health_check_interval = 30  # seconds
        self.service_ttl_default = 300  # seconds
        self.cleanup_interval = 60  # seconds
        
        # Load initial configuration
        if self.config_file and self.config_file.exists():
            self._load_configuration()
    
    async def start(self):
        """Start the service discovery system"""
        self.running = True
        logger.info("Service Discovery started")
        
        # Start background tasks
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._cleanup_loop())
    
    async def stop(self):
        """Stop the service discovery system"""
        self.running = False
        logger.info("Service Discovery stopped")
    
    def register_service(self, service_id: str, service_type: str, 
                        host: str, port: int, **kwargs) -> bool:
        """Register a new service"""
        try:
            endpoint = ServiceEndpoint(
                service_id=service_id,
                service_type=service_type,
                host=host,
                port=port,
                protocol=kwargs.get("protocol", "http"),
                path=kwargs.get("path", "/"),
                metadata=kwargs.get("metadata", {})
            )
            
            registration = ServiceRegistration(
                endpoint=endpoint,
                registered_at=datetime.now(),
                last_health_check=datetime.now(),
                health_check_url=kwargs.get("health_check_url"),
                tags=set(kwargs.get("tags", [])),
                ttl=kwargs.get("ttl", self.service_ttl_default)
            )
            
            self.services[service_id] = registration
            logger.info(f"Service {service_id} registered at {endpoint.url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register service {service_id}: {e}")
            return False
    
    def unregister_service(self, service_id: str) -> bool:
        """Unregister a service"""
        if service_id in self.services:
            del self.services[service_id]
            logger.info(f"Service {service_id} unregistered")
            return True
        return False
    
    def discover_services(self, service_type: str = None, 
                         tags: Set[str] = None, 
                         status: ServiceStatus = ServiceStatus.HEALTHY) -> List[ServiceEndpoint]:
        """Discover services by type, tags, and status"""
        matching_services = []
        
        for registration in self.services.values():
            # Filter by service type
            if service_type and registration.endpoint.service_type != service_type:
                continue
            
            # Filter by tags
            if tags and not tags.issubset(registration.tags):
                continue
            
            # Filter by status
            if registration.status != status:
                continue
            
            matching_services.append(registration.endpoint)
        
        return matching_services
    
    def get_service(self, service_id: str) -> Optional[ServiceEndpoint]:
        """Get a specific service by ID"""
        registration = self.services.get(service_id)
        return registration.endpoint if registration else None
    
    def get_service_status(self, service_id: str) -> Optional[ServiceStatus]:
        """Get the status of a specific service"""
        registration = self.services.get(service_id)
        return registration.status if registration else None
    
    def update_service_status(self, service_id: str, status: ServiceStatus, 
                            metadata: Dict[str, Any] = None) -> bool:
        """Update the status of a service"""
        registration = self.services.get(service_id)
        if not registration:
            return False
        
        registration.status = status
        registration.last_health_check = datetime.now()
        
        if metadata:
            registration.endpoint.metadata.update(metadata)
        
        return True
    
    async def _health_check_loop(self):
        """Perform periodic health checks on registered services"""
        while self.running:
            try:
                for service_id, registration in self.services.items():
                    await self._check_service_health(service_id, registration)
                
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _check_service_health(self, service_id: str, registration: ServiceRegistration):
        """Check the health of a specific service"""
        try:
            # If no health check URL is provided, assume healthy if recently registered
            if not registration.health_check_url:
                time_since_registration = (datetime.now() - registration.registered_at).total_seconds()
                if time_since_registration < registration.ttl:
                    registration.status = ServiceStatus.HEALTHY
                else:
                    registration.status = ServiceStatus.UNKNOWN
                registration.last_health_check = datetime.now()
                return
            
            # Perform HTTP health check
            try:
                import aiohttp
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(registration.health_check_url, timeout=5) as response:
                        if response.status == 200:
                            registration.status = ServiceStatus.HEALTHY
                        else:
                            registration.status = ServiceStatus.UNHEALTHY
                            
            except ImportError:
                logger.warning("aiohttp not available for health checks")
                registration.status = ServiceStatus.UNKNOWN
            except Exception as e:
                logger.warning(f"Health check failed for {service_id}: {e}")
                registration.status = ServiceStatus.UNHEALTHY
            
            registration.last_health_check = datetime.now()
            
        except Exception as e:
            logger.error(f"Error checking health for service {service_id}: {e}")
            registration.status = ServiceStatus.UNKNOWN
    
    async def _cleanup_loop(self):
        """Clean up expired services"""
        while self.running:
            try:
                current_time = datetime.now()
                expired_services = []
                
                for service_id, registration in self.services.items():
                    time_since_check = (current_time - registration.last_health_check).total_seconds()
                    
                    if time_since_check > registration.ttl:
                        expired_services.append(service_id)
                
                for service_id in expired_services:
                    logger.info(f"Service {service_id} expired, removing from registry")
                    del self.services[service_id]
                
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    def _load_configuration(self):
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            # Load predefined services
            for service_config in config.get("services", []):
                self.register_service(**service_config)
            
            # Update configuration parameters
            if "health_check_interval" in config:
                self.health_check_interval = config["health_check_interval"]
            
            if "service_ttl_default" in config:
                self.service_ttl_default = config["service_ttl_default"]
            
            logger.info(f"Configuration loaded from {self.config_file}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
    
    def save_configuration(self):
        """Save current configuration to file"""
        if not self.config_file:
            return
        
        try:
            config = {
                "health_check_interval": self.health_check_interval,
                "service_ttl_default": self.service_ttl_default,
                "services": []
            }
            
            # Save registered services
            for registration in self.services.values():
                service_config = {
                    "service_id": registration.endpoint.service_id,
                    "service_type": registration.endpoint.service_type,
                    "host": registration.endpoint.host,
                    "port": registration.endpoint.port,
                    "protocol": registration.endpoint.protocol,
                    "path": registration.endpoint.path,
                    "metadata": registration.endpoint.metadata,
                    "health_check_url": registration.health_check_url,
                    "tags": list(registration.tags),
                    "ttl": registration.ttl
                }
                config["services"].append(service_config)
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def get_discovery_stats(self) -> Dict[str, Any]:
        """Get service discovery statistics"""
        status_counts = {}
        type_counts = {}
        
        for registration in self.services.values():
            # Count by status
            status = registration.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # Count by type
            service_type = registration.endpoint.service_type
            type_counts[service_type] = type_counts.get(service_type, 0) + 1
        
        return {
            "total_services": len(self.services),
            "by_status": status_counts,
            "by_type": type_counts,
            "last_updated": datetime.now().isoformat()
        }


# Convenience functions
def create_service_discovery(config_file: str = None) -> ServiceDiscovery:
    """Create and return a ServiceDiscovery instance"""
    return ServiceDiscovery(config_file)


async def start_service_discovery(config_file: str = None) -> ServiceDiscovery:
    """Create, start, and return a ServiceDiscovery instance"""
    discovery = ServiceDiscovery(config_file)
    await discovery.start()
    return discovery
