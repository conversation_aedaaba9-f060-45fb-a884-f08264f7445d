"""
IRONFOR<PERSON> Failover and Error Handling
=====================================

Robust error handling, recovery mechanisms, and failover strategies for remote agents.
Provides circuit breakers, retry policies, and automatic recovery systems.
"""

import asyncio
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit breaker tripped
    HALF_OPEN = "half_open"  # Testing if service recovered


class RetryStrategy(Enum):
    FIXED = "fixed"
    EXPONENTIAL = "exponential"
    LINEAR = "linear"


@dataclass
class RetryPolicy:
    """Configuration for retry behavior"""
    max_attempts: int = 3
    base_delay: float = 1.0  # seconds
    max_delay: float = 60.0  # seconds
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    backoff_multiplier: float = 2.0
    jitter: bool = True
    retryable_exceptions: List[type] = field(default_factory=lambda: [Exception])


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0  # seconds
    success_threshold: int = 3  # successes needed to close circuit
    timeout: float = 30.0  # seconds for operations


class CircuitBreaker:
    """Circuit breaker pattern implementation"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.next_attempt_time = None
    
    async def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                logger.info("Circuit breaker transitioning to HALF_OPEN")
            else:
                raise CircuitBreakerOpenError("Circuit breaker is OPEN")
        
        try:
            # Execute with timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            self._on_success()
            return result
            
        except Exception as e:
            self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset"""
        if self.last_failure_time is None:
            return True
        
        return (datetime.now() - self.last_failure_time).total_seconds() >= self.config.recovery_timeout
    
    def _on_success(self):
        """Handle successful operation"""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                self.success_count = 0
                logger.info("Circuit breaker CLOSED after successful recovery")
        else:
            self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed operation"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            self.success_count = 0
            logger.warning("Circuit breaker OPEN after failure in HALF_OPEN state")
        elif self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker OPEN after {self.failure_count} failures")
    
    def get_state(self) -> Dict[str, Any]:
        """Get current circuit breaker state"""
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time.isoformat() if self.last_failure_time else None
        }


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open"""
    pass


class RetryHandler:
    """Handles retry logic with configurable policies"""
    
    def __init__(self, policy: RetryPolicy):
        self.policy = policy
    
    async def execute_with_retry(self, func: Callable, *args, **kwargs):
        """Execute function with retry logic"""
        last_exception = None
        
        for attempt in range(self.policy.max_attempts):
            try:
                result = await func(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"Operation succeeded on attempt {attempt + 1}")
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if exception is retryable
                if not self._is_retryable_exception(e):
                    logger.error(f"Non-retryable exception: {e}")
                    raise
                
                # Don't retry on last attempt
                if attempt == self.policy.max_attempts - 1:
                    break
                
                # Calculate delay
                delay = self._calculate_delay(attempt)
                logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay:.2f}s")
                
                await asyncio.sleep(delay)
        
        # All attempts failed
        logger.error(f"All {self.policy.max_attempts} attempts failed. Last error: {last_exception}")
        raise last_exception
    
    def _is_retryable_exception(self, exception: Exception) -> bool:
        """Check if exception is retryable"""
        return any(isinstance(exception, exc_type) for exc_type in self.policy.retryable_exceptions)
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        if self.policy.strategy == RetryStrategy.FIXED:
            delay = self.policy.base_delay
        elif self.policy.strategy == RetryStrategy.LINEAR:
            delay = self.policy.base_delay * (attempt + 1)
        else:  # EXPONENTIAL
            delay = self.policy.base_delay * (self.policy.backoff_multiplier ** attempt)
        
        # Apply maximum delay
        delay = min(delay, self.policy.max_delay)
        
        # Add jitter if enabled
        if self.policy.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # 50-100% of calculated delay
        
        return delay


class FailoverManager:
    """Manages failover between multiple service instances"""
    
    def __init__(self, services: List[str], health_check_func: Callable = None):
        self.services = services
        self.health_check_func = health_check_func
        self.current_service_index = 0
        self.service_health = {service: True for service in services}
        self.circuit_breakers = {
            service: CircuitBreaker(CircuitBreakerConfig()) 
            for service in services
        }
        self.last_health_check = {}
    
    async def execute_with_failover(self, func: Callable, *args, **kwargs):
        """Execute function with automatic failover"""
        attempts = 0
        max_attempts = len(self.services) * 2  # Try each service twice
        
        while attempts < max_attempts:
            service = self._get_next_healthy_service()
            if not service:
                raise NoHealthyServicesError("No healthy services available")
            
            try:
                circuit_breaker = self.circuit_breakers[service]
                result = await circuit_breaker.call(func, service, *args, **kwargs)
                return result
                
            except (CircuitBreakerOpenError, Exception) as e:
                logger.warning(f"Service {service} failed: {e}")
                self.service_health[service] = False
                attempts += 1
                
                # Move to next service
                self._advance_to_next_service()
        
        raise AllServicesFailedError("All services failed")
    
    def _get_next_healthy_service(self) -> Optional[str]:
        """Get the next healthy service"""
        # First, try current service if healthy
        current_service = self.services[self.current_service_index]
        if self.service_health.get(current_service, False):
            return current_service
        
        # Find next healthy service
        for i in range(len(self.services)):
            service = self.services[i]
            if self.service_health.get(service, False):
                self.current_service_index = i
                return service
        
        return None
    
    def _advance_to_next_service(self):
        """Move to the next service in the list"""
        self.current_service_index = (self.current_service_index + 1) % len(self.services)
    
    async def health_check_all_services(self):
        """Perform health checks on all services"""
        if not self.health_check_func:
            return
        
        for service in self.services:
            try:
                is_healthy = await self.health_check_func(service)
                self.service_health[service] = is_healthy
                self.last_health_check[service] = datetime.now()
                
                if is_healthy:
                    logger.debug(f"Service {service} is healthy")
                else:
                    logger.warning(f"Service {service} is unhealthy")
                    
            except Exception as e:
                logger.error(f"Health check failed for service {service}: {e}")
                self.service_health[service] = False
    
    def get_failover_status(self) -> Dict[str, Any]:
        """Get current failover status"""
        return {
            "current_service": self.services[self.current_service_index],
            "service_health": self.service_health,
            "circuit_breaker_states": {
                service: cb.get_state() 
                for service, cb in self.circuit_breakers.items()
            },
            "last_health_checks": {
                service: check_time.isoformat() 
                for service, check_time in self.last_health_check.items()
            }
        }


class NoHealthyServicesError(Exception):
    """Exception raised when no healthy services are available"""
    pass


class AllServicesFailedError(Exception):
    """Exception raised when all services have failed"""
    pass


class ErrorRecoveryManager:
    """Manages error recovery and system resilience"""
    
    def __init__(self):
        self.recovery_strategies: Dict[type, Callable] = {}
        self.error_history: List[Dict[str, Any]] = []
        self.recovery_attempts: Dict[str, int] = {}
    
    def register_recovery_strategy(self, exception_type: type, recovery_func: Callable):
        """Register a recovery strategy for specific exception type"""
        self.recovery_strategies[exception_type] = recovery_func
        logger.info(f"Recovery strategy registered for {exception_type.__name__}")
    
    async def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> bool:
        """Handle error with appropriate recovery strategy"""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat(),
            "context": context or {}
        }
        self.error_history.append(error_info)
        
        # Find recovery strategy
        recovery_func = None
        for exc_type, func in self.recovery_strategies.items():
            if isinstance(error, exc_type):
                recovery_func = func
                break
        
        if not recovery_func:
            logger.error(f"No recovery strategy found for {type(error).__name__}")
            return False
        
        # Attempt recovery
        recovery_key = f"{type(error).__name__}_{context.get('service', 'unknown')}"
        attempt_count = self.recovery_attempts.get(recovery_key, 0) + 1
        self.recovery_attempts[recovery_key] = attempt_count
        
        try:
            logger.info(f"Attempting recovery for {type(error).__name__} (attempt {attempt_count})")
            success = await recovery_func(error, context)
            
            if success:
                logger.info(f"Recovery successful for {type(error).__name__}")
                self.recovery_attempts[recovery_key] = 0  # Reset on success
            else:
                logger.warning(f"Recovery failed for {type(error).__name__}")
            
            return success
            
        except Exception as recovery_error:
            logger.error(f"Recovery strategy failed: {recovery_error}")
            return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error and recovery statistics"""
        error_counts = {}
        for error in self.error_history[-100:]:  # Last 100 errors
            error_type = error["error_type"]
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        return {
            "total_errors": len(self.error_history),
            "recent_errors": len(self.error_history[-100:]),
            "error_types": error_counts,
            "recovery_attempts": dict(self.recovery_attempts),
            "timestamp": datetime.now().isoformat()
        }


# Decorators for easy integration
def with_retry(policy: RetryPolicy = None):
    """Decorator to add retry logic to functions"""
    if policy is None:
        policy = RetryPolicy()
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retry_handler = RetryHandler(policy)
            return await retry_handler.execute_with_retry(func, *args, **kwargs)
        return wrapper
    return decorator


def with_circuit_breaker(config: CircuitBreakerConfig = None):
    """Decorator to add circuit breaker protection to functions"""
    if config is None:
        config = CircuitBreakerConfig()
    
    circuit_breaker = CircuitBreaker(config)
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await circuit_breaker.call(func, *args, **kwargs)
        return wrapper
    return decorator


# Convenience functions
def create_failover_manager(services: List[str], health_check_func: Callable = None) -> FailoverManager:
    """Create and return a FailoverManager instance"""
    return FailoverManager(services, health_check_func)


def create_error_recovery_manager() -> ErrorRecoveryManager:
    """Create and return an ErrorRecoveryManager instance"""
    return ErrorRecoveryManager()
