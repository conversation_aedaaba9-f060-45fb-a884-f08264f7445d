"""
IRONFORGE Remote Agent Monitoring
=================================

Comprehensive monitoring, metrics collection, and health checking for remote agents.
Provides real-time insights into agent performance and system health.
"""

import asyncio
import json
import logging
import time
from collections import defaultdict, deque
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class MetricType(Enum):
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


class HealthStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class Metric:
    """Represents a system metric"""
    name: str
    metric_type: MetricType
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    description: str = ""


@dataclass
class HealthCheck:
    """Health check configuration and result"""
    name: str
    check_function: Callable
    interval: int = 30  # seconds
    timeout: int = 5  # seconds
    last_check: Optional[datetime] = None
    last_status: HealthStatus = HealthStatus.UNKNOWN
    last_error: Optional[str] = None
    consecutive_failures: int = 0
    max_failures: int = 3


class MetricsCollector:
    """Collects and manages system metrics"""
    
    def __init__(self, retention_hours: int = 24):
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.retention_hours = retention_hours
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = defaultdict(float)
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self.timers: Dict[str, List[float]] = defaultdict(list)
    
    def increment_counter(self, name: str, value: float = 1.0, labels: Dict[str, str] = None):
        """Increment a counter metric"""
        self.counters[name] += value
        self._record_metric(name, MetricType.COUNTER, value, labels)
    
    def set_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """Set a gauge metric"""
        self.gauges[name] = value
        self._record_metric(name, MetricType.GAUGE, value, labels)
    
    def record_histogram(self, name: str, value: float, labels: Dict[str, str] = None):
        """Record a histogram value"""
        self.histograms[name].append(value)
        self._record_metric(name, MetricType.HISTOGRAM, value, labels)
    
    def record_timer(self, name: str, duration: float, labels: Dict[str, str] = None):
        """Record a timer duration"""
        self.timers[name].append(duration)
        self._record_metric(name, MetricType.TIMER, duration, labels)
    
    def _record_metric(self, name: str, metric_type: MetricType, value: float, 
                      labels: Dict[str, str] = None):
        """Record a metric with timestamp"""
        metric = Metric(
            name=name,
            metric_type=metric_type,
            value=value,
            timestamp=datetime.now(),
            labels=labels or {}
        )
        self.metrics[name].append(metric)
    
    def get_metric_summary(self, name: str) -> Dict[str, Any]:
        """Get summary statistics for a metric"""
        if name not in self.metrics:
            return {}
        
        values = [m.value for m in self.metrics[name]]
        if not values:
            return {}
        
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "latest": values[-1],
            "timestamp": self.metrics[name][-1].timestamp.isoformat()
        }
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all current metric values"""
        return {
            "counters": dict(self.counters),
            "gauges": dict(self.gauges),
            "histograms": {k: self.get_metric_summary(k) for k in self.histograms.keys()},
            "timers": {k: self.get_metric_summary(k) for k in self.timers.keys()},
            "timestamp": datetime.now().isoformat()
        }
    
    def cleanup_old_metrics(self):
        """Remove old metrics beyond retention period"""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        for metric_name, metric_deque in self.metrics.items():
            # Remove old metrics
            while metric_deque and metric_deque[0].timestamp < cutoff_time:
                metric_deque.popleft()


class HealthMonitor:
    """Monitors system health through configurable health checks"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.health_checks: Dict[str, HealthCheck] = {}
        self.metrics_collector = metrics_collector
        self.running = False
    
    def register_health_check(self, name: str, check_function: Callable, 
                            interval: int = 30, timeout: int = 5, max_failures: int = 3):
        """Register a new health check"""
        health_check = HealthCheck(
            name=name,
            check_function=check_function,
            interval=interval,
            timeout=timeout,
            max_failures=max_failures
        )
        self.health_checks[name] = health_check
        logger.info(f"Health check '{name}' registered")
    
    async def start_monitoring(self):
        """Start health monitoring"""
        self.running = True
        logger.info("Health monitoring started")
        
        # Start health check tasks
        tasks = []
        for health_check in self.health_checks.values():
            task = asyncio.create_task(self._health_check_loop(health_check))
            tasks.append(task)
        
        # Wait for all tasks
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stop_monitoring(self):
        """Stop health monitoring"""
        self.running = False
        logger.info("Health monitoring stopped")
    
    async def _health_check_loop(self, health_check: HealthCheck):
        """Run health check loop for a specific check"""
        while self.running:
            try:
                await self._perform_health_check(health_check)
                await asyncio.sleep(health_check.interval)
            except Exception as e:
                logger.error(f"Error in health check loop for {health_check.name}: {e}")
                await asyncio.sleep(health_check.interval)
    
    async def _perform_health_check(self, health_check: HealthCheck):
        """Perform a single health check"""
        try:
            # Run health check with timeout
            start_time = time.time()
            result = await asyncio.wait_for(
                health_check.check_function(),
                timeout=health_check.timeout
            )
            duration = time.time() - start_time
            
            # Record timing metric
            self.metrics_collector.record_timer(
                f"health_check_duration_{health_check.name}",
                duration * 1000,  # Convert to milliseconds
                {"check_name": health_check.name}
            )
            
            # Update health check status
            if result:
                health_check.last_status = HealthStatus.HEALTHY
                health_check.consecutive_failures = 0
                health_check.last_error = None
            else:
                health_check.consecutive_failures += 1
                if health_check.consecutive_failures >= health_check.max_failures:
                    health_check.last_status = HealthStatus.UNHEALTHY
                else:
                    health_check.last_status = HealthStatus.DEGRADED
            
            health_check.last_check = datetime.now()
            
            # Record health status metric
            status_value = 1 if health_check.last_status == HealthStatus.HEALTHY else 0
            self.metrics_collector.set_gauge(
                f"health_check_status_{health_check.name}",
                status_value,
                {"check_name": health_check.name, "status": health_check.last_status.value}
            )
            
        except asyncio.TimeoutError:
            health_check.consecutive_failures += 1
            health_check.last_error = "Health check timeout"
            health_check.last_status = HealthStatus.DEGRADED if health_check.consecutive_failures < health_check.max_failures else HealthStatus.UNHEALTHY
            health_check.last_check = datetime.now()
            
        except Exception as e:
            health_check.consecutive_failures += 1
            health_check.last_error = str(e)
            health_check.last_status = HealthStatus.DEGRADED if health_check.consecutive_failures < health_check.max_failures else HealthStatus.UNHEALTHY
            health_check.last_check = datetime.now()
            logger.error(f"Health check {health_check.name} failed: {e}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status"""
        overall_status = HealthStatus.HEALTHY
        health_details = {}
        
        for name, health_check in self.health_checks.items():
            health_details[name] = {
                "status": health_check.last_status.value,
                "last_check": health_check.last_check.isoformat() if health_check.last_check else None,
                "consecutive_failures": health_check.consecutive_failures,
                "last_error": health_check.last_error
            }
            
            # Determine overall status
            if health_check.last_status == HealthStatus.UNHEALTHY:
                overall_status = HealthStatus.UNHEALTHY
            elif health_check.last_status == HealthStatus.DEGRADED and overall_status == HealthStatus.HEALTHY:
                overall_status = HealthStatus.DEGRADED
        
        return {
            "overall_status": overall_status.value,
            "checks": health_details,
            "timestamp": datetime.now().isoformat()
        }


class RemoteAgentMonitor:
    """Comprehensive monitoring system for remote agents"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.metrics_collector = MetricsCollector(
            retention_hours=self.config.get("retention_hours", 24)
        )
        self.health_monitor = HealthMonitor(self.metrics_collector)
        self.running = False
        
        # Agent-specific metrics
        self.agent_metrics = defaultdict(dict)
        self.task_metrics = defaultdict(dict)
        
        # Setup default health checks
        self._setup_default_health_checks()
    
    def _setup_default_health_checks(self):
        """Setup default health checks"""
        # System health checks
        self.health_monitor.register_health_check(
            "system_memory",
            self._check_system_memory,
            interval=30
        )
        
        self.health_monitor.register_health_check(
            "system_cpu",
            self._check_system_cpu,
            interval=30
        )
    
    async def _check_system_memory(self) -> bool:
        """Check system memory usage"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            self.metrics_collector.set_gauge("system_memory_percent", memory.percent)
            return memory.percent < 90  # Healthy if less than 90% memory usage
        except ImportError:
            return True  # Assume healthy if psutil not available
    
    async def _check_system_cpu(self) -> bool:
        """Check system CPU usage"""
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics_collector.set_gauge("system_cpu_percent", cpu_percent)
            return cpu_percent < 90  # Healthy if less than 90% CPU usage
        except ImportError:
            return True  # Assume healthy if psutil not available
    
    async def start(self):
        """Start the monitoring system"""
        self.running = True
        logger.info("Remote Agent Monitor started")
        
        # Start background tasks
        tasks = [
            asyncio.create_task(self.health_monitor.start_monitoring()),
            asyncio.create_task(self._metrics_cleanup_loop())
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stop(self):
        """Stop the monitoring system"""
        self.running = False
        await self.health_monitor.stop_monitoring()
        logger.info("Remote Agent Monitor stopped")
    
    async def _metrics_cleanup_loop(self):
        """Periodic cleanup of old metrics"""
        while self.running:
            try:
                self.metrics_collector.cleanup_old_metrics()
                await asyncio.sleep(3600)  # Cleanup every hour
            except Exception as e:
                logger.error(f"Error in metrics cleanup: {e}")
                await asyncio.sleep(3600)
    
    def record_agent_event(self, agent_id: str, event_type: str, metadata: Dict[str, Any] = None):
        """Record an agent event"""
        self.metrics_collector.increment_counter(
            f"agent_events_{event_type}",
            labels={"agent_id": agent_id}
        )
        
        # Update agent-specific metrics
        if agent_id not in self.agent_metrics:
            self.agent_metrics[agent_id] = {
                "events": defaultdict(int),
                "last_seen": datetime.now(),
                "metadata": {}
            }
        
        self.agent_metrics[agent_id]["events"][event_type] += 1
        self.agent_metrics[agent_id]["last_seen"] = datetime.now()
        if metadata:
            self.agent_metrics[agent_id]["metadata"].update(metadata)
    
    def record_task_event(self, task_id: str, event_type: str, duration: float = None, 
                         agent_id: str = None):
        """Record a task event"""
        labels = {"task_id": task_id}
        if agent_id:
            labels["agent_id"] = agent_id
        
        self.metrics_collector.increment_counter(
            f"task_events_{event_type}",
            labels=labels
        )
        
        if duration is not None:
            self.metrics_collector.record_timer(
                f"task_duration_{event_type}",
                duration,
                labels=labels
            )
    
    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive monitoring dashboard data"""
        return {
            "system_health": self.health_monitor.get_health_status(),
            "metrics": self.metrics_collector.get_all_metrics(),
            "agent_summary": {
                "total_agents": len(self.agent_metrics),
                "active_agents": sum(
                    1 for agent in self.agent_metrics.values()
                    if (datetime.now() - agent["last_seen"]).total_seconds() < 300
                ),
                "agent_details": {
                    agent_id: {
                        "last_seen": agent["last_seen"].isoformat(),
                        "event_counts": dict(agent["events"]),
                        "metadata": agent["metadata"]
                    }
                    for agent_id, agent in self.agent_metrics.items()
                }
            },
            "task_summary": dict(self.task_metrics),
            "timestamp": datetime.now().isoformat()
        }


# Convenience functions
def create_remote_agent_monitor(config: Dict[str, Any] = None) -> RemoteAgentMonitor:
    """Create and return a RemoteAgentMonitor instance"""
    return RemoteAgentMonitor(config)


async def start_monitoring(config: Dict[str, Any] = None) -> RemoteAgentMonitor:
    """Create, start, and return a RemoteAgentMonitor instance"""
    monitor = RemoteAgentMonitor(config)
    asyncio.create_task(monitor.start())
    return monitor
