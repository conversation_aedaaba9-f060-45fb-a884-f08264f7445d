"""
IRONFORGE Agent Orchestrator
===========================

Orchestrates remote agent lifecycle, task distribution, and resource management.
Manages agent registration, health monitoring, and automatic scaling.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from uuid import uuid4

from ironforge.integration.ironforge_container import get_ironforge_container

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    PENDING = "pending"
    ASSIGNED = "assigned"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentStatus(Enum):
    REGISTERING = "registering"
    AVAILABLE = "available"
    BUSY = "busy"
    UNHEALTHY = "unhealthy"
    DISCONNECTED = "disconnected"


@dataclass
class Task:
    """Represents a task to be executed by remote agents"""
    task_id: str
    task_type: str
    payload: Dict[str, Any]
    priority: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    assigned_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    assigned_agent: Optional[str] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class AgentInfo:
    """Information about a registered agent"""
    agent_id: str
    capabilities: List[str]
    status: AgentStatus
    registered_at: datetime
    last_heartbeat: datetime
    current_task: Optional[str] = None
    completed_tasks: int = 0
    failed_tasks: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


class AgentOrchestrator:
    """
    Orchestrates remote agent lifecycle and task distribution.
    Manages agent registration, health monitoring, and task scheduling.
    """
    
    def __init__(self, gateway_client=None):
        self.gateway_client = gateway_client
        self.agents: Dict[str, AgentInfo] = {}
        self.tasks: Dict[str, Task] = {}
        self.task_queue: List[str] = []  # Task IDs in priority order
        self.running = False
        self.ironforge_container = get_ironforge_container()
        
        # Configuration
        self.heartbeat_timeout = 60  # seconds
        self.task_timeout = 300  # seconds
        self.max_concurrent_tasks_per_agent = 1
        
    async def start(self):
        """Start the orchestrator"""
        self.running = True
        logger.info("Agent Orchestrator started")
        
        # Start background tasks
        asyncio.create_task(self._health_monitor_loop())
        asyncio.create_task(self._task_scheduler_loop())
        asyncio.create_task(self._cleanup_loop())
    
    async def stop(self):
        """Stop the orchestrator"""
        self.running = False
        logger.info("Agent Orchestrator stopped")
    
    def register_agent(self, agent_id: str, capabilities: List[str], 
                      metadata: Dict[str, Any] = None) -> bool:
        """Register a new agent"""
        if agent_id in self.agents:
            logger.warning(f"Agent {agent_id} already registered")
            return False
        
        agent_info = AgentInfo(
            agent_id=agent_id,
            capabilities=capabilities,
            status=AgentStatus.AVAILABLE,
            registered_at=datetime.now(),
            last_heartbeat=datetime.now(),
            metadata=metadata or {}
        )
        
        self.agents[agent_id] = agent_info
        logger.info(f"Agent {agent_id} registered with capabilities: {capabilities}")
        return True
    
    def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent"""
        if agent_id not in self.agents:
            return False
        
        agent = self.agents[agent_id]
        
        # Cancel any assigned tasks
        if agent.current_task:
            self._cancel_task(agent.current_task, "Agent unregistered")
        
        del self.agents[agent_id]
        logger.info(f"Agent {agent_id} unregistered")
        return True
    
    def update_agent_heartbeat(self, agent_id: str, metadata: Dict[str, Any] = None):
        """Update agent heartbeat"""
        if agent_id not in self.agents:
            return False
        
        agent = self.agents[agent_id]
        agent.last_heartbeat = datetime.now()
        
        if metadata:
            agent.metadata.update(metadata)
        
        # Update status if agent was unhealthy
        if agent.status == AgentStatus.UNHEALTHY:
            agent.status = AgentStatus.AVAILABLE
        
        return True
    
    def submit_task(self, task_type: str, payload: Dict[str, Any], 
                   priority: int = 0) -> str:
        """Submit a new task for execution"""
        task_id = str(uuid4())
        
        task = Task(
            task_id=task_id,
            task_type=task_type,
            payload=payload,
            priority=priority
        )
        
        self.tasks[task_id] = task
        
        # Insert into priority queue
        self._insert_task_by_priority(task_id)
        
        logger.info(f"Task {task_id} submitted with type {task_type}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific task"""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        return {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "status": task.status.value,
            "assigned_agent": task.assigned_agent,
            "created_at": task.created_at.isoformat(),
            "assigned_at": task.assigned_at.isoformat() if task.assigned_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "retry_count": task.retry_count,
            "error": task.error
        }
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or running task"""
        return self._cancel_task(task_id, "Cancelled by user")
    
    def _cancel_task(self, task_id: str, reason: str) -> bool:
        """Internal method to cancel a task"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            return False
        
        task.status = TaskStatus.CANCELLED
        task.error = reason
        
        # Remove from queue if pending
        if task_id in self.task_queue:
            self.task_queue.remove(task_id)
        
        # Update agent if task was assigned
        if task.assigned_agent:
            agent = self.agents.get(task.assigned_agent)
            if agent and agent.current_task == task_id:
                agent.current_task = None
                agent.status = AgentStatus.AVAILABLE
        
        logger.info(f"Task {task_id} cancelled: {reason}")
        return True
    
    def _insert_task_by_priority(self, task_id: str):
        """Insert task into queue by priority"""
        task = self.tasks[task_id]
        
        # Find insertion point (higher priority first)
        insert_index = 0
        for i, existing_task_id in enumerate(self.task_queue):
            existing_task = self.tasks[existing_task_id]
            if task.priority <= existing_task.priority:
                insert_index = i + 1
            else:
                break
        
        self.task_queue.insert(insert_index, task_id)
    
    async def _health_monitor_loop(self):
        """Monitor agent health and update status"""
        while self.running:
            try:
                current_time = datetime.now()
                
                for agent_id, agent in self.agents.items():
                    time_since_heartbeat = (current_time - agent.last_heartbeat).total_seconds()
                    
                    if time_since_heartbeat > self.heartbeat_timeout:
                        if agent.status != AgentStatus.UNHEALTHY:
                            logger.warning(f"Agent {agent_id} marked as unhealthy")
                            agent.status = AgentStatus.UNHEALTHY
                            
                            # Cancel current task if any
                            if agent.current_task:
                                self._cancel_task(agent.current_task, "Agent unhealthy")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in health monitor loop: {e}")
                await asyncio.sleep(10)
    
    async def _task_scheduler_loop(self):
        """Schedule tasks to available agents"""
        while self.running:
            try:
                if not self.task_queue:
                    await asyncio.sleep(1)
                    continue
                
                # Find available agents
                available_agents = [
                    agent for agent in self.agents.values()
                    if agent.status == AgentStatus.AVAILABLE
                ]
                
                if not available_agents:
                    await asyncio.sleep(1)
                    continue
                
                # Assign tasks to agents
                for task_id in self.task_queue[:]:
                    task = self.tasks[task_id]
                    
                    # Find suitable agent
                    suitable_agent = self._find_suitable_agent(task, available_agents)
                    if not suitable_agent:
                        continue
                    
                    # Assign task
                    success = await self._assign_task_to_agent(task, suitable_agent)
                    if success:
                        self.task_queue.remove(task_id)
                        available_agents.remove(suitable_agent)
                
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in task scheduler loop: {e}")
                await asyncio.sleep(1)
    
    def _find_suitable_agent(self, task: Task, available_agents: List[AgentInfo]) -> Optional[AgentInfo]:
        """Find the most suitable agent for a task"""
        suitable_agents = []
        
        for agent in available_agents:
            # Check if agent has required capabilities
            if task.task_type in agent.capabilities or "all" in agent.capabilities:
                suitable_agents.append(agent)
        
        if not suitable_agents:
            return None
        
        # Select agent with best performance (least failed tasks ratio)
        best_agent = min(suitable_agents, key=lambda a: a.failed_tasks / max(a.completed_tasks + a.failed_tasks, 1))
        return best_agent
    
    async def _assign_task_to_agent(self, task: Task, agent: AgentInfo) -> bool:
        """Assign a task to a specific agent"""
        try:
            # Update task status
            task.status = TaskStatus.ASSIGNED
            task.assigned_at = datetime.now()
            task.assigned_agent = agent.agent_id
            
            # Update agent status
            agent.status = AgentStatus.BUSY
            agent.current_task = task.task_id
            
            # Send task to agent via gateway
            if self.gateway_client:
                success = await self.gateway_client.assign_task(agent.agent_id, {
                    "task_id": task.task_id,
                    "task_type": task.task_type,
                    "payload": task.payload
                })
                
                if not success:
                    # Revert assignment
                    task.status = TaskStatus.PENDING
                    task.assigned_at = None
                    task.assigned_agent = None
                    agent.status = AgentStatus.AVAILABLE
                    agent.current_task = None
                    return False
            
            logger.info(f"Task {task.task_id} assigned to agent {agent.agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error assigning task {task.task_id} to agent {agent.agent_id}: {e}")
            return False
    
    async def _cleanup_loop(self):
        """Clean up completed and old tasks"""
        while self.running:
            try:
                current_time = datetime.now()
                cleanup_threshold = current_time - timedelta(hours=24)
                
                # Remove old completed/failed tasks
                tasks_to_remove = []
                for task_id, task in self.tasks.items():
                    if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                        task.completed_at and task.completed_at < cleanup_threshold):
                        tasks_to_remove.append(task_id)
                
                for task_id in tasks_to_remove:
                    del self.tasks[task_id]
                
                if tasks_to_remove:
                    logger.info(f"Cleaned up {len(tasks_to_remove)} old tasks")
                
                await asyncio.sleep(3600)  # Clean up every hour
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(3600)
    
    def get_orchestrator_stats(self) -> Dict[str, Any]:
        """Get orchestrator statistics"""
        task_stats = {}
        for status in TaskStatus:
            task_stats[status.value] = sum(1 for task in self.tasks.values() if task.status == status)
        
        agent_stats = {}
        for status in AgentStatus:
            agent_stats[status.value] = sum(1 for agent in self.agents.values() if agent.status == status)
        
        return {
            "agents": {
                "total": len(self.agents),
                "by_status": agent_stats
            },
            "tasks": {
                "total": len(self.tasks),
                "queued": len(self.task_queue),
                "by_status": task_stats
            },
            "timestamp": datetime.now().isoformat()
        }
