"""
IRONFORGE Remote Agent
=====================

Remote agent worker that connects to the Agent Gateway and executes IRONFORGE tasks.
Handles task execution, status reporting, and automatic reconnection.
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Callable
from uuid import uuid4

try:
    import websockets
    from websockets.exceptions import ConnectionClosed, WebSocketException
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False

from ironforge.integration.ironforge_container import get_ironforge_container

logger = logging.getLogger(__name__)


class TaskExecutor:
    """Executes different types of IRONFORGE tasks"""
    
    def __init__(self):
        self.ironforge_container = get_ironforge_container()
        self.task_handlers = {
            "discovery": self._execute_discovery_task,
            "confluence": self._execute_confluence_task,
            "validation": self._execute_validation_task,
            "analysis": self._execute_analysis_task
        }
    
    async def execute_task(self, task_type: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a task based on its type"""
        handler = self.task_handlers.get(task_type)
        if not handler:
            raise ValueError(f"Unknown task type: {task_type}")
        
        try:
            result = await handler(payload)
            return {
                "success": True,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _execute_discovery_task(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute temporal discovery task"""
        # Get discovery engine
        discovery_engine = self.ironforge_container.get_tgat_discovery()
        
        # Extract parameters
        shard_paths = payload.get("shard_paths", [])
        config = payload.get("config", {})
        
        # Execute discovery
        # Note: This would need to be adapted based on actual discovery API
        result = {
            "task_type": "discovery",
            "shard_count": len(shard_paths),
            "patterns_discovered": 0,  # Placeholder
            "execution_time": 0.0
        }
        
        return result
    
    async def _execute_confluence_task(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute confluence scoring task"""
        # Placeholder for confluence scoring
        result = {
            "task_type": "confluence",
            "sessions_scored": payload.get("session_count", 0),
            "average_score": 65.0,  # Placeholder
            "execution_time": 0.0
        }
        
        return result
    
    async def _execute_validation_task(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute validation task"""
        # Placeholder for validation
        result = {
            "task_type": "validation",
            "validation_passed": True,
            "metrics": {},
            "execution_time": 0.0
        }
        
        return result
    
    async def _execute_analysis_task(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute analysis task"""
        # Placeholder for analysis
        result = {
            "task_type": "analysis",
            "analysis_results": {},
            "execution_time": 0.0
        }

        return result

    async def _execute_augment_task(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Execute Augment AI assistant task"""
        operation_type = payload.get("operation_type")

        if operation_type == "file_operation":
            return await self._handle_file_operation(payload)
        elif operation_type == "shell_command":
            return await self._handle_shell_command(payload)
        elif operation_type == "git_operation":
            return await self._handle_git_operation(payload)
        elif operation_type == "code_execution":
            return await self._handle_code_execution(payload)
        else:
            raise ValueError(f"Unknown Augment operation type: {operation_type}")

    async def _handle_file_operation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file system operations for Augment"""
        from ironforge.services.augment_executor import AugmentFileExecutor

        executor = AugmentFileExecutor()
        return await executor.execute_file_operation(payload)

    async def _handle_shell_command(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle shell command execution for Augment"""
        from ironforge.services.augment_executor import AugmentShellExecutor

        executor = AugmentShellExecutor()
        return await executor.execute_shell_command(payload)

    async def _handle_git_operation(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle git operations for Augment"""
        from ironforge.services.augment_executor import AugmentGitExecutor

        executor = AugmentGitExecutor()
        return await executor.execute_git_operation(payload)

    async def _handle_code_execution(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle code execution for Augment"""
        from ironforge.services.augment_executor import AugmentCodeExecutor

        executor = AugmentCodeExecutor()
        return await executor.execute_code(payload)


class RemoteAgent:
    """
    Remote agent that connects to IRONFORGE Agent Gateway.
    Executes tasks and reports status back to the gateway.
    """
    
    def __init__(self, gateway_url: str, agent_id: str = None, 
                 capabilities: List[str] = None):
        if not WEBSOCKETS_AVAILABLE:
            raise RuntimeError("websockets library not available")
        
        self.gateway_url = gateway_url
        self.agent_id = agent_id or str(uuid4())
        self.capabilities = capabilities or ["discovery", "confluence", "validation", "analysis"]
        
        self.websocket = None
        self.running = False
        self.task_executor = TaskExecutor()
        self.current_task = None
        
        # Configuration
        self.heartbeat_interval = 30  # seconds
        self.reconnect_delay = 5  # seconds
        self.max_reconnect_attempts = 10
    
    async def start(self):
        """Start the remote agent"""
        self.running = True
        logger.info(f"Starting remote agent {self.agent_id}")
        
        # Start connection loop with reconnection
        await self._connection_loop()
    
    async def stop(self):
        """Stop the remote agent"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
        logger.info(f"Remote agent {self.agent_id} stopped")
    
    async def _connection_loop(self):
        """Main connection loop with automatic reconnection"""
        reconnect_attempts = 0
        
        while self.running and reconnect_attempts < self.max_reconnect_attempts:
            try:
                # Connect to gateway
                ws_url = f"{self.gateway_url}/ws/agent/{self.agent_id}"
                logger.info(f"Connecting to gateway: {ws_url}")
                
                async with websockets.connect(ws_url) as websocket:
                    self.websocket = websocket
                    reconnect_attempts = 0  # Reset on successful connection
                    
                    # Start heartbeat task
                    heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                    
                    try:
                        # Handle messages
                        await self._message_loop()
                    finally:
                        heartbeat_task.cancel()
                        
            except (ConnectionClosed, WebSocketException) as e:
                logger.warning(f"WebSocket connection lost: {e}")
                reconnect_attempts += 1
                
                if self.running and reconnect_attempts < self.max_reconnect_attempts:
                    logger.info(f"Reconnecting in {self.reconnect_delay} seconds... (attempt {reconnect_attempts})")
                    await asyncio.sleep(self.reconnect_delay)
                
            except Exception as e:
                logger.error(f"Unexpected error in connection loop: {e}")
                reconnect_attempts += 1
                await asyncio.sleep(self.reconnect_delay)
        
        if reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("Max reconnection attempts reached. Agent stopping.")
    
    async def _message_loop(self):
        """Handle incoming messages from gateway"""
        async for message in self.websocket:
            try:
                data = json.loads(message)
                await self._process_message(data)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON received: {e}")
            except Exception as e:
                logger.error(f"Error processing message: {e}")
    
    async def _process_message(self, message: Dict[str, Any]):
        """Process incoming message from gateway"""
        message_type = message.get("type")
        
        if message_type == "welcome":
            logger.info(f"Connected to gateway as agent {self.agent_id}")
            
            # Send agent registration info
            await self._send_message({
                "type": "agent_info",
                "capabilities": self.capabilities,
                "metadata": {
                    "version": "1.0.0",
                    "started_at": datetime.now().isoformat()
                }
            })
        
        elif message_type == "task_assignment":
            await self._handle_task_assignment(message)
        
        elif message_type == "heartbeat_ack":
            # Heartbeat acknowledged
            pass
        
        else:
            logger.warning(f"Unknown message type: {message_type}")
    
    async def _handle_task_assignment(self, message: Dict[str, Any]):
        """Handle task assignment from gateway"""
        task_id = message.get("task_id")
        task = message.get("task")
        
        if not task_id or not task:
            logger.error("Invalid task assignment message")
            return
        
        logger.info(f"Received task assignment: {task_id}")
        
        # Update status
        self.current_task = task_id
        await self._send_status_update("busy", {"current_task": task_id})
        
        try:
            # Execute task
            task_type = task.get("task_type")
            payload = task.get("payload", {})
            
            start_time = time.time()
            result = await self.task_executor.execute_task(task_type, payload)
            execution_time = time.time() - start_time
            
            # Add execution time to result
            result["execution_time"] = execution_time
            
            # Send result back to gateway
            await self._send_message({
                "type": "task_result",
                "task_id": task_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            logger.info(f"Task {task_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Task {task_id} failed: {e}")
            
            # Send error result
            await self._send_message({
                "type": "task_result",
                "task_id": task_id,
                "result": {
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            })
        
        finally:
            # Update status back to available
            self.current_task = None
            await self._send_status_update("available")
    
    async def _heartbeat_loop(self):
        """Send periodic heartbeats to gateway"""
        while self.running:
            try:
                await self._send_message({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat(),
                    "status": "busy" if self.current_task else "available"
                })
                
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Error sending heartbeat: {e}")
                break
    
    async def _send_message(self, message: Dict[str, Any]):
        """Send message to gateway"""
        if self.websocket:
            await self.websocket.send(json.dumps(message))
    
    async def _send_status_update(self, status: str, metadata: Dict[str, Any] = None):
        """Send status update to gateway"""
        await self._send_message({
            "type": "status_update",
            "status": status,
            "metadata": metadata or {},
            "timestamp": datetime.now().isoformat()
        })


# Convenience function to create and start an agent
async def start_remote_agent(gateway_url: str, agent_id: str = None, 
                           capabilities: List[str] = None):
    """Start a remote agent with the given configuration"""
    agent = RemoteAgent(gateway_url, agent_id, capabilities)
    await agent.start()
    return agent


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python remote_agent.py <gateway_url> [agent_id]")
        sys.exit(1)
    
    gateway_url = sys.argv[1]
    agent_id = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Start agent
    asyncio.run(start_remote_agent(gateway_url, agent_id))
