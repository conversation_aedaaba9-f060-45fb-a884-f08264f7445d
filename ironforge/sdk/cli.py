from __future__ import annotations

import argparse
import contextlib
import importlib
import json
import warnings
import sys
from pathlib import Path
from typing import Optional

import pandas as pd

from ironforge.reporting.minidash import build_minidash

from .app_config import load_config, materialize_run_dir, validate_config
from .io import glob_many, write_json
from .oracle_commands import cmd_audit_oracle, cmd_train_oracle
from ..utils.common import maybe_import, get_legacy_entrypoint


# _maybe function moved to ironforge.utils.common.maybe_import


def _extract_m1_from_enhanced_session(session_data: dict, session_name: str) -> Optional[pd.DataFrame]:
    """
    Extract M1-level events from enhanced session price movements and liquidity events
    Creates SMPE (Session Market Price Event) format for M1 processing
    Uses session metadata to compute absolute timestamps from relative time offsets
    """
    import datetime
    import pytz
    
    m1_events = []
    
    # Get session metadata for proper timestamp conversion
    session_metadata = session_data.get("session_metadata", {})
    session_date = session_metadata.get("session_date", "2025-08-07")  # fallback
    session_start_time = session_metadata.get("session_start", "00:00:00")
    timezone = session_metadata.get("timezone", "ET")
    
    # Parse session start datetime with timezone
    try:
        if timezone == "ET":
            tz = pytz.timezone("US/Eastern")
        else:
            tz = pytz.timezone(timezone)
    except:
        tz = pytz.timezone("US/Eastern")  # fallback
    
    # Create session start datetime
    session_start_dt = datetime.datetime.strptime(
        f"{session_date} {session_start_time}", "%Y-%m-%d %H:%M:%S"
    )
    session_start_dt = tz.localize(session_start_dt)
    session_start_ns = int(session_start_dt.timestamp() * 1_000_000_000)
    
    # Extract price movements as M1 events
    price_movements = session_data.get("price_movements", [])
    for i, movement in enumerate(price_movements):
        timestamp = movement.get("timestamp", "")
        price_level = movement.get("price_level")
        if timestamp and price_level is not None:
            # Convert timestamp to nanoseconds
            try:
                # Parse HH:MM:SS format and convert to absolute nanoseconds
                time_parts = timestamp.split(":")
                hours = int(time_parts[0])
                minutes = int(time_parts[1]) 
                seconds = int(time_parts[2])
                offset_seconds = hours * 3600 + minutes * 60 + seconds
                
                # Compute absolute timestamp: session start + offset
                ts_ns = session_start_ns + (offset_seconds * 1_000_000_000)
                
                m1_events.append({
                    'session_id': session_name,
                    'event_id': f"{session_name}_pm_{i}",
                    'event_type': movement.get("movement_type", "price_movement"),
                    'ts_ns': ts_ns,
                    'price': float(price_level),
                    'start_ts_ns': ts_ns,
                    'end_ts_ns': ts_ns,
                    'price_low': float(price_level),
                    'price_high': float(price_level),
                    'attrs': json.dumps(movement)
                })
            except (ValueError, IndexError, KeyError) as e:
                continue  # Skip malformed timestamps
    
    # Extract session liquidity events
    liquidity_events = session_data.get("session_liquidity_events", [])
    for i, event in enumerate(liquidity_events):
        timestamp = event.get("timestamp", "")
        if timestamp:
            try:
                # Parse HH:MM:SS format and convert to absolute nanoseconds
                time_parts = timestamp.split(":")
                hours = int(time_parts[0])
                minutes = int(time_parts[1])
                seconds = int(time_parts[2])
                offset_seconds = hours * 3600 + minutes * 60 + seconds
                
                # Compute absolute timestamp: session start + offset
                ts_ns = session_start_ns + (offset_seconds * 1_000_000_000)
                
                # Use intensity as price if available, otherwise use a default
                price = event.get("intensity", 1.0)
                if event.get("price_level"):
                    price = float(event["price_level"])
                    
                m1_events.append({
                    'session_id': session_name,
                    'event_id': f"{session_name}_liq_{i}",
                    'event_type': event.get("event_type", "liquidity_event"),
                    'ts_ns': ts_ns,
                    'price': price,
                    'start_ts_ns': ts_ns,
                    'end_ts_ns': ts_ns + event.get("impact_duration", 1) * 60 * 1_000_000_000,  # Duration in minutes to ns
                    'price_low': price,
                    'price_high': price,
                    'attrs': json.dumps(event)
                })
            except (ValueError, IndexError, KeyError) as e:
                continue
                
    if not m1_events:
        return None
        
    # Create DataFrame with SMPE schema
    df = pd.DataFrame(m1_events)
    
    # Sort by timestamp to ensure monotonicity
    df = df.sort_values('ts_ns').reset_index(drop=True)
    
    # Assert NO required OHLC columns in SMPE format
    ohlc_columns = ['open', 'high', 'low', 'close']
    assert not any(col in df.columns for col in ohlc_columns), f"SMPE schema should not contain OHLC columns: {[col for col in ohlc_columns if col in df.columns]}"
    
    # Assert required schema elements
    required_smpe_columns = ['session_id', 'event_id', 'event_type', 'ts_ns', 'price']
    missing_columns = [col for col in required_smpe_columns if col not in df.columns]
    assert not missing_columns, f"Missing required SMPE columns: {missing_columns}"
    
    # Assert ts_ns is int64 and monotonic non-decreasing
    assert df['ts_ns'].dtype == 'int64' or pd.api.types.is_integer_dtype(df['ts_ns']), "ts_ns must be int64"
    if len(df) > 1:
        # Use non-decreasing check (allows equal timestamps)
        ts_diff = df['ts_ns'].diff().dropna()
        assert (ts_diff >= 0).all(), "ts_ns must be monotonic non-decreasing"
    
    return df


def cmd_discover(cfg):
    # Canonical entrypoint
    fn = maybe_import("ironforge.learning.discovery_pipeline", "run_discovery")
    if fn is None:
        # Legacy fallbacks
        legacy_paths = [
            "ironforge.learning.tgat_discovery",
            "ironforge.discovery.runner"
        ]
        fn = get_legacy_entrypoint(
            legacy_paths, 
            "run_discovery", 
            "ironforge.learning.discovery_pipeline"
        )
        if fn is None:
            print("[discover] discovery engine not found; skipping (no-op).")
            return 0
    return int(bool(fn(cfg)))


def cmd_score(cfg):
    # Canonical entrypoint
    fn = maybe_import("ironforge.confluence.scoring", "score_confluence")
    if fn is None:
        # Legacy fallbacks
        legacy_paths = [
            "ironforge.confluence.scorer",
            "ironforge.metrics.confluence"
        ]
        legacy = get_legacy_entrypoint(
            legacy_paths, 
            "score_session", 
            "ironforge.confluence.scoring"
        )
        if legacy is None:
            print("[score] scorer not found; skipping (no-op).")
            return 0
        legacy(cfg)
        return 0
    fn(cfg)
    return 0


def cmd_validate(cfg):
    fn = maybe_import("ironforge.validation.runner", "validate_run")
    if fn is None:
        print("[validate] validation rails not found; skipping (no-op).")
        return 0
    res = fn(cfg)
    run_dir = materialize_run_dir(cfg) / "reports"
    run_dir.mkdir(parents=True, exist_ok=True)
    write_json(run_dir / "validation.json", res if isinstance(res, dict) else {"result": "ok"})
    return 0


def _load_first_parquet(paths: list[Path], cols: list[str]) -> pd.DataFrame:
    if not paths:
        return pd.DataFrame(columns=cols)
    try:
        df = pd.read_parquet(paths[0])
        return df
    except Exception:
        return pd.DataFrame(columns=cols)


def cmd_report(cfg):
    run_dir = materialize_run_dir(cfg)
    conf_paths = glob_many(str(run_dir / "confluence" / "*.parquet"))
    conf = _load_first_parquet(conf_paths, ["ts", "score"])
    if conf.empty:
        conf = pd.DataFrame(
            {
                "ts": pd.date_range("2025-01-01", periods=50, freq="T"),
                "score": [min(99, i * 2 % 100) for i in range(50)],
            }
        )
    pat_paths = glob_many(str(run_dir / "patterns" / "*.parquet"))
    act = _load_first_parquet(pat_paths, ["ts", "count"])
    if act.empty:
        g = conf.groupby(conf["ts"].astype("datetime64[m]")).size().reset_index(name="count")
        g.rename(columns={g.columns[0]: "ts"}, inplace=True)
        act = g
    motifs = []
    for j in Path(run_dir / "motifs").glob("*.json"):
        with contextlib.suppress(Exception):
            motifs.extend(json.loads(j.read_text(encoding="utf-8")))
    if not motifs:
        motifs = [{"name": "sweep→fvg", "support": 12, "ppv": 0.61}]
    out_html = run_dir / cfg.reporting.minidash.out_html
    out_png = run_dir / cfg.reporting.minidash.out_png
    build_minidash(
        act,
        conf,
        motifs,
        out_html,
        out_png,
        width=cfg.reporting.minidash.width,
        height=cfg.reporting.minidash.height,
    )
    print(f"[report] wrote {out_html} and {out_png}")
    # Optional manifest writer (env-gated, backward-compatible)
    import os as _os
    if _os.getenv("IRONFORGE_WRITE_MANIFEST") == "1":
        try:
            import ironforge as _pkg
            from . import manifest as _mf

            _mf.write_for_run(
                run_dir=str(run_dir),
                window_bars=512,  # default; use helper script for richer manifests
                version=getattr(_pkg, "__version__", "unknown"),
            )
        except Exception as e:  # pragma: no cover
            warnings.warn(f"Manifest write failed: {e}", RuntimeWarning, stacklevel=2)
    return 0


# Oracle commands moved to oracle_commands.py module


def cmd_prep_shards(
    source_glob: str,
    symbol: str,
    timeframe: str,
    timezone: str,
    pack_mode: str,
    dry_run: bool,
    overwrite: bool,
    htf_context: bool,
):
    """Prepare Parquet shards from enhanced JSON sessions."""
    try:
        from ironforge.converters.json_to_parquet import ConversionConfig, convert_enhanced_sessions

        config = ConversionConfig(
            source_glob=source_glob,
            symbol=symbol,
            timeframe=timeframe,
            source_timezone=timezone,
            pack_mode=pack_mode,
            dry_run=dry_run,
            overwrite=overwrite,
            htf_context_enabled=htf_context,
        )

        print(f"[prep-shards] Converting sessions from {source_glob}")
        print(
            f"[prep-shards] Target: {symbol}_{timeframe} | Timezone: {timezone} | Pack: {pack_mode}"
        )
        print(
            f"[prep-shards] HTF Context: {'ENABLED (51D features)' if htf_context else 'DISABLED (45D features)'}"
        )

        if dry_run:
            print("[prep-shards] DRY RUN MODE - no files will be written")

        shard_dirs = convert_enhanced_sessions(config)

        print(f"[prep-shards] ✅ Processed {len(shard_dirs)} sessions")

        # Write manifest
        if not dry_run and shard_dirs:
            manifest_path = Path(f"data/shards/{symbol}_{timeframe}/manifest.jsonl")
            manifest_path.parent.mkdir(parents=True, exist_ok=True)

            with open(manifest_path, "w") as f:
                for shard_dir in shard_dirs:
                    if shard_dir.exists():
                        meta_file = shard_dir / "meta.json"
                        if meta_file.exists():
                            with open(meta_file, "r") as meta_f:
                                metadata = json.load(meta_f)
                                manifest_entry = {
                                    "shard_dir": str(shard_dir),
                                    "session_id": metadata.get("session_id"),
                                    "node_count": metadata.get("node_count", 0),
                                    "edge_count": metadata.get("edge_count", 0),
                                    "conversion_timestamp": metadata.get("conversion_timestamp"),
                                }
                                f.write(json.dumps(manifest_entry) + "\n")

            print(f"[prep-shards] Wrote manifest: {manifest_path}")

        return 0

    except ImportError as e:
        print(f"[prep-shards] Error: Converter not available - {e}")
        return 1
    except Exception as e:
        print(f"[prep-shards] Error: {e}")
        return 1


def cmd_build_graph(args):
    """Build comprehensive dual graph views with full configuration support."""
    try:
        import glob
        import json
        import logging
        from pathlib import Path
        from ironforge.learning.dual_graph_config import load_config_with_overrides
        from ironforge.learning.dag_graph_builder import DAGGraphBuilder
        from ironforge.learning.dag_motif_miner import DAGMotifMiner
        from ironforge.learning.builder_doctor import (
            BuilderDiagnostics, 
            save_build_diagnostics,
            validate_session_schema
        )
        
        # Ensure pandas is available for timestamp operations
        import pandas as pd
        
        # Configure logging for verbose mode
        if args.verbose:
            logging.basicConfig(level=logging.DEBUG, format='%(name)s - %(levelname)s - %(message)s')
        else:
            logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')
        
        # Build comprehensive configuration
        overrides = {}
        
        # Apply quick configuration flags
        if args.with_dag:
            overrides['dag.enabled'] = True
        if args.with_m1:
            overrides['m1.enabled'] = True
        if args.enhanced_tgat:
            overrides['tgat.enhanced'] = True
        if args.enable_motifs:
            overrides['motifs.min_frequency'] = 2  # Enable motif discovery
            
        # Apply legacy DAG options for backwards compatibility
        if args.dag_k:
            overrides['dag.k_successors'] = args.dag_k
        if args.dag_dt_min:
            overrides['dag.dt_min_minutes'] = args.dag_dt_min
        if args.dag_dt_max:
            overrides['dag.dt_max_minutes'] = args.dag_dt_max
            
        # Apply JSON configuration overrides
        if args.config_overrides:
            try:
                json_overrides = json.loads(args.config_overrides)
                overrides.update(json_overrides)
            except json.JSONDecodeError as e:
                print(f"[build-graph] Error: Invalid JSON in config overrides - {e}")
                return 1
        
        # Load final configuration
        config = load_config_with_overrides(
            base_config_path=args.config,
            preset=args.preset,
            overrides=overrides
        )
        
        # Display configuration summary
        print(f"[build-graph] 🚀 IRONFORGE Dual Graph Views v{config.version}")
        print(f"[build-graph] Configuration: {args.preset} preset")
        print(f"[build-graph] DAG Construction: {'✅' if config.dag.enabled else '❌'}")
        print(f"[build-graph] M1 Integration: {'✅' if config.m1.enabled else '❌'}")  
        print(f"[build-graph] M1 Source: {args.m1_source} {'(force extraction)' if args.m1_source == 'extract' else '(prefer files)'}")
        print(f"[build-graph] Enhanced TGAT: {'✅' if config.tgat.enhanced else '❌'}")
        print(f"[build-graph] Motif Mining: {'✅' if config.motifs.min_frequency <= 3 else '❌'}")
        print(f"[build-graph] Processing sessions from {args.source_glob}")
        print(f"[build-graph] Output directory: {args.output_dir}")
        print(f"[build-graph] Output format: {args.format}")
        
        if args.dry_run:
            print("[build-graph] 🔍 DRY RUN MODE - no files will be written")
        
        # Initialize components based on configuration
        output_path = Path(args.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # DAG builder with comprehensive configuration 
        dag_config = {
            'k_successors': config.dag.k_successors,
            'dt_min_minutes': config.dag.dt_min_minutes,
            'dt_max_minutes': config.dag.dt_max_minutes,
            'enabled': config.dag.enabled,
            'predicate': config.dag.predicate,
            'm1_integration': config.m1.enabled
        }
        builder = DAGGraphBuilder(dag_config=dag_config)
        
        # Motif miner if enabled
        motif_miner = None
        if config.motifs.min_frequency <= 5:  # Reasonable threshold for enabled
            from ironforge.learning.dag_motif_miner import MotifConfig
            motif_config = MotifConfig(
                min_nodes=config.motifs.min_nodes,
                max_nodes=config.motifs.max_nodes,
                min_frequency=config.motifs.min_frequency,
                null_iterations=config.motifs.null_iterations,
                significance_threshold=config.motifs.significance_threshold
            )
            motif_miner = DAGMotifMiner(motif_config)
            print(f"[build-graph] 🔍 Motif mining enabled: {config.motifs.null_iterations} null iterations")
        
        # Find and process session files
        session_files = glob.glob(args.source_glob)
        if args.max_sessions:
            session_files = session_files[:args.max_sessions]
        
        # Apply debug-first limit if specified
        if args.debug_first:
            session_files = session_files[:args.debug_first]
            print(f"[build-graph] 🔍 DEBUG MODE: Processing first {len(session_files)} sessions only")
        
        print(f"[build-graph] Found {len(session_files)} session files")
        
        if not session_files:
            print("[build-graph] ❌ No session files found")
            return 1
        
        # Setup run directory with custom run-id support
        if args.run_id:
            run_id = args.run_id
        else:
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            run_id = f"RUN_{timestamp}"
        
        run_dir = Path("runs") / run_id
        print(f"[build-graph] Run ID: {run_id}")
        print(f"[build-graph] Run directory: {run_dir}")
        
        # Storage for motif mining (collect all DAGs)
        all_dags = []
        all_session_names = []
        
        # Session tracking for comprehensive reporting
        processed_count = 0
        successful_count = 0
        failed_sessions = []
        session_diagnostics = []
        for i, session_file in enumerate(session_files):
            session_path = Path(session_file)
            session_name = session_path.stem
            diagnostics = BuilderDiagnostics()
            diagnostics.session_id = session_name
            
            try:
                print(f"[build-graph] Processing {i+1}/{len(session_files)}: {session_name}")
                
                # Load session data
                with open(session_path, 'r') as f:
                    session_data = json.load(f)
                
                # Validate session schema (debug mode shows normalized events)
                if args.debug_first or args.verbose:
                    schema_validation = validate_session_schema(session_data, debug_first=True)
                    diagnostics.schema_validation = schema_validation
                    if not schema_validation["valid"]:
                        diagnostics.failure_reason = f"Schema validation failed: {schema_validation['errors']}"
                        raise ValueError(f"Session schema validation failed: {schema_validation['errors']}")
                
                # Count raw events (try multiple field names)
                events = None
                for field_name in ["events", "price_movements", "session_events", "data"]:
                    if field_name in session_data and isinstance(session_data[field_name], list):
                        events = session_data[field_name]
                        break
                
                if events is None:
                    events = []
                diagnostics.raw_events = len(events)
                
                # Load M1 data if M1 integration is enabled
                m1_data = None
                if config.m1.enabled:
                    if args.m1_source == "extract":
                        # Force extraction from enhanced session JSON
                        print(f"[build-graph] M1 source=extract: extracting from enhanced JSON")
                        m1_data = _extract_m1_from_enhanced_session(session_data, session_name)
                        if m1_data is not None:
                            print(f"[build-graph] Extracted M1 data from enhanced session: {len(m1_data)} events")
                        else:
                            print(f"[build-graph] ❌ Failed to extract M1 data from enhanced session")
                    else:
                        # Default behavior: prefer file, fallback to extraction
                        m1_file_pattern = session_path.parent / f"{session_name}_M1.parquet"
                        if m1_file_pattern.exists():
                            import pandas as pd
                            m1_data = pd.read_parquet(m1_file_pattern)
                            print(f"[build-graph] Loaded M1 data from file: {len(m1_data)} bars")
                        else:
                            # Fallback: extract M1 data from enhanced session
                            print(f"[build-graph] M1 file not found, extracting from enhanced session")
                            m1_data = _extract_m1_from_enhanced_session(session_data, session_name)
                            if m1_data is not None:
                                print(f"[build-graph] Extracted M1 data from enhanced session: {len(m1_data)} events")
                
                # Build graphs with comprehensive diagnostics
                if config.dag.enabled:
                    # Always use standard dual view graphs (DAG + temporal)
                    temporal_graph, dag_graph = builder.build_dual_view_graphs(session_data)
                    
                    # Update diagnostics
                    diagnostics.nodes = temporal_graph.number_of_nodes()
                    diagnostics.edges = dag_graph.number_of_edges() if dag_graph else 0
                    if m1_data is not None:
                        diagnostics.m1_nodes = len(m1_data)
                    
                    # Collect DAG for motif mining
                    if motif_miner and dag_graph and dag_graph.number_of_nodes() > 0:
                        all_dags.append(dag_graph)
                        all_session_names.append(session_name)
                        
                else:
                    # Temporal graph only
                    temporal_graph = builder.build_session_graph(session_data)
                    dag_graph = None
                    diagnostics.nodes = temporal_graph.number_of_nodes()
                    diagnostics.edges = temporal_graph.number_of_edges()
                
                if not args.dry_run:
                    # Create output directory
                    session_output_dir = output_path / session_name
                    session_output_dir.mkdir(parents=True, exist_ok=True)
                    
                    # STRICT JSON→Parquet→Graph pipeline (no pickle)
                    # Save graphs in Parquet format only
                    if args.format in ['parquet', 'both'] or True:  # Force Parquet
                        # DAG edges to optimized parquet 
                        if dag_graph and dag_graph.number_of_edges() > 0:
                            dag_edges_path = session_output_dir / 'edges_dag.parquet'
                            builder.save_dag_edges_parquet(dag_graph, dag_edges_path, session_name)
                            print(f"[build-graph] Saved DAG edges: {dag_edges_path} ({dag_graph.number_of_edges()} edges)")
                        elif dag_graph:
                            print(f"[build-graph] ⚠️  DAG created but has 0 edges (nodes: {dag_graph.number_of_nodes()})")
                        
                        # Save M1-specific artifacts if M1 integration is enabled
                        if config.m1.enabled and m1_data is not None:
                            # Save M1 events as nodes_m1_events.parquet (using SMPE format directly)
                            m1_events_path = session_output_dir / 'nodes_m1_events.parquet'
                            m1_data.to_parquet(
                                m1_events_path,
                                compression='zstd',
                                row_group_size=5000,
                                engine='pyarrow'
                            )
                            print(f"[build-graph] Saved M1 events: {m1_events_path}")
                            
                            # Build and save cross-scale edges
                            # Convert SMPE format to M1Event objects for cross-scale building
                            try:
                                from ironforge.learning.m1_event_detector import M1Event
                                from ironforge.learning.cross_scale_edge_builder import CrossScaleEdgeBuilder
                                
                                m1_events = []
                                for _, row in m1_data.iterrows():
                                    m1_event = M1Event(
                                        event_id=row['event_id'],
                                        session_id=row['session_id'],
                                        timestamp_ms=int(row['ts_ns'] // 1_000_000),  # Convert ns to ms
                                        parent_m5_seq_idx=-1,  # Will be computed in cross-scale builder
                                        event_kind=row['event_type'],
                                        price=row['price'],
                                        volume=1.0,  # Default volume
                                        features={},
                                        confidence=1.0,
                                        metadata={}
                                    )
                                    m1_events.append(m1_event)
                                
                                # Build cross-scale edges (use empty M5 bars for now)
                                cross_scale_builder = CrossScaleEdgeBuilder()
                                m5_bars = pd.DataFrame()  # Empty for now - will be enhanced later
                                cross_scale_edges = cross_scale_builder.build_cross_scale_edges(
                                    m1_events, m5_bars, session_name
                                )
                                
                                if cross_scale_edges:
                                    cross_scale_edges_path = session_output_dir / 'edges_cross_scale.parquet'
                                    cross_scale_builder.save_cross_scale_edges_parquet(
                                        cross_scale_edges, cross_scale_edges_path
                                    )
                                    print(f"[build-graph] Saved cross-scale edges: {cross_scale_edges_path}")
                                
                            except ImportError as e:
                                print(f"[build-graph] ⚠️ Cross-scale edge building failed (import): {e}")
                            except Exception as e:
                                print(f"[build-graph] ⚠️ Cross-scale edge building failed: {e}")
                    
                    # Save comprehensive metadata
                    metadata = {
                        'session_name': session_name,
                        'temporal_nodes': temporal_graph.number_of_nodes(),
                        'temporal_edges': temporal_graph.number_of_edges(),
                        'dag_enabled': config.dag.enabled,
                        'dag_nodes': dag_graph.number_of_nodes() if dag_graph else 0,
                        'dag_edges': dag_graph.number_of_edges() if dag_graph else 0,
                        'm1_enabled': config.m1.enabled,
                        'm1_events_detected': len(m1_data) if m1_data is not None else 0,
                        'enhanced_tgat': config.tgat.enhanced,
                        'configuration_preset': args.preset,
                        'build_timestamp': pd.Timestamp.now().isoformat(),
                        'feature_dimensions': config.tgat.input_dim
                    }
                    
                    metadata_path = session_output_dir / 'metadata.json'
                    with open(metadata_path, 'w') as f:
                        json.dump(metadata, f, indent=2)
                
                # Mark session as successful
                successful_count += 1
                
                # Save diagnostics
                if not args.dry_run:
                    save_build_diagnostics(run_dir, session_name, diagnostics)
                
                session_diagnostics.append(diagnostics)
                
                # Show per-session summary in verbose/debug mode
                if args.verbose or args.debug_first:
                    print(f"  ✅ {session_name}: {diagnostics.raw_events} events → {diagnostics.nodes} nodes → {diagnostics.edges} edges")
                
                processed_count += 1
                
            except Exception as e:
                # Record failure
                diagnostics.failure_reason = str(e)
                failed_sessions.append({
                    "session": session_name,
                    "error": str(e)
                })
                session_diagnostics.append(diagnostics)
                
                # Save failure diagnostics
                if not args.dry_run:
                    save_build_diagnostics(run_dir, session_name, diagnostics)
                
                print(f"[build-graph] ❌ Error processing {session_file}: {e}")
                if args.verbose:
                    import traceback
                    traceback.print_exc()
                    
                processed_count += 1
                
                # In strict mode, exit immediately on any failure
                if args.strict:
                    print(f"[build-graph] 💥 STRICT MODE: Exiting due to session failure")
                    return 1
        
        # Run motif mining if enabled
        if motif_miner and all_dags and not args.dry_run:
            print(f"[build-graph] 🔍 Mining motifs from {len(all_dags)} DAGs...")
            
            try:
                motifs = motif_miner.mine_motifs(all_dags, all_session_names)
                
                # Save motif results
                motifs_dir = output_path / 'motifs'
                motifs_dir.mkdir(exist_ok=True)
                
                motif_results = []
                for motif in motifs:
                    motif_result = {
                        'motif_id': motif.motif_id,
                        'frequency': motif.frequency,
                        'lift': motif.lift,
                        'p_value': motif.p_value,
                        'classification': motif.classification,
                        'sessions_found': list(motif.sessions_found),
                        'confidence_interval': motif.confidence_interval
                    }
                    motif_results.append(motif_result)
                
                motifs_summary_path = motifs_dir / 'motifs_summary.json'
                with open(motifs_summary_path, 'w') as f:
                    json.dump(motif_results, f, indent=2)
                
                promote_count = len([m for m in motifs if m.classification == 'PROMOTE'])
                park_count = len([m for m in motifs if m.classification == 'PARK'])
                
                print(f"[build-graph] 📊 Motif mining complete: {promote_count} PROMOTE, {park_count} PARK patterns")
                
            except Exception as e:
                print(f"[build-graph] ⚠️ Motif mining failed: {e}")
        
        # Save configuration if requested
        if args.save_config and not args.dry_run:
            config_output_path = output_path / 'dual_graph_config.json'
            config.save_to_file(config_output_path)
            print(f"[build-graph] 💾 Configuration saved: {config_output_path}")
        
        # Write comprehensive build manifest
        if not args.dry_run:
            manifest_path = output_path / 'build_manifest.jsonl'
            session_dirs = [d for d in output_path.iterdir() if d.is_dir() and d.name != 'motifs']
            
            with open(manifest_path, 'w') as f:
                for session_dir in session_dirs:
                    metadata_file = session_dir / 'metadata.json'
                    if metadata_file.exists():
                        with open(metadata_file, 'r') as meta_f:
                            metadata = json.load(meta_f)
                            f.write(json.dumps(metadata) + '\n')
            
            print(f"[build-graph] 📋 Build manifest: {manifest_path}")
        
        # Comprehensive final reporting
        print(f"\n{'='*60}")
        print(f"🚀 DUAL GRAPH VIEWS BUILD COMPLETE")
        print(f"{'='*60}")
        print(f"Sessions Total: {len(session_files)}")
        print(f"Sessions Successful: {successful_count}")
        print(f"Sessions Failed: {len(failed_sessions)}")
        
        if failed_sessions:
            print(f"🔴 FAILED SESSIONS:")
            for failure in failed_sessions:
                print(f"  ❌ {failure['session']}: {failure['error']}")
        
        # Count successful sessions with nodes/edges > 0
        graphs_with_data = sum(1 for d in session_diagnostics if d.nodes > 0 and d.edges > 0)
        print(f"Graphs with nodes>0 and edges>0: {graphs_with_data}")
        
        print(f"DAG Construction: {'✅' if config.dag.enabled else '❌'}")
        print(f"M1 Integration: {'✅' if config.m1.enabled else '❌'}")
        print(f"Enhanced TGAT: {'✅' if config.tgat.enhanced else '❌'}")
        if motif_miner and all_dags:
            print(f"Motifs Discovered: {len(motifs) if 'motifs' in locals() else 'N/A'}")
        print(f"Output Directory: {output_path}")
        print(f"Run ID: {run_id}")
        print(f"Configuration: {args.preset} preset")
        print(f"{'='*60}")
        
        # Display diagnostic paths for successful sessions
        if not args.dry_run and session_diagnostics:
            print(f"\n📋 DIAGNOSTIC FILES:")
            for diag in session_diagnostics[:3]:  # Show first 3
                if diag.nodes > 0:
                    diag_path = run_dir / "graphs" / diag.session_id / "build_diagnostics.json"
                    print(f"  {diag_path}")
            if len(session_diagnostics) > 3:
                print(f"  ... and {len(session_diagnostics) - 3} more in {run_dir}/graphs/")
        
        # Return appropriate exit code
        if args.strict and failed_sessions:
            print(f"🔴 STRICT MODE: Exiting with failure code due to {len(failed_sessions)} failed sessions")
            return 1
        elif failed_sessions:
            print(f"⚠️ Build completed with {len(failed_sessions)} failures (use --strict to exit with error)")
        
        return 0
        
    except ImportError as e:
        print(f"[build-graph] ❌ Error: Required components not available - {e}")
        return 1
    except Exception as e:
        print(f"[build-graph] ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1


def cmd_doctor(cfg=None):
    """Comprehensive system compatibility checks for IRONFORGE"""
    print("🔬 IRONFORGE Doctor - System Compatibility Check")
    print("=" * 50)
    
    issues = []
    warnings_count = 0
    
    # PyTorch version and device check
    try:
        import torch
        pytorch_version = torch.__version__
        print(f"✅ PyTorch version: {pytorch_version}")
        
        # Check if version supports SDPA (≥2.0)
        major, minor = map(int, pytorch_version.split('.')[:2])
        if major >= 2:
            print("✅ PyTorch version supports SDPA (≥2.0)")
        else:
            issues.append(f"❌ PyTorch {pytorch_version} < 2.0 - SDPA not available")
            
        # Check CUDA availability 
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name()}")
        else:
            print("⚠️  CUDA not available - using CPU")
            warnings_count += 1
            
    except ImportError:
        issues.append("❌ PyTorch not installed")
    
    # SDPA availability check
    try:
        from torch.nn.functional import scaled_dot_product_attention as sdpa
        print("✅ SDPA (scaled_dot_product_attention) available")
        
        # Test SDPA functionality
        q = torch.randn(1, 4, 10, 11)
        k = torch.randn(1, 4, 10, 11) 
        v = torch.randn(1, 4, 10, 11)
        
        output = sdpa(q, k, v)
        if output.shape == (1, 4, 10, 11):
            print("✅ SDPA functional test passed")
        else:
            issues.append(f"❌ SDPA test failed - output shape {output.shape}")
            
    except ImportError:
        issues.append("❌ SDPA not available - requires PyTorch ≥2.0")
    except Exception as e:
        issues.append(f"❌ SDPA test failed: {e}")
    
    # Parquet codec checks (ZSTD)
    try:
        import pyarrow.parquet as pq
        print("✅ PyArrow available")
        
        # Test ZSTD compression
        try:
            import pyarrow as pa
            
            # Create test data
            test_data = pa.table({'x': [1, 2, 3], 'y': [4, 5, 6]})
            
            # Test ZSTD compression
            import io
            buf = io.BytesIO()
            pq.write_table(test_data, buf, compression='zstd')
            buf.seek(0)
            
            # Read back
            table_read = pq.read_table(buf)
            if len(table_read) == 3:
                print("✅ ZSTD compression test passed")
            else:
                issues.append("❌ ZSTD compression test failed - data corruption")
                
        except Exception as e:
            issues.append(f"❌ ZSTD compression not available: {e}")
            
    except ImportError:
        issues.append("❌ PyArrow not installed")
    
    # NetworkX availability (for DAG operations)
    try:
        import networkx as nx
        print(f"✅ NetworkX available: {nx.__version__}")
        
        # Test DAG functionality
        dag = nx.DiGraph()
        dag.add_edges_from([(0, 1), (1, 2)])
        if nx.is_directed_acyclic_graph(dag):
            print("✅ DAG operations functional")
        else:
            issues.append("❌ DAG operations test failed")
            
    except ImportError:
        issues.append("❌ NetworkX not installed")
    
    # Context7 MCP connectivity (optional)
    try:
        # This is just a placeholder - real implementation would test MCP connection
        print("⚠️  Context7 MCP connectivity test skipped (optional)")
        warnings_count += 1
    except Exception:
        pass
    
    # Memory check
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        
        if available_gb >= 8.0:
            print(f"✅ Available memory: {available_gb:.1f}GB")
        elif available_gb >= 4.0:
            print(f"⚠️  Available memory: {available_gb:.1f}GB (recommended: 8GB+)")
            warnings_count += 1
        else:
            issues.append(f"❌ Low memory: {available_gb:.1f}GB (minimum: 4GB)")
            
    except ImportError:
        print("⚠️  psutil not available - memory check skipped")
        warnings_count += 1
    
    # TGAT attention implementation check
    try:
        from ironforge.learning.tgat_discovery import graph_attention
        q = torch.randn(1, 4, 5, 11)
        k = torch.randn(1, 4, 5, 11)
        v = torch.randn(1, 4, 5, 11)
        
        # Test both implementations
        out_sdpa, _ = graph_attention(q, k, v, impl="sdpa", training=False)
        out_manual, _ = graph_attention(q, k, v, impl="manual", training=False)
        
        if out_sdpa.shape == out_manual.shape:
            print("✅ TGAT attention implementations functional")
        else:
            issues.append("❌ TGAT attention test failed - shape mismatch")
            
    except ImportError:
        issues.append("❌ IRONFORGE TGAT module not available")
    except Exception as e:
        issues.append(f"❌ TGAT attention test failed: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    if issues:
        print(f"❌ Found {len(issues)} critical issues:")
        for issue in issues:
            print(f"  {issue}")
        print(f"\n⚠️  {warnings_count} warnings")
        return 1  # Exit code 1 for issues
    else:
        print("✅ All critical checks passed!")
        if warnings_count > 0:
            print(f"⚠️  {warnings_count} warnings (non-critical)")
        print("\nSystem ready for IRONFORGE operations.")
        return 0


def cmd_status(runs: Path):
    runs = Path(runs)
    if not runs.exists():
        print(json.dumps({"runs": []}, indent=2))
        return 0
    items = []
    for r in sorted([p for p in runs.iterdir() if p.is_dir()]):
        counts = {
            k: len(list((r / k).glob("**/*")))
            for k in ["embeddings", "patterns", "confluence", "motifs", "reports"]
        }
        items.append({"run": r.name, **counts})
    print(json.dumps({"runs": items}, indent=2))
    return 0


def main(argv: list[str] | None = None) -> int:
    p = argparse.ArgumentParser("ironforge")
    sub = p.add_subparsers(dest="cmd", required=True)
    c1 = sub.add_parser("discover-temporal")
    c1.add_argument("--config", default="configs/dev.yml")
    c2 = sub.add_parser("score-session")
    c2.add_argument("--config", default="configs/dev.yml")
    c3 = sub.add_parser("validate-run")
    c3.add_argument("--config", default="configs/dev.yml")
    c4 = sub.add_parser("report-minimal")
    c4.add_argument("--config", default="configs/dev.yml")
    c5 = sub.add_parser("status")
    c5.add_argument("--runs", default="runs")
    
    # System compatibility doctor command
    c_doctor = sub.add_parser("doctor", help="Run system compatibility checks")
    
    # Oracle audit command
    c_audit = sub.add_parser("audit-oracle", help="Audit Oracle training pipeline sessions")
    c_audit.add_argument("--symbols", required=True, help="Comma-separated symbols (e.g., NQ,ES)")
    c_audit.add_argument("--tf", required=True, help="Timeframe (e.g., 5 or M5)")
    c_audit.add_argument("--from", dest="from_date", required=True, help="Start date (YYYY-MM-DD)")
    c_audit.add_argument("--to", dest="to_date", required=True, help="End date (YYYY-MM-DD)")
    c_audit.add_argument("--data-dir", default="data/shards", help="Parquet shard data directory")
    c_audit.add_argument("--output", help="Output CSV ledger file")
    c_audit.add_argument("--min-sessions", type=int, default=57, help="Minimum required sessions")
    c_audit.add_argument("--verbose", action="store_true", help="Verbose logging")
    
    # Oracle training command
    c_oracle = sub.add_parser("train-oracle", help="Train Oracle temporal non-locality system")
    c_oracle.add_argument("--symbols", required=True, help="Comma-separated symbols (e.g., NQ,ES)")
    c_oracle.add_argument("--tf", required=True, help="Timeframe (e.g., 5 or M5)")
    c_oracle.add_argument("--from", dest="from_date", required=True, help="Start date (YYYY-MM-DD)")
    c_oracle.add_argument("--to", dest="to_date", required=True, help="End date (YYYY-MM-DD)")
    c_oracle.add_argument("--early-pct", type=float, default=0.20, help="Early batch percentage")
    c_oracle.add_argument("--htf-context", action="store_true", help="Enable HTF context (45D→51D)")
    c_oracle.add_argument("--no-htf-context", dest="htf_context", action="store_false", help="Disable HTF context")
    c_oracle.add_argument("--out", required=True, help="Output model directory")
    c_oracle.add_argument("--rebuild", action="store_true", help="Force rebuild embeddings")
    c_oracle.add_argument("--data-dir", default="data/shards", help="Parquet shard data directory")
    c_oracle.add_argument("--max-sessions", type=int, help="Limit training sessions")
    c_oracle.add_argument("--strict", action="store_true", help="Enable strict mode with audit validation")
    c_oracle.add_argument("--min-sessions", type=int, help="Minimum required sessions (strict mode)")
    c_oracle.set_defaults(htf_context=False)
    
    c6 = sub.add_parser("prep-shards")
    c6.add_argument(
        "--source-glob",
        default="data/enhanced/enhanced_*_Lvl-1_*.json",
        help="Glob pattern for enhanced JSON sessions",
    )
    c6.add_argument("--symbol", default="NQ", help="Symbol for shard directory")
    c6.add_argument("--timeframe", "--tf", default="M5", help="Timeframe for shard directory")
    c6.add_argument("--timezone", "--tz", default="ET", help="Source timezone")
    c6.add_argument(
        "--pack",
        choices=["single", "pack"],
        default="single",
        help="Packing mode: single session per shard or pack multiple",
    )
    c6.add_argument(
        "--dry-run", action="store_true", help="Show what would be converted without writing files"
    )
    c6.add_argument("--overwrite", action="store_true", help="Overwrite existing shards")
    c6.add_argument(
        "--htf-context", action="store_true", help="Enable HTF context features (45D → 51D)"
    )

    # Graph building command with comprehensive configuration
    c7 = sub.add_parser("build-graph", help="Build dual graph views (temporal + DAG) from session data")
    c7.add_argument(
        "--source-glob", 
        default="data/enhanced/enhanced_*_Lvl-1_*.json",
        help="Glob pattern for enhanced JSON sessions"
    )
    c7.add_argument("--output-dir", default="data/graphs", help="Output directory for graph files")
    
    # Configuration options
    c7.add_argument("--config", type=Path, help="Path to dual graph views configuration JSON file")
    c7.add_argument("--preset", choices=["minimal", "standard", "enhanced", "research"], 
                   default="standard", help="Configuration preset to use")
    c7.add_argument("--config-overrides", type=str, help="JSON string of configuration overrides")
    
    # Quick configuration flags (override config file)
    c7.add_argument("--with-dag", action="store_true", help="Enable DAG view construction")
    c7.add_argument("--with-m1", action="store_true", help="Enable M1 integration and cross-scale features")
    c7.add_argument("--enhanced-tgat", action="store_true", help="Enable enhanced TGAT with masked attention")
    c7.add_argument("--enable-motifs", action="store_true", help="Enable motif mining and statistical validation")
    
    # Legacy DAG options (for backwards compatibility)
    c7.add_argument("--dag-k", type=int, help="DAG k-successors per node")
    c7.add_argument("--dag-dt-min", type=int, help="DAG minimum time delta (minutes)")
    c7.add_argument("--dag-dt-max", type=int, help="DAG maximum time delta (minutes)")
    
    # Processing options
    c7.add_argument("--format", choices=["parquet"], default="parquet", 
                   help="Output format (parquet only - STRICT pipeline)")
    c7.add_argument("--dry-run", action="store_true", help="Show what would be built without writing files")
    c7.add_argument("--max-sessions", type=int, help="Limit number of sessions to process")
    c7.add_argument("--save-config", action="store_true", help="Save final configuration to output directory")
    
    # M1 source control
    c7.add_argument("--m1-source", choices=["extract", "file"], default="file", 
                   help="M1 data source: 'extract' from enhanced JSON or 'file' from parquet (default)")
    
    # Debug and validation options
    c7.add_argument("--debug-first", type=int, metavar="N", help="Process first N sessions only with debug output")
    c7.add_argument("--verbose", action="store_true", help="Enable verbose logging and diagnostics")
    c7.add_argument("--strict", action="store_true", help="Exit with nonzero code if any session fails")
    c7.add_argument("--run-id", type=str, help="Custom run ID for output directory naming")

    args = p.parse_args(argv)
    if args.cmd == "status":
        return cmd_status(Path(args.runs))
    if args.cmd == "doctor":
        return cmd_doctor()
    if args.cmd == "audit-oracle":
        return cmd_audit_oracle(
            symbols=args.symbols.split(","),
            timeframe=args.tf,
            from_date=args.from_date,
            to_date=args.to_date,
            data_dir=args.data_dir,
            output_file=args.output,
            min_sessions=args.min_sessions,
            verbose=args.verbose,
        )
    if args.cmd == "prep-shards":
        return cmd_prep_shards(
            args.source_glob,
            args.symbol,
            args.timeframe,
            args.timezone,
            args.pack,
            args.dry_run,
            args.overwrite,
            args.htf_context,
        )
    if args.cmd == "build-graph":
        return cmd_build_graph(args)
    if args.cmd == "train-oracle":
        return cmd_train_oracle(
            symbols=args.symbols.split(","),
            timeframe=args.tf,
            from_date=args.from_date,
            to_date=args.to_date,
            early_pct=args.early_pct,
            htf_context=args.htf_context,
            output_dir=args.out,
            rebuild=args.rebuild,
            data_dir=args.data_dir,
            max_sessions=args.max_sessions,
            strict_mode=args.strict,
            min_sessions=args.min_sessions,
        )
    cfg = load_config(args.config)
    try:
        validate_config(cfg)
    except Exception as e:
        print(f"[config] invalid configuration: {e}")
        return 2
    if args.cmd == "discover-temporal":
        return cmd_discover(cfg)
    if args.cmd == "score-session":
        return cmd_score(cfg)
    if args.cmd == "validate-run":
        return cmd_validate(cfg)
    if args.cmd == "report-minimal":
        return cmd_report(cfg)
    return 0


if __name__ == "__main__":
    sys.exit(main())
