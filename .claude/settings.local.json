{"permissions": {"allow": ["Bash(gh auth:*)", "Bash(gh repo create:*)", "Bash(git remote add:*)", "Bash(git push:*)", "Bash(gh repo view:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "Bash(git add:*)", "Bash(git pull:*)", "Bash(pip install:*)", "Bash(grep:*)", "Bash(gh pr view --json number,headRepository)", "Bash(find:*)", "Bash(gh pr view:*)", "Bash(git checkout:*)", "Bash(git stash push:*)", "Bash(git stash:*)", "<PERSON><PERSON>(git clean:*)", "Bash(git rm:*)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 -c \"import ironforge; print(''✅ IRONFORGE import successful'')\")", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(pip uninstall:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "<PERSON><PERSON>(nvim:*)", "Bash(pgrep:*)", "Bash(kill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./index-project quick --verbose)", "Bash(./index-project summary --quiet)", "Bash(./index-project:*)", "Bash(ruff check:*)", "Bash(black:*)", "Bash(pre-commit:*)", "Bash(git reset:*)", "Bash(git restore:*)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH pytest tests/unit/fpfvg/test_validators.py::test_safe_float -v)", "<PERSON><PERSON>(mypy:*)", "<PERSON><PERSON>(gh pr edit:*)", "Bash(git merge:*)", "<PERSON><PERSON>(true)", "Bash(gh api:*)", "Bash(git ls-tree:*)", "Bash(git tag:*)"], "deny": [], "ask": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/private/tmp/ironforge_complete_archaeology"]}, "outputStyle": "Explanatory"}