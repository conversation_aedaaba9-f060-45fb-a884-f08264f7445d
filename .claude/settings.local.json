{"permissions": {"allow": ["Bash(gh auth:*)", "Bash(gh repo create:*)", "Bash(git remote add:*)", "Bash(git push:*)", "Bash(gh repo view:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "Bash(git add:*)", "Bash(git pull:*)", "Bash(pip install:*)", "Bash(grep:*)", "Bash(gh pr view --json number,headRepository)", "Bash(find:*)", "Bash(gh pr view:*)", "Bash(git checkout:*)", "Bash(git stash push:*)", "Bash(git stash:*)", "<PERSON><PERSON>(git clean:*)", "Bash(git rm:*)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 -c \"import ironforge; print(''✅ IRONFORGE import successful'')\")", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(pip uninstall:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "<PERSON><PERSON>(nvim:*)", "Bash(pgrep:*)", "Bash(kill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./index-project quick --verbose)", "Bash(./index-project summary --quiet)", "Bash(./index-project:*)", "Bash(ruff check:*)", "Bash(black:*)", "Bash(pre-commit:*)", "Bash(git reset:*)", "Bash(git restore:*)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH pytest tests/unit/fpfvg/test_validators.py::test_safe_float -v)", "<PERSON><PERSON>(mypy:*)", "<PERSON><PERSON>(gh pr edit:*)", "Bash(git merge:*)", "<PERSON><PERSON>(true)", "Bash(gh api:*)", "Bash(git ls-tree:*)", "Bash(git tag:*)", "Bash(git branch:*)", "Bash(gh pr create:*)", "Bash(claude mcp add git npx @modelcontextprotocol/server-git /Users/<USER>/IRONFORGE)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__filesystem__list_directory", "Bash(git commit:*)", "mcp__filesystem__directory_tree", "<PERSON><PERSON>(tmux:*)", "Bash(echo $TMUX)", "<PERSON><PERSON>(git clone:*)", "mcp__filesystem__read_text_file", "mcp__filesystem__search_files", "WebSearch", "Bash(npx @modelcontextprotocol/server-git:*)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 real_gauntlet_detector.py)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 enhanced_tqe_gauntlet_queries.py)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 corrected_fpfvg_validation.py)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 comprehensive_gauntlet_analysis.py)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 enhanced_session_gauntlet_detector.py)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 gauntlet_resonance_framework.py)", "Bash(export PATH=\"$HOME/.npm-global/bin:$PATH\")", "Bash(export CHAT_CMD=\"claude chat\")", "Bash(./setup.sh)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 temporal_non_locality_hypothesis_h1.py)", "Read(//Users/<USER>/**)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(echo:*)", "Bash(ironforge ingest-json:*)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 -m ironforge.sdk.cli ingest-json --run $RUN_ID --last 180)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 -m ironforge.sdk.cli --help)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 -m ironforge.sdk.cli build-graph --help)", "Bash(PYTHONPATH=/Users/<USER>/IRONFORGE:$PYTHONPATH python3 -m ironforge.sdk.cli build-graph --preset standard --with-dag --with-m1 --format parquet --run-id $RUN_ID --verbose)", "Bash(export RUN_ID=RUN_20250826_191138_DUALGRAPH)"], "deny": [], "ask": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/private/tmp/ironforge_complete_archaeology"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["context7"], "outputStyle": "Explanatory"}