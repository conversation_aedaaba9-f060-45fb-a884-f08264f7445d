{"ironforge_semantic_index": {"version": "1.0.0", "generated_at": "2025-08-17 21:51:24 UTC", "analysis_metadata": {"analysis_timestamp": 1755467484.281683, "analysis_duration": 8.08215594291687, "indexer_version": "1.0.0", "files_analyzed": 162}}, "project_overview": {"name": "IRONFORGE", "architecture_type": "Multi-Engine Archaeological Discovery System", "total_files": 162, "total_lines": 50977, "total_functions": 286, "total_classes": 192, "average_complexity": 5.82, "key_technologies": ["TGAT", "PyTorch", "NetworkX", "NumPy", "iron-core"], "analysis_scope": ["__init__.py", "config.py", "data_migration/batch_migrate_graphs.py", "data_migration/schema_normalizer.py", "extract_lattice_summary.py", "iron_core/__init__.py", "iron_core/integration/__init__.py", "iron_core/mathematical/__init__.py", "iron_core/mathematical/adaptive_rg_optimizer.py", "iron_core/mathematical/cascade_classifier.py"]}, "engine_architecture": {"analysis": {"description": "Pattern analysis and session adaptation components", "metrics": {"file_count": 62, "total_lines": 24778, "avg_complexity": 42.79}, "key_components": [{"name": "extract_lattice_summary", "file": "extract_lattice_summary.py", "primary_classes": [], "complexity": 19}, {"name": "temporal_correlator", "file": "iron_core/mathematical/temporal_correlator.py", "primary_classes": ["CorrelationResult", "TemporalCorrelationEngine", "SequencePatternAnalyzer", "HTFEvent", "HTFIntensity", "HTFMasterController", "CascadeType", "<PERSON><PERSON>E<PERSON>"], "complexity": 57}, {"name": "__init__", "file": "ironforge/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "__init__", "file": "ironforge/analysis/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "broad_spectrum_archaeology", "file": "ironforge/analysis/broad_spectrum_archaeology.py", "primary_classes": ["BroadSpectrumArchaeology"], "complexity": 3}], "public_interfaces": [{"name": "CorrelationResult", "file": "iron_core/mathematical/temporal_correlator.py", "description": "Result from temporal correlation analysis..."}, {"name": "TemporalCorrelationEngine", "file": "iron_core/mathematical/temporal_correlator.py", "description": "Engine for correlating predictions with validation data across sequences..."}, {"name": "SequencePatternAnalyzer", "file": "iron_core/mathematical/temporal_correlator.py", "description": "Analyzer for detecting patterns in cascade sequences..."}]}, "learning": {"description": "Machine learning, TGAT discovery, and graph building", "metrics": {"file_count": 10, "total_lines": 3059, "avg_complexity": 35.9}, "key_components": [{"name": "contracts", "file": "ironforge/contracts.py", "primary_classes": ["DiscoveryResult"], "complexity": 0}, {"name": "__init__", "file": "ironforge/graph_builder/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "igraph_builder", "file": "ironforge/graph_builder/igraph_builder.py", "primary_classes": [], "complexity": 1}, {"name": "pyg_converters", "file": "ironforge/graph_builder/pyg_converters.py", "primary_classes": [], "complexity": 1}, {"name": "__init__", "file": "ironforge/learning/__init__.py", "primary_classes": [], "complexity": 0}], "public_interfaces": [{"name": "DiscoveryResult", "file": "ironforge/contracts.py", "description": ""}, {"name": "TemporalDiscoveryPipeline", "file": "ironforge/learning/discovery_pipeline.py", "description": "Shard‑aware pipeline for temporal TGAT discovery.\n\nParameters\n----------\ndata_path : str or Path\n   "}, {"name": "RichNodeFeature", "file": "ironforge/learning/enhanced_graph_builder.py", "description": "45D node feature vector with semantic preservation..."}]}, "synthesis": {"description": "Pattern validation and production graduation", "metrics": {"file_count": 5, "total_lines": 1534, "avg_complexity": 25.4}, "key_components": [{"name": "__init__", "file": "ironforge/synthesis/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "pattern_graduation", "file": "ironforge/synthesis/pattern_graduation.py", "primary_classes": ["PatternGraduation"], "complexity": 15}, {"name": "production_graduation", "file": "ironforge/synthesis/production_graduation.py", "primary_classes": ["ProductionGraduation"], "complexity": 32}, {"name": "metrics", "file": "ironforge/validation/metrics.py", "primary_classes": [], "complexity": 30}, {"name": "runner", "file": "ironforge/validation/runner.py", "primary_classes": ["ValidationConfig", "ValidationRunner"], "complexity": 50}], "public_interfaces": [{"name": "PatternGraduation", "file": "ironforge/synthesis/pattern_graduation.py", "description": "Validation system ensuring discovered patterns exceed 87% baseline accuracy..."}, {"name": "ProductionGraduation", "file": "ironforge/synthesis/production_graduation.py", "description": "Production feature export for graduated patterns\nConverts validated archaeological discoveries into "}, {"name": "ValidationConfig", "file": "ironforge/validation/runner.py", "description": "Configuration for validation experiments.\n\nParameters\n----------\nmode : str\n    Validation mode: \"oo"}]}, "integration": {"description": "System integration, configuration, and dependency injection", "metrics": {"file_count": 25, "total_lines": 7959, "avg_complexity": 29.44}, "key_components": [{"name": "config", "file": "config.py", "primary_classes": ["IRONFORGEConfig"], "complexity": 38}, {"name": "__init__", "file": "iron_core/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "__init__", "file": "iron_core/integration/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "__init__", "file": "iron_core/mathematical/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "adaptive_rg_optimizer", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "primary_classes": ["AdaptiveRGParameters", "ThresholdOptimizationResult", "ScalingCalibrationResult", "AdaptiveRGOptimizer"], "complexity": 78}], "public_interfaces": [{"name": "IRONFORGEConfig", "file": "config.py", "description": "Configuration manager for IRONFORGE system.\n\nEliminates hardcoded paths and provides environment-spe"}, {"name": "AdaptiveRGParameters", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Optimized RG parameters for current market regime..."}, {"name": "ThresholdOptimizationResult", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Result from information-theoretic threshold optimization..."}]}, "validation": {"description": "Testing, validation, and quality assurance", "metrics": {"file_count": 9, "total_lines": 3520, "avg_complexity": 34.44}, "key_components": [{"name": "validation_framework", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "primary_classes": ["ValidationLevel", "TestResult", "ValidationResult", "ValidationSuite", "ValidationLayer", "MathematicalPropertyTest", "NumericalStabilityTest", "PerformanceBenchmarkTest", "MathematicalValidationFramework", "MockHawkesModel", "st"], "complexity": 153}, {"name": "__init__", "file": "iron_core/validation/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "cards", "file": "ironforge/motifs/cards.py", "primary_classes": ["MotifStep", "MotifCard"], "complexity": 1}, {"name": "__init__", "file": "ironforge/validation/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "controls", "file": "ironforge/validation/controls.py", "primary_classes": [], "complexity": 20}], "public_interfaces": [{"name": "ValidationLevel", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Validation thoroughness levels..."}, {"name": "TestResult", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Test result status..."}, {"name": "ValidationResult", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Result of a validation test..."}]}, "reporting": {"description": "Report generation and data visualization", "metrics": {"file_count": 5, "total_lines": 183, "avg_complexity": 3.8}, "key_components": [{"name": "__init__", "file": "ironforge/reporting/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "confluence", "file": "ironforge/reporting/confluence.py", "primary_classes": ["ConfluenceStripSpec"], "complexity": 8}, {"name": "heatmap", "file": "ironforge/reporting/heatmap.py", "primary_classes": ["TimelineHeatmapSpec"], "complexity": 6}, {"name": "html", "file": "ironforge/reporting/html.py", "primary_classes": [], "complexity": 3}, {"name": "writer", "file": "ironforge/reporting/writer.py", "primary_classes": [], "complexity": 2}], "public_interfaces": [{"name": "ConfluenceStripSpec", "file": "ironforge/reporting/confluence.py", "description": ""}, {"name": "TimelineHeatmapSpec", "file": "ironforge/reporting/heatmap.py", "description": ""}]}, "utilities": {"description": "Utility functions, scripts, and support tools", "metrics": {"file_count": 39, "total_lines": 9772, "avg_complexity": 28.9}, "key_components": [{"name": "batch_migrate_graphs", "file": "data_migration/batch_migrate_graphs.py", "primary_classes": ["BatchGraphMigrator"], "complexity": 58}, {"name": "schema_normalizer", "file": "data_migration/schema_normalizer.py", "primary_classes": ["SchemaNormalizer"], "complexity": 47}, {"name": "__init__", "file": "ironforge/scripts/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "prepare_motifs_input", "file": "ironforge/scripts/prepare_motifs_input.py", "primary_classes": [], "complexity": 16}, {"name": "__init__", "file": "ironforge/sdk/__init__.py", "primary_classes": [], "complexity": 0}], "public_interfaces": [{"name": "BatchGraphMigrator", "file": "data_migration/batch_migrate_graphs.py", "description": "Batch migration system for IRONFORGE graph files\n\nTechnical Debt Surgeon: Comprehensive batch proces"}, {"name": "SchemaNormalizer", "file": "data_migration/schema_normalizer.py", "description": "Technical Debt Surgeon implementation for schema normalization\nMigrates 34D legacy data to 37D tempo"}, {"name": "PerformanceMonitor", "file": "ironforge/utilities/performance_monitor.py", "description": "Monitor performance of IRONFORGE components\nTrack timing, memory usage, and component initialization"}]}, "data": {"description": "Data storage and preservation", "metrics": {"file_count": 3, "total_lines": 54, "avg_complexity": 2.33}, "key_components": [{"name": "__init__", "file": "ironforge/data_engine/__init__.py", "primary_classes": [], "complexity": 0}, {"name": "parquet_writer", "file": "ironforge/data_engine/parquet_writer.py", "primary_classes": [], "complexity": 7}, {"name": "schemas", "file": "ironforge/data_engine/schemas.py", "primary_classes": [], "complexity": 0}], "public_interfaces": []}}, "dependency_map": {"import_relationships": {"__init__": {"imports": ["sys", "os"], "imported_by": [], "coupling_score": 2}, "sys": {"imports": [], "imported_by": ["__init__", "extract_lattice_summary", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.scripts.prepare_motifs_input", "ironforge.sdk.cli", "ironforge.synthesis.production_graduation", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.statistical_validator", "run_fpfvg_network_analysis", "run_fpfvg_network_analysis_simple", "run_fpfvg_redelivery_lattice", "run_global_lattice", "run_specialized_lattice", "run_terrain_analysis", "run_weekly_daily_cascade_lattice", "run_weekly_daily_sweep_cascade_step_3b", "run_weekly_daily_sweep_cascade_step_3b_refined", "run_working_cascade_analysis", "scripts.analysis.bridge_node_mapper", "scripts.analysis.enrichment_analyzer", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_manual_discovery", "scripts.utilities.benchmark_performance", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.debug_lattice", "scripts.utilities.graph_builder_diagnostic", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.lattice_population_runner", "scripts.utilities.pattern_intelligence", "visualizations.lattice_visualizer"], "coupling_score": 46}, "os": {"imports": [], "imported_by": ["__init__", "config", "data_migration.batch_migrate_graphs", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.validation_framework", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.specialized_lattice_builder", "ironforge.synthesis.production_graduation", "ironforge.validation.performance_monitor", "ironforge.validation.statistical_validator", "orchestrator", "scripts.analysis.complete_phase2_enhancement", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_validation_summary", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.quick_pattern_discovery", "scripts.analysis.real_pattern_finder", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_direct_discovery", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_session_analysis", "scripts.analysis.run_htf_orchestrator", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.price_field_standardizer", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.session_quality_assessor", "scripts.data_processing.unicode_fix", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.graph_builder_diagnostic", "scripts.utilities.htf_builder", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_monitor", "setup"], "coupling_score": 39}, "config": {"imports": ["os", "json", "pathlib", "typing", "typing", "typing", "logging"], "imported_by": ["ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.synthesis.production_graduation", "ironforge.synthesis.production_graduation", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.statistical_validator", "ironforge.validation.statistical_validator", "orchestrator", "run_fpfvg_network_analysis_simple", "run_working_cascade_analysis"], "coupling_score": 28}, "json": {"imports": [], "imported_by": ["config", "data_migration.batch_migrate_graphs", "data_migration.schema_normalizer", "extract_lattice_summary", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.integration_layer", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.learning.discovery_pipeline", "ironforge.motifs.scanner", "ironforge.scripts.prepare_motifs_input", "ironforge.sdk.cli", "ironforge.synthesis.production_graduation", "ironforge.validation.performance_monitor", "ironforge.validation.runner", "ironforge.validation.statistical_validator", "orchestrator", "run_fpfvg_network_analysis_simple", "run_weekly_daily_sweep_cascade_step_3b_refined", "run_working_cascade_analysis", "scripts.analysis.analyze_concrete_patterns", "scripts.analysis.bridge_node_mapper", "scripts.analysis.complete_phase2_enhancement", "scripts.analysis.comprehensive_discovery_report", "scripts.analysis.enrichment_analyzer", "scripts.analysis.explore_discoveries", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase2_validation_summary", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.quick_pattern_discovery", "scripts.analysis.real_pattern_finder", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_contaminated_session_enhancement", "scripts.analysis.run_direct_discovery", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_session_analysis", "scripts.analysis.run_manual_discovery", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.price_field_standardizer", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.session_quality_assessor", "scripts.data_processing.unicode_fix", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.debug_features", "scripts.utilities.debug_graph_structure", "scripts.utilities.debug_lattice", "scripts.utilities.example_htf_output", "scripts.utilities.graph_builder_diagnostic", "scripts.utilities.htf_builder", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.lattice_population_runner", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_monitor", "scripts.utilities.performance_monitor", "visualizations.lattice_visualizer"], "coupling_score": 82}, "pathlib": {"imports": [], "imported_by": ["config", "data_migration.batch_migrate_graphs", "data_migration.schema_normalizer", "extract_lattice_summary", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.invariants", "iron_core.performance.lazy_loader", "iron_core.setup", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.learning.discovery_pipeline", "ironforge.reporting.writer", "ironforge.scripts.prepare_motifs_input", "ironforge.sdk.cli", "ironforge.synthesis.production_graduation", "ironforge.validation.performance_monitor", "ironforge.validation.runner", "ironforge.validation.statistical_validator", "orchestrator", "orchestrator", "run_fpfvg_network_analysis", "run_fpfvg_network_analysis_simple", "run_fpfvg_redelivery_lattice", "run_global_lattice", "run_specialized_lattice", "run_terrain_analysis", "run_weekly_daily_cascade_lattice", "run_weekly_daily_sweep_cascade_step_3b", "run_weekly_daily_sweep_cascade_step_3b_refined", "run_working_cascade_analysis", "scripts.analysis.analyze_concrete_patterns", "scripts.analysis.bridge_node_mapper", "scripts.analysis.comprehensive_discovery_report", "scripts.analysis.enrichment_analyzer", "scripts.analysis.explore_discoveries", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase2_validation_summary", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.process_all_sessions", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_session_analysis", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.price_field_standardizer", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.session_quality_assessor", "scripts.utilities.benchmark_performance", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.debug_graph_structure", "scripts.utilities.debug_lattice", "scripts.utilities.graph_builder_diagnostic", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.lattice_population_runner", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_intelligence", "scripts.utilities.performance_monitor", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 81}, "typing": {"imports": [], "imported_by": ["config", "config", "config", "data_migration.batch_migrate_graphs", "data_migration.batch_migrate_graphs", "data_migration.batch_migrate_graphs", "data_migration.batch_migrate_graphs", "data_migration.schema_normalizer", "data_migration.schema_normalizer", "data_migration.schema_normalizer", "data_migration.schema_normalizer", "data_migration.schema_normalizer", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.constraints", "iron_core.mathematical.constraints", "iron_core.mathematical.constraints", "iron_core.mathematical.constraints", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.invariants", "iron_core.mathematical.invariants", "iron_core.mathematical.invariants", "iron_core.mathematical.invariants", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.temporal_correlator", "iron_core.mathematical.temporal_correlator", "iron_core.mathematical.temporal_correlator", "iron_core.mathematical.temporal_correlator", "iron_core.mathematical.temporal_correlator", "iron_core.performance.container", "iron_core.performance.container", "iron_core.performance.container", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "ironforge.analysis.broad_spectrum_archaeology", "ironforge.analysis.broad_spectrum_archaeology", "ironforge.analysis.broad_spectrum_archaeology", "ironforge.analysis.broad_spectrum_archaeology", "ironforge.analysis.enhanced_session_adapter", "ironforge.analysis.enhanced_session_adapter", "ironforge.analysis.enhanced_session_adapter", "ironforge.analysis.enhanced_session_adapter", "ironforge.analysis.fpfvg.chain_builder", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.validators", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.timeframe_lattice_mapper", "ironforge.analysis.timeframe_lattice_mapper", "ironforge.analysis.timeframe_lattice_mapper", "ironforge.analysis.timeframe_lattice_mapper", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.contracts", "ironforge.learning.discovery_pipeline", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.tgat_discovery", "ironforge.learning.tgat_discovery", "ironforge.learning.tgat_discovery", "ironforge.learning.tgat_discovery", "ironforge.learning.tgat_discovery", "ironforge.metrics.confluence", "ironforge.scripts.prepare_motifs_input", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.production_graduation", "ironforge.synthesis.production_graduation", "ironforge.synthesis.production_graduation", "ironforge.synthesis.production_graduation", "ironforge.utilities.performance_monitor", "ironforge.utilities.performance_monitor", "ironforge.utilities.performance_monitor", "ironforge.utilities.performance_monitor", "ironforge.validation.controls", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.runner", "ironforge.validation.statistical_validator", "ironforge.validation.statistical_validator", "ironforge.validation.statistical_validator", "ironforge.validation.statistical_validator", "ironforge.validation.statistical_validator", "orchestrator", "orchestrator", "orchestrator", "orchestrator", "run_fpfvg_network_analysis_simple", "run_fpfvg_network_analysis_simple", "run_fpfvg_network_analysis_simple", "run_working_cascade_analysis", "run_working_cascade_analysis", "run_working_cascade_analysis", "run_working_cascade_analysis", "scripts.analysis.bridge_node_mapper", "scripts.analysis.bridge_node_mapper", "scripts.analysis.bridge_node_mapper", "scripts.analysis.bridge_node_mapper", "scripts.analysis.bridge_node_mapper", "scripts.analysis.complete_phase2_enhancement", "scripts.analysis.complete_phase2_enhancement", "scripts.analysis.complete_phase2_enhancement", "scripts.analysis.enrichment_analyzer", "scripts.analysis.enrichment_analyzer", "scripts.analysis.enrichment_analyzer", "scripts.analysis.enrichment_analyzer", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_session_analysis", "scripts.analysis.run_full_session_analysis", "scripts.analysis.run_full_session_analysis", "scripts.analysis.run_full_session_analysis", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.price_field_standardizer", "scripts.data_processing.price_field_standardizer", "scripts.data_processing.price_field_standardizer", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.session_quality_assessor", "scripts.data_processing.session_quality_assessor", "scripts.data_processing.session_quality_assessor", "scripts.data_processing.session_quality_assessor", "scripts.data_processing.session_quality_assessor", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.lattice_population_runner", "scripts.utilities.lattice_population_runner", "scripts.utilities.lattice_population_runner", "scripts.utilities.lattice_population_runner", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "scripts.utilities.performance_monitor", "scripts.utilities.performance_monitor", "scripts.utilities.performance_monitor", "scripts.utilities.performance_monitor", "scripts.utilities.performance_monitor", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 324}, "logging": {"imports": [], "imported_by": ["config", "data_migration.schema_normalizer", "iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.scaling_patterns", "iron_core.performance.container", "iron_core.performance.lazy_loader", "ironforge.analysis.broad_spectrum_archaeology", "ironforge.analysis.enhanced_session_adapter", "ironforge.analysis.fpfvg.chain_builder", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.validators", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.timeframe_lattice_mapper", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.integration.ironforge_container", "ironforge.learning.discovery_pipeline", "ironforge.learning.discovery_pipeline", "ironforge.learning.discovery_pipeline", "ironforge.learning.discovery_pipeline", "ironforge.learning.discovery_pipeline", "ironforge.learning.discovery_pipeline", "ironforge.learning.discovery_pipeline", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.tgat_discovery", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.production_graduation", "ironforge.utilities.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.runner", "ironforge.validation.statistical_validator", "orchestrator", "orchestrator", "orchestrator", "orchestrator", "run_fpfvg_network_analysis", "run_fpfvg_network_analysis_simple", "run_fpfvg_redelivery_lattice", "run_global_lattice", "run_specialized_lattice", "run_terrain_analysis", "run_weekly_daily_cascade_lattice", "run_weekly_daily_sweep_cascade_step_3b", "run_weekly_daily_sweep_cascade_step_3b_refined", "run_working_cascade_analysis", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_validation_framework", "scripts.analysis.quick_pattern_discovery", "scripts.analysis.run_contaminated_session_enhancement", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_session_analysis", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.pattern_monitor", "scripts.utilities.performance_monitor", "visualizations.lattice_visualizer"], "coupling_score": 69}, "data_migration.batch_migrate_graphs": {"imports": ["os", "json", "<PERSON><PERSON><PERSON><PERSON>", "shutil", "pathlib", "datetime", "typing", "typing", "typing", "typing", "concurrent.futures", "concurrent.futures", "traceback", "schema_normalizer", "schema_normalizer", "schema_normalizer"], "imported_by": [], "coupling_score": 16}, "argparse": {"imports": [], "imported_by": ["data_migration.batch_migrate_graphs", "data_migration.schema_normalizer", "ironforge.scripts.prepare_motifs_input", "ironforge.sdk.cli", "scripts.analysis.quick_pattern_discovery", "scripts.utilities.pattern_monitor", "scripts.utilities.performance_monitor"], "coupling_score": 7}, "shutil": {"imports": [], "imported_by": ["data_migration.batch_migrate_graphs"], "coupling_score": 1}, "datetime": {"imports": [], "imported_by": ["data_migration.batch_migrate_graphs", "data_migration.schema_normalizer", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.temporal_correlator", "iron_core.mathematical.temporal_correlator", "ironforge.analysis.fpfvg.chain_builder", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.validators", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.fpfvg_redelivery_lattice", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "ironforge.learning.discovery_pipeline", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.production_graduation", "ironforge.validation.performance_monitor", "ironforge.validation.runner", "ironforge.validation.statistical_validator", "orchestrator", "run_fpfvg_network_analysis_simple", "run_fpfvg_network_analysis_simple", "run_weekly_daily_sweep_cascade_step_3b_refined", "run_weekly_daily_sweep_cascade_step_3b_refined", "run_working_cascade_analysis", "run_working_cascade_analysis", "scripts.analysis.analyze_nypm_patterns", "scripts.analysis.bridge_node_mapper", "scripts.analysis.bridge_node_mapper", "scripts.analysis.complete_phase2_enhancement", "scripts.analysis.enrichment_analyzer", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.quick_pattern_discovery", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_session_analysis", "scripts.analysis.run_full_session_analysis", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.session_quality_assessor", "scripts.data_processing.session_quality_assessor", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.htf_builder", "scripts.utilities.htf_builder", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.lattice_population_runner", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_monitor", "scripts.utilities.performance_monitor", "visualizations.lattice_visualizer"], "coupling_score": 96}, "concurrent.futures": {"imports": [], "imported_by": ["data_migration.batch_migrate_graphs", "data_migration.batch_migrate_graphs", "iron_core.mathematical.scaling_patterns", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk"], "coupling_score": 5}, "traceback": {"imports": [], "imported_by": ["data_migration.batch_migrate_graphs", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_htf_orchestrator", "scripts.utilities.debug_features", "scripts.utilities.debug_graph_structure", "scripts.utilities.debug_lattice", "scripts.utilities.graph_builder_diagnostic", "scripts.utilities.graph_builder_diagnostic"], "coupling_score": 16}, "schema_normalizer": {"imports": [], "imported_by": ["data_migration.batch_migrate_graphs", "data_migration.batch_migrate_graphs", "data_migration.batch_migrate_graphs"], "coupling_score": 3}, "data_migration.schema_normalizer": {"imports": ["json", "logging", "datetime", "typing", "typing", "typing", "typing", "typing", "pathlib", "<PERSON><PERSON><PERSON><PERSON>"], "imported_by": [], "coupling_score": 10}, "extract_lattice_summary": {"imports": ["json", "sys", "pathlib"], "imported_by": [], "coupling_score": 3}, "iron_core.__init__": {"imports": ["iron_core.performance.container", "iron_core.performance.container", "iron_core.performance.container", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader"], "imported_by": [], "coupling_score": 7}, "iron_core.performance.container": {"imports": ["logging", "threading", "typing", "typing", "typing", "time", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader"], "imported_by": ["iron_core.__init__", "iron_core.__init__", "iron_core.__init__", "iron_core.performance.__init__", "iron_core.performance.__init__", "iron_core.performance.__init__", "scripts.analysis.phase5_archaeological_discovery_validation"], "coupling_score": 16}, "iron_core.performance.lazy_loader": {"imports": ["asyncio", "weakref", "threading", "typing", "typing", "typing", "typing", "typing", "typing", "functools", "time", "logging", "pathlib", "importlib"], "imported_by": ["iron_core.__init__", "iron_core.__init__", "iron_core.__init__", "iron_core.__init__", "iron_core.performance.__init__", "iron_core.performance.__init__", "iron_core.performance.__init__", "iron_core.performance.__init__", "iron_core.performance.container", "iron_core.performance.container", "iron_core.performance.container"], "coupling_score": 25}, "iron_core.mathematical.adaptive_rg_optimizer": {"imports": ["numpy", "json", "logging", "typing", "typing", "typing", "typing", "typing", "dataclasses", "pathlib", "scipy", "scipy.stats", "warnings"], "imported_by": [], "coupling_score": 13}, "numpy": {"imports": [], "imported_by": ["iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.temporal_correlator", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.specialized_lattice_builder", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.learning.discovery_pipeline", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.tgat_discovery", "ironforge.metrics.confluence", "ironforge.motifs.scanner", "ironforge.reporting.confluence", "ironforge.reporting.heatmap", "ironforge.sdk.cli", "ironforge.sdk.cli", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.production_graduation", "ironforge.validation.controls", "ironforge.validation.metrics", "ironforge.validation.performance_monitor", "ironforge.validation.runner", "ironforge.validation.splits", "ironforge.validation.statistical_validator", "orchestrator", "run_fpfvg_network_analysis_simple", "scripts.analysis.analyze_concrete_patterns", "scripts.analysis.bridge_node_mapper", "scripts.analysis.complete_phase2_enhancement", "scripts.analysis.comprehensive_discovery_report", "scripts.analysis.enrichment_analyzer", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.phase2_feature_pipeline_enhancement", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase2_validation_summary", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_full_session_analysis", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.price_relativity_generator", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_intelligence", "scripts.utilities.performance_monitor", "visualizations.lattice_visualizer"], "coupling_score": 68}, "dataclasses": {"imports": [], "imported_by": ["iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.cascade_classifier", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.invariants", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.rg_scaler_production", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.temporal_correlator", "iron_core.mathematical.temporal_correlator", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.contracts", "ironforge.metrics.confluence", "ironforge.motifs.cards", "ironforge.motifs.scanner", "ironforge.reporting.confluence", "ironforge.reporting.heatmap", "ironforge.validation.runner", "ironforge.validation.runner", "ironforge.validation.splits", "run_working_cascade_analysis", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_archaeology_demonstration", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "scripts.utilities.performance_monitor", "visualizations.lattice_visualizer"], "coupling_score": 40}, "scipy": {"imports": [], "imported_by": ["iron_core.mathematical.adaptive_rg_optimizer", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.validation.metrics", "scripts.analysis.enrichment_analyzer", "scripts.utilities.pattern_intelligence"], "coupling_score": 6}, "scipy.stats": {"imports": [], "imported_by": ["iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.validation_framework", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg_network_analyzer", "ironforge.validation.statistical_validator", "run_fpfvg_network_analysis_simple", "run_fpfvg_network_analysis_simple", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_htf_structural_inheritance"], "coupling_score": 11}, "warnings": {"imports": [], "imported_by": ["iron_core.mathematical.adaptive_rg_optimizer", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.validation_framework", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.run_full_session_analysis", "visualizations.lattice_visualizer"], "coupling_score": 9}, "iron_core.mathematical.cascade_classifier": {"imports": ["json", "numpy", "datetime", "datetime", "typing", "typing", "typing", "typing", "typing", "dataclasses", "enum"], "imported_by": [], "coupling_score": 11}, "enum": {"imports": [], "imported_by": ["iron_core.mathematical.cascade_classifier", "iron_core.mathematical.constraints", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.temporal_correlator", "scripts.analysis.run_archaeology_demonstration"], "coupling_score": 9}, "iron_core.mathematical.constraints": {"imports": ["decimal", "decimal", "enum", "typing", "typing", "typing", "typing", "math"], "imported_by": ["iron_core.mathematical.mathematical_layers.integration_layer"], "coupling_score": 9}, "decimal": {"imports": [], "imported_by": ["iron_core.mathematical.constraints", "iron_core.mathematical.constraints", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.theory_abstraction"], "coupling_score": 5}, "math": {"imports": [], "imported_by": ["iron_core.mathematical.constraints", "iron_core.mathematical.hawkes_engine", "ironforge.reporting.heatmap", "scripts.utilities.htf_builder"], "coupling_score": 4}, "iron_core.mathematical.fisher_information_monitor": {"imports": ["numpy", "logging", "typing", "typing", "typing", "typing", "typing", "dataclasses", "warnings", "scipy.stats"], "imported_by": ["iron_core.mathematical.mathematical_layers.integration_layer"], "coupling_score": 11}, "iron_core.mathematical.grammar_fisher_correlator": {"imports": ["logging", "numpy", "typing", "typing", "typing", "typing", "typing", "dataclasses", "datetime", "collections"], "imported_by": [], "coupling_score": 10}, "collections": {"imports": [], "imported_by": ["iron_core.mathematical.grammar_fisher_correlator", "iron_core.mathematical.mathematical_hooks", "ironforge.analysis.global_lattice_builder", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.lattice_terrain_analyzer", "ironforge.analysis.specialized_lattice_builder", "scripts.analysis.analyze_concrete_patterns", "scripts.analysis.analyze_concrete_patterns", "scripts.analysis.analyze_nypm_patterns", "scripts.analysis.analyze_nypm_patterns", "scripts.analysis.bridge_node_mapper", "scripts.analysis.bridge_node_mapper", "scripts.analysis.comprehensive_discovery_report", "scripts.analysis.comprehensive_discovery_report", "scripts.analysis.enrichment_analyzer", "scripts.analysis.enrichment_analyzer", "scripts.analysis.explore_discoveries", "scripts.analysis.explore_discoveries", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.phase2_validation_framework", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.analysis.run_archaeology_demonstration", "scripts.utilities.htf_builder", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 42}, "iron_core.mathematical.hawkes_engine": {"imports": ["numpy", "json", "math", "typing", "typing", "typing", "typing", "typing", "dataclasses", "datetime", "datetime", "logging"], "imported_by": ["iron_core.mathematical.mathematical_layers.integration_layer"], "coupling_score": 13}, "iron_core.mathematical.invariants": {"imports": ["ast", "<PERSON><PERSON><PERSON>", "inspect", "time", "typing", "typing", "typing", "typing", "dataclasses", "pathlib"], "imported_by": [], "coupling_score": 10}, "ast": {"imports": [], "imported_by": ["iron_core.mathematical.invariants"], "coupling_score": 1}, "hashlib": {"imports": [], "imported_by": ["iron_core.mathematical.invariants"], "coupling_score": 1}, "inspect": {"imports": [], "imported_by": ["iron_core.mathematical.invariants"], "coupling_score": 1}, "time": {"imports": [], "imported_by": ["iron_core.mathematical.invariants", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.performance.container", "iron_core.performance.lazy_loader", "ironforge.utilities.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.runner", "scripts.analysis.enrichment_analyzer", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_archaeological_discovery_validation", "scripts.analysis.process_all_sessions", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.utilities.benchmark_performance", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.lattice_population_runner", "scripts.utilities.pattern_monitor", "scripts.utilities.performance_monitor", "scripts.utilities.performance_monitor"], "coupling_score": 25}, "iron_core.mathematical.mathematical_hooks": {"imports": ["typing", "typing", "typing", "typing", "typing", "typing", "dataclasses", "dataclasses", "enum", "abc", "abc", "asyncio", "numpy", "logging", "datetime", "datetime", "collections", "json", "asyncio"], "imported_by": ["iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface"], "coupling_score": 23}, "abc": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns"], "coupling_score": 14}, "asyncio": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.scaling_patterns", "iron_core.mathematical.scaling_patterns", "iron_core.performance.lazy_loader"], "coupling_score": 8}, "iron_core.mathematical.mathematical_layers.api_interface": {"imports": ["abc", "abc", "typing", "typing", "typing", "typing", "typing", "dataclasses", "datetime", "json", "logging", "asyncio", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_hooks", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "fastapi.responses", "pydantic", "pydantic", "u<PERSON><PERSON>", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_hooks", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "numpy"], "imported_by": [], "coupling_score": 31}, "iron_core.mathematical.mathematical_layers.integration_layer": {"imports": ["abc", "abc", "typing", "typing", "typing", "typing", "typing", "typing", "dataclasses", "dataclasses", "enum", "numpy", "json", "logging", "asyncio", "datetime", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.constraints", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.hawkes_engine", "iron_core.mathematical.fisher_information_monitor", "iron_core.mathematical.rg_scaler_production"], "imported_by": ["iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface"], "coupling_score": 30}, "fastapi": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface"], "coupling_score": 5}, "fastapi.responses": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.api_interface"], "coupling_score": 1}, "pydantic": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface"], "coupling_score": 2}, "uvicorn": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.api_interface"], "coupling_score": 1}, "iron_core.mathematical.mathematical_layers.validation_framework": {"imports": ["abc", "abc", "typing", "typing", "typing", "typing", "typing", "typing", "typing", "dataclasses", "dataclasses", "enum", "numpy", "scipy.stats", "scipy.optimize", "logging", "datetime", "warnings", "asyncio", "hypothesis", "hypothesis", "hypothesis", "hypothesis", "psutil", "os"], "imported_by": ["iron_core.mathematical.mathematical_layers.api_interface", "iron_core.mathematical.mathematical_layers.api_interface"], "coupling_score": 27}, "iron_core.mathematical.mathematical_layers.core_algorithms": {"imports": ["abc", "abc", "typing", "typing", "typing", "typing", "typing", "typing", "dataclasses", "numpy", "decimal", "decimal", "scipy.fft", "scipy.optimize", "scipy.stats", "warnings", "iron_core.mathematical.mathematical_layers.theory_abstraction", "iron_core.mathematical.mathematical_layers.theory_abstraction", "time", "psutil", "os", "time", "time"], "imported_by": ["iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer"], "coupling_score": 28}, "scipy.fft": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.core_algorithms"], "coupling_score": 1}, "scipy.optimize": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.validation_framework"], "coupling_score": 2}, "iron_core.mathematical.mathematical_layers.theory_abstraction": {"imports": ["abc", "abc", "typing", "typing", "typing", "typing", "typing", "typing", "typing", "dataclasses", "enum", "decimal", "numpy"], "imported_by": ["iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.integration_layer", "iron_core.mathematical.mathematical_layers.integration_layer"], "coupling_score": 17}, "psutil": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.core_algorithms", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.scaling_patterns", "ironforge.validation.performance_monitor", "scripts.analysis.phase4d_profile_run", "scripts.utilities.performance_monitor"], "coupling_score": 6}, "iron_core.mathematical.rg_scaler_production": {"imports": ["numpy", "logging", "typing", "typing", "typing", "typing", "typing", "dataclasses"], "imported_by": ["iron_core.mathematical.mathematical_layers.integration_layer"], "coupling_score": 9}, "hypothesis": {"imports": [], "imported_by": ["iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework", "iron_core.mathematical.mathematical_layers.validation_framework"], "coupling_score": 4}, "iron_core.mathematical.scaling_patterns": {"imports": ["typing", "typing", "typing", "typing", "typing", "typing", "typing", "dataclasses", "dataclasses", "enum", "abc", "abc", "asyncio", "numpy", "logging", "datetime", "concurrent.futures", "psutil", "threading", "contextlib", "asyncio"], "imported_by": [], "coupling_score": 21}, "threading": {"imports": [], "imported_by": ["iron_core.mathematical.scaling_patterns", "iron_core.performance.container", "iron_core.performance.lazy_loader"], "coupling_score": 3}, "contextlib": {"imports": [], "imported_by": ["iron_core.mathematical.scaling_patterns", "ironforge.utilities.performance_monitor", "ironforge.validation.performance_monitor"], "coupling_score": 3}, "iron_core.mathematical.temporal_correlator": {"imports": ["numpy", "datetime", "datetime", "typing", "typing", "typing", "typing", "typing", "dataclasses", "dataclasses", "enum"], "imported_by": [], "coupling_score": 11}, "iron_core.performance.__init__": {"imports": ["iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.lazy_loader", "iron_core.performance.container", "iron_core.performance.container", "iron_core.performance.container"], "imported_by": [], "coupling_score": 7}, "weakref": {"imports": [], "imported_by": ["iron_core.performance.lazy_loader"], "coupling_score": 1}, "functools": {"imports": [], "imported_by": ["iron_core.performance.lazy_loader"], "coupling_score": 1}, "importlib": {"imports": [], "imported_by": ["iron_core.performance.lazy_loader", "scripts.utilities.benchmark_performance"], "coupling_score": 2}, "iron_core.setup": {"imports": ["setuptools", "setuptools", "pathlib"], "imported_by": [], "coupling_score": 3}, "setuptools": {"imports": [], "imported_by": ["iron_core.setup", "iron_core.setup", "setup", "setup"], "coupling_score": 4}, "ironforge.__init__": {"imports": ["ironforge.integration.ironforge_container", "ironforge.integration.ironforge_container"], "imported_by": [], "coupling_score": 2}, "ironforge.integration.ironforge_container": {"imports": ["iron_core.performance", "logging", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.tgat_discovery", "ironforge.synthesis.pattern_graduation"], "imported_by": ["ironforge.__init__", "ironforge.__init__", "ironforge.integration.__init__", "ironforge.integration.__init__", "ironforge.integration.__init__", "ironforge.learning.discovery_pipeline", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "ironforge.validation.performance_monitor", "orchestrator", "scripts.utilities.benchmark_performance"], "coupling_score": 18}, "ironforge.analysis.__init__": {"imports": ["ironforge.analysis.timeframe_lattice_mapper", "ironforge.analysis.enhanced_session_adapter", "ironforge.analysis.broad_spectrum_archaeology"], "imported_by": [], "coupling_score": 3}, "ironforge.analysis.timeframe_lattice_mapper": {"imports": ["logging", "typing", "typing", "typing", "typing"], "imported_by": ["ironforge.analysis.__init__", "ironforge.analysis.global_lattice_builder"], "coupling_score": 7}, "ironforge.analysis.enhanced_session_adapter": {"imports": ["logging", "typing", "typing", "typing", "typing"], "imported_by": ["ironforge.analysis.__init__"], "coupling_score": 6}, "ironforge.analysis.broad_spectrum_archaeology": {"imports": ["logging", "typing", "typing", "typing", "typing"], "imported_by": ["ironforge.analysis.__init__"], "coupling_score": 6}, "ironforge.analysis.fpfvg.chain_builder": {"imports": ["logging", "datetime", "typing"], "imported_by": ["ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner"], "coupling_score": 7}, "ironforge.analysis.fpfvg.features": {"imports": ["logging", "typing", "numpy", "scipy", "scipy.stats"], "imported_by": ["ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner"], "coupling_score": 15}, "ironforge.analysis.fpfvg.runner": {"imports": ["json", "logging", "sys", "datetime", "datetime", "pathlib", "typing", "config", "ironforge.analysis.fpfvg.chain_builder", "ironforge.analysis.fpfvg.chain_builder", "ironforge.analysis.fpfvg.chain_builder", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.features", "ironforge.analysis.fpfvg.validators", "ironforge.analysis.fpfvg.validators", "ironforge.analysis.fpfvg.validators", "ironforge.analysis.fpfvg.validators", "ironforge.analysis.fpfvg.chain_builder"], "imported_by": [], "coupling_score": 26}, "ironforge.analysis.fpfvg.validators": {"imports": ["logging", "datetime", "typing"], "imported_by": ["ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner", "ironforge.analysis.fpfvg.runner"], "coupling_score": 7}, "ironforge.analysis.fpfvg_network_analyzer": {"imports": ["json", "logging", "numpy", "pandas", "datetime", "datetime", "datetime", "pathlib", "typing", "typing", "typing", "typing", "typing", "scipy", "scipy.stats", "sys", "config"], "imported_by": ["run_fpfvg_network_analysis"], "coupling_score": 18}, "pandas": {"imports": [], "imported_by": ["ironforge.analysis.fpfvg_network_analyzer", "ironforge.data_engine.parquet_writer", "ironforge.graph_builder.igraph_builder", "ironforge.learning.discovery_pipeline", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.phase2_validation_framework", "scripts.analysis.run_full_session_analysis", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.ironforge_discovery_sdk", "scripts.utilities.pattern_correlation_visualizer", "scripts.utilities.pattern_intelligence", "visualizations.lattice_visualizer"], "coupling_score": 15}, "ironforge.analysis.fpfvg_redelivery_lattice": {"imports": ["json", "logging", "datetime", "datetime", "pathlib", "typing", "typing", "typing", "typing", "typing", "sys", "config"], "imported_by": ["run_fpfvg_redelivery_lattice"], "coupling_score": 13}, "ironforge.analysis.global_lattice_builder": {"imports": ["json", "logging", "pathlib", "typing", "typing", "typing", "typing", "typing", "datetime", "numpy", "collections", "ironforge.analysis.timeframe_lattice_mapper", "config", "sys", "os", "config"], "imported_by": ["run_global_lattice"], "coupling_score": 17}, "ironforge.analysis.lattice_terrain_analyzer": {"imports": ["json", "logging", "pathlib", "typing", "typing", "typing", "typing", "typing", "datetime", "numpy", "collections", "collections", "config", "sys", "os", "config"], "imported_by": ["run_terrain_analysis"], "coupling_score": 17}, "ironforge.analysis.refined_sweep_detector": {"imports": ["json", "logging", "sys", "dataclasses", "datetime", "pathlib", "typing", "config"], "imported_by": ["run_weekly_daily_sweep_cascade_step_3b_refined"], "coupling_score": 9}, "ironforge.analysis.specialized_lattice_builder": {"imports": ["json", "logging", "pathlib", "typing", "typing", "typing", "typing", "typing", "datetime", "datetime", "numpy", "collections", "config", "sys", "os", "config"], "imported_by": ["run_specialized_lattice"], "coupling_score": 17}, "ironforge.analysis.weekly_daily_sweep_cascade_analyzer": {"imports": ["json", "logging", "sys", "dataclasses", "datetime", "datetime", "pathlib", "typing", "numpy", "config"], "imported_by": ["run_weekly_daily_sweep_cascade_step_3b", "run_weekly_daily_sweep_cascade_step_3b_refined", "run_weekly_daily_sweep_cascade_step_3b_refined"], "coupling_score": 13}, "ironforge.analysis.weekly_daily_sweep_cascade_lattice": {"imports": ["json", "logging", "datetime", "datetime", "datetime", "pathlib", "typing", "typing", "typing", "typing", "typing", "sys", "config"], "imported_by": ["run_weekly_daily_cascade_lattice"], "coupling_score": 14}, "ironforge.contracts": {"imports": ["dataclasses", "typing"], "imported_by": [], "coupling_score": 2}, "ironforge.data_engine.parquet_writer": {"imports": ["pandas", "p<PERSON><PERSON>", "pyarrow.parquet", "ironforge.data_engine.schemas", "ironforge.data_engine.schemas", "ironforge.data_engine.schemas"], "imported_by": [], "coupling_score": 6}, "pyarrow": {"imports": [], "imported_by": ["ironforge.data_engine.parquet_writer"], "coupling_score": 1}, "pyarrow.parquet": {"imports": [], "imported_by": ["ironforge.data_engine.parquet_writer"], "coupling_score": 1}, "ironforge.data_engine.schemas": {"imports": [], "imported_by": ["ironforge.data_engine.parquet_writer", "ironforge.data_engine.parquet_writer", "ironforge.data_engine.parquet_writer"], "coupling_score": 3}, "ironforge.graph_builder.igraph_builder": {"imports": ["igraph", "pandas"], "imported_by": [], "coupling_score": 2}, "igraph": {"imports": [], "imported_by": ["ironforge.graph_builder.igraph_builder"], "coupling_score": 1}, "ironforge.graph_builder.pyg_converters": {"imports": ["torch", "torch_geometric.data"], "imported_by": [], "coupling_score": 2}, "torch": {"imports": [], "imported_by": ["ironforge.graph_builder.pyg_converters", "ironforge.learning.discovery_pipeline", "ironforge.learning.enhanced_graph_builder", "ironforge.learning.tgat_discovery", "ironforge.synthesis.pattern_graduation", "ironforge.synthesis.production_graduation", "ironforge.validation.performance_monitor", "orchestrator", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.utilities.debug_features", "scripts.utilities.debug_tgat_init", "scripts.utilities.ironforge_discovery_sdk"], "coupling_score": 17}, "torch_geometric.data": {"imports": [], "imported_by": ["ironforge.graph_builder.pyg_converters", "ironforge.learning.discovery_pipeline"], "coupling_score": 2}, "ironforge.integration.__init__": {"imports": ["ironforge.integration.ironforge_container", "ironforge.integration.ironforge_container", "ironforge.integration.ironforge_container"], "imported_by": [], "coupling_score": 3}, "iron_core.performance": {"imports": [], "imported_by": ["ironforge.integration.ironforge_container"], "coupling_score": 1}, "ironforge.learning.enhanced_graph_builder": {"imports": ["torch", "networkx", "numpy", "typing", "typing", "typing", "typing", "typing", "logging"], "imported_by": ["ironforge.integration.ironforge_container", "ironforge.learning.__init__", "ironforge.learning.tgat_discovery", "ironforge.validation.performance_monitor"], "coupling_score": 13}, "ironforge.learning.tgat_discovery": {"imports": ["torch", "torch.nn", "torch.nn.functional", "networkx", "numpy", "typing", "typing", "typing", "typing", "typing", "logging", "ironforge.learning.enhanced_graph_builder"], "imported_by": ["ironforge.integration.ironforge_container", "ironforge.learning.__init__", "ironforge.learning.discovery_pipeline"], "coupling_score": 15}, "ironforge.synthesis.pattern_graduation": {"imports": ["torch", "numpy", "typing", "typing", "typing", "typing", "logging", "datetime"], "imported_by": ["ironforge.integration.ironforge_container", "ironforge.synthesis.__init__", "ironforge.validation.performance_monitor", "orchestrator"], "coupling_score": 12}, "ironforge.learning.__init__": {"imports": ["ironforge.learning.enhanced_graph_builder", "ironforge.learning.tgat_discovery", "ironforge.learning.simple_event_clustering", "ironforge.learning.regime_segmentation"], "imported_by": [], "coupling_score": 4}, "ironforge.learning.simple_event_clustering": {"imports": [], "imported_by": ["ironforge.learning.__init__", "orchestrator"], "coupling_score": 2}, "ironforge.learning.regime_segmentation": {"imports": [], "imported_by": ["ironforge.learning.__init__"], "coupling_score": 1}, "ironforge.learning.discovery_pipeline": {"imports": ["__future__", "collections.abc", "pathlib", "typing", "numpy", "torch", "torch_geometric.data", "torch_geometric.loader", "logging", "pandas", "logging", "logging", "logging", "logging", "logging", "json", "logging", "datetime", "ironforge.integration.ironforge_container", "ironforge.learning.tgat_discovery"], "imported_by": ["ironforge.sdk.cli"], "coupling_score": 21}, "__future__": {"imports": [], "imported_by": ["ironforge.learning.discovery_pipeline", "ironforge.metrics.confluence", "ironforge.motifs.cards", "ironforge.motifs.scanner", "ironforge.reporting.confluence", "ironforge.reporting.heatmap", "ironforge.reporting.html", "ironforge.reporting.writer", "ironforge.scripts.prepare_motifs_input", "ironforge.sdk.cli", "ironforge.validation.controls", "ironforge.validation.metrics", "ironforge.validation.runner", "ironforge.validation.splits"], "coupling_score": 14}, "collections.abc": {"imports": [], "imported_by": ["ironforge.learning.discovery_pipeline", "ironforge.metrics.confluence", "ironforge.metrics.confluence", "ironforge.validation.controls", "ironforge.validation.metrics", "ironforge.validation.runner", "ironforge.validation.splits", "ironforge.validation.splits"], "coupling_score": 8}, "torch_geometric.loader": {"imports": [], "imported_by": ["ironforge.learning.discovery_pipeline"], "coupling_score": 1}, "networkx": {"imports": [], "imported_by": ["ironforge.learning.enhanced_graph_builder", "ironforge.learning.tgat_discovery", "visualizations.lattice_visualizer"], "coupling_score": 3}, "torch.nn": {"imports": [], "imported_by": ["ironforge.learning.tgat_discovery"], "coupling_score": 1}, "torch.nn.functional": {"imports": [], "imported_by": ["ironforge.learning.tgat_discovery"], "coupling_score": 1}, "ironforge.metrics.__init__": {"imports": ["ironforge.metrics.confluence", "ironforge.metrics.confluence", "ironforge.metrics.confluence"], "imported_by": [], "coupling_score": 3}, "ironforge.metrics.confluence": {"imports": ["__future__", "collections.abc", "collections.abc", "dataclasses", "typing", "numpy"], "imported_by": ["ironforge.metrics.__init__", "ironforge.metrics.__init__", "ironforge.metrics.__init__"], "coupling_score": 9}, "ironforge.motifs.__init__": {"imports": ["ironforge.motifs.cards", "ironforge.motifs.cards", "ironforge.motifs.cards", "ironforge.motifs.scanner", "ironforge.motifs.scanner"], "imported_by": [], "coupling_score": 5}, "ironforge.motifs.cards": {"imports": ["__future__", "dataclasses"], "imported_by": ["ironforge.motifs.__init__", "ironforge.motifs.__init__", "ironforge.motifs.__init__", "ironforge.motifs.scanner", "ironforge.motifs.scanner"], "coupling_score": 7}, "ironforge.motifs.scanner": {"imports": ["__future__", "bisect", "dataclasses", "numpy", "ironforge.motifs.cards", "ironforge.motifs.cards", "json"], "imported_by": ["ironforge.motifs.__init__", "ironforge.motifs.__init__", "ironforge.sdk.cli"], "coupling_score": 10}, "bisect": {"imports": [], "imported_by": ["ironforge.motifs.scanner"], "coupling_score": 1}, "ironforge.reporting.__init__": {"imports": ["ironforge.reporting.confluence", "ironforge.reporting.confluence", "ironforge.reporting.heatmap", "ironforge.reporting.heatmap"], "imported_by": [], "coupling_score": 4}, "ironforge.reporting.confluence": {"imports": ["__future__", "dataclasses", "numpy", "PIL", "PIL"], "imported_by": ["ironforge.reporting.__init__", "ironforge.reporting.__init__"], "coupling_score": 7}, "ironforge.reporting.heatmap": {"imports": ["__future__", "math", "dataclasses", "numpy", "PIL"], "imported_by": ["ironforge.reporting.__init__", "ironforge.reporting.__init__"], "coupling_score": 7}, "PIL": {"imports": [], "imported_by": ["ironforge.reporting.confluence", "ironforge.reporting.confluence", "ironforge.reporting.heatmap"], "coupling_score": 3}, "ironforge.reporting.html": {"imports": ["__future__", "base64", "io"], "imported_by": [], "coupling_score": 3}, "base64": {"imports": [], "imported_by": ["ironforge.reporting.html"], "coupling_score": 1}, "io": {"imports": [], "imported_by": ["ironforge.reporting.html"], "coupling_score": 1}, "ironforge.reporting.writer": {"imports": ["__future__", "pathlib"], "imported_by": [], "coupling_score": 2}, "ironforge.scripts.prepare_motifs_input": {"imports": ["__future__", "json", "pathlib", "typing", "<PERSON><PERSON><PERSON><PERSON>", "sys"], "imported_by": ["ironforge.sdk.cli"], "coupling_score": 7}, "ironforge.sdk.__main__": {"imports": ["ironforge.sdk.cli"], "imported_by": [], "coupling_score": 1}, "ironforge.sdk.cli": {"imports": ["__future__", "<PERSON><PERSON><PERSON><PERSON>", "sys", "pathlib", "ironforge.learning.discovery_pipeline", "ironforge.validation.runner", "ironforge.validation.runner", "numpy", "json", "numpy", "ironforge.reporting", "ironforge.reporting", "ironforge.reporting", "ironforge.reporting", "ironforge.motifs.scanner", "ironforge.scripts.prepare_motifs_input"], "imported_by": ["ironforge.sdk.__main__"], "coupling_score": 17}, "ironforge.validation.runner": {"imports": ["__future__", "json", "logging", "time", "collections.abc", "dataclasses", "dataclasses", "datetime", "pathlib", "typing", "numpy", "ironforge.validation.controls", "ironforge.validation.metrics", "ironforge.validation.splits", "ironforge.validation.splits", "ironforge.validation.splits"], "imported_by": ["ironforge.sdk.cli", "ironforge.sdk.cli", "ironforge.validation.__init__", "ironforge.validation.__init__"], "coupling_score": 20}, "ironforge.reporting": {"imports": [], "imported_by": ["ironforge.sdk.cli", "ironforge.sdk.cli", "ironforge.sdk.cli", "ironforge.sdk.cli"], "coupling_score": 4}, "ironforge.synthesis.__init__": {"imports": ["ironforge.synthesis.pattern_graduation", "ironforge.synthesis.production_graduation"], "imported_by": [], "coupling_score": 2}, "ironforge.synthesis.production_graduation": {"imports": ["json", "torch", "numpy", "typing", "typing", "typing", "typing", "pathlib", "logging", "datetime", "config", "sys", "os", "config"], "imported_by": ["ironforge.synthesis.__init__"], "coupling_score": 15}, "ironforge.utilities.__init__": {"imports": ["ironforge.utilities.performance_monitor"], "imported_by": [], "coupling_score": 1}, "ironforge.utilities.performance_monitor": {"imports": ["time", "logging", "typing", "typing", "typing", "typing", "contextlib"], "imported_by": ["ironforge.utilities.__init__"], "coupling_score": 8}, "ironforge.validation.__init__": {"imports": ["ironforge.validation.statistical_validator", "ironforge.validation.regression_tester", "ironforge.validation.performance_monitor", "ironforge.validation.integration_tester", "ironforge.validation.splits", "ironforge.validation.splits", "ironforge.validation.splits", "ironforge.validation.controls", "ironforge.validation.controls", "ironforge.validation.controls", "ironforge.validation.controls", "ironforge.validation.controls", "ironforge.validation.controls", "ironforge.validation.metrics", "ironforge.validation.metrics", "ironforge.validation.metrics", "ironforge.validation.metrics", "ironforge.validation.metrics", "ironforge.validation.metrics", "ironforge.validation.runner", "ironforge.validation.runner"], "imported_by": [], "coupling_score": 21}, "ironforge.validation.statistical_validator": {"imports": ["numpy", "scipy.stats", "typing", "typing", "typing", "typing", "typing", "logging", "datetime", "json", "pathlib", "config", "sys", "os", "config"], "imported_by": ["ironforge.validation.__init__"], "coupling_score": 16}, "ironforge.validation.regression_tester": {"imports": [], "imported_by": ["ironforge.validation.__init__"], "coupling_score": 1}, "ironforge.validation.performance_monitor": {"imports": ["time", "sys", "psutil", "torch", "numpy", "typing", "typing", "typing", "typing", "typing", "logging", "datetime", "json", "pathlib", "gc", "contextlib", "config", "sys", "os", "config", "ironforge.integration.ironforge_container", "ironforge.integration.ironforge_container", "ironforge.integration.ironforge_container", "ironforge.learning.enhanced_graph_builder", "ironforge.synthesis.pattern_graduation", "ironforge.integration.ironforge_container", "ironforge.integration.ironforge_container"], "imported_by": ["ironforge.validation.__init__"], "coupling_score": 28}, "ironforge.validation.integration_tester": {"imports": [], "imported_by": ["ironforge.validation.__init__"], "coupling_score": 1}, "ironforge.validation.splits": {"imports": ["__future__", "collections.abc", "collections.abc", "dataclasses", "numpy"], "imported_by": ["ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.runner", "ironforge.validation.runner", "ironforge.validation.runner"], "coupling_score": 11}, "ironforge.validation.controls": {"imports": ["__future__", "collections.abc", "typing", "numpy"], "imported_by": ["ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.runner"], "coupling_score": 11}, "ironforge.validation.metrics": {"imports": ["__future__", "collections.abc", "numpy", "scipy", "sklearn.metrics"], "imported_by": ["ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.__init__", "ironforge.validation.runner"], "coupling_score": 12}, "sklearn.metrics": {"imports": [], "imported_by": ["ironforge.validation.metrics", "scripts.analysis.investigate_pattern_subarchitecture"], "coupling_score": 2}, "gc": {"imports": [], "imported_by": ["ironforge.validation.performance_monitor"], "coupling_score": 1}, "orchestrator": {"imports": ["os", "json", "pickle", "torch", "numpy", "logging", "typing", "typing", "typing", "typing", "datetime", "pathlib", "config", "ironforge.integration.ironforge_container", "ironforge.synthesis.pattern_graduation", "ironforge.learning.simple_event_clustering", "pathlib", "performance_monitor", "performance_monitor", "learning.graph_builder", "logging", "logging", "performance_monitor", "logging"], "imported_by": ["scripts.analysis.process_all_sessions", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_htf_orchestrator", "scripts.utilities.ironforge_discovery_sdk"], "coupling_score": 28}, "pickle": {"imports": [], "imported_by": ["orchestrator", "scripts.analysis.analyze_concrete_patterns", "scripts.analysis.analyze_nypm_patterns", "scripts.analysis.comprehensive_discovery_report", "scripts.analysis.explore_discoveries", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.process_all_sessions", "scripts.analysis.real_pattern_finder"], "coupling_score": 12}, "performance_monitor": {"imports": [], "imported_by": ["orchestrator", "orchestrator", "orchestrator"], "coupling_score": 3}, "learning.graph_builder": {"imports": [], "imported_by": ["orchestrator"], "coupling_score": 1}, "run_fpfvg_network_analysis": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.fpfvg_network_analyzer"], "imported_by": [], "coupling_score": 4}, "run_fpfvg_network_analysis_simple": {"imports": ["sys", "json", "logging", "pathlib", "datetime", "typing", "typing", "typing", "numpy", "scipy.stats", "scipy.stats", "config", "datetime"], "imported_by": [], "coupling_score": 13}, "run_fpfvg_redelivery_lattice": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.fpfvg_redelivery_lattice"], "imported_by": [], "coupling_score": 4}, "run_global_lattice": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.global_lattice_builder"], "imported_by": [], "coupling_score": 4}, "run_specialized_lattice": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.specialized_lattice_builder"], "imported_by": [], "coupling_score": 4}, "run_terrain_analysis": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.lattice_terrain_analyzer"], "imported_by": [], "coupling_score": 4}, "run_weekly_daily_cascade_lattice": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.weekly_daily_sweep_cascade_lattice"], "imported_by": [], "coupling_score": 4}, "run_weekly_daily_sweep_cascade_step_3b": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer"], "imported_by": [], "coupling_score": 4}, "run_weekly_daily_sweep_cascade_step_3b_refined": {"imports": ["sys", "logging", "pathlib", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "ironforge.analysis.refined_sweep_detector", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "datetime", "datetime", "json"], "imported_by": [], "coupling_score": 9}, "run_working_cascade_analysis": {"imports": ["sys", "json", "logging", "datetime", "datetime", "pathlib", "typing", "typing", "typing", "typing", "dataclasses", "config"], "imported_by": [], "coupling_score": 12}, "scripts.analysis.analyze_concrete_patterns": {"imports": ["json", "pickle", "numpy", "glob", "pathlib", "collections", "collections"], "imported_by": [], "coupling_score": 7}, "glob": {"imports": [], "imported_by": ["scripts.analysis.analyze_concrete_patterns", "scripts.analysis.analyze_nypm_patterns", "scripts.analysis.bridge_node_mapper", "scripts.analysis.comprehensive_discovery_report", "scripts.analysis.enrichment_analyzer", "scripts.analysis.explore_discoveries", "scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.process_all_sessions", "scripts.analysis.real_pattern_finder", "scripts.analysis.run_direct_discovery", "scripts.analysis.run_full_scale_discovery", "scripts.analysis.run_htf_orchestrator", "scripts.data_processing.enhanced_session_relativity_processor", "scripts.data_processing.enhanced_sessions_price_relativity_processor", "scripts.data_processing.price_relativity_generator", "scripts.data_processing.unicode_fix", "scripts.utilities.htf_builder", "scripts.utilities.lattice_population_runner"], "coupling_score": 27}, "scripts.analysis.analyze_nypm_patterns": {"imports": ["re", "glob", "pickle", "collections", "collections", "datetime"], "imported_by": [], "coupling_score": 6}, "re": {"imports": [], "imported_by": ["scripts.analysis.analyze_nypm_patterns", "scripts.analysis.bridge_node_mapper", "scripts.analysis.real_pattern_finder"], "coupling_score": 3}, "scripts.analysis.bridge_node_mapper": {"imports": ["sys", "json", "glob", "pathlib", "typing", "typing", "typing", "typing", "typing", "datetime", "datetime", "collections", "collections", "numpy", "re"], "imported_by": [], "coupling_score": 15}, "scripts.analysis.complete_phase2_enhancement": {"imports": ["json", "os", "random", "numpy", "datetime", "typing", "typing", "typing"], "imported_by": [], "coupling_score": 8}, "random": {"imports": [], "imported_by": ["scripts.analysis.complete_phase2_enhancement"], "coupling_score": 1}, "scripts.analysis.comprehensive_discovery_report": {"imports": ["pickle", "json", "glob", "pathlib", "collections", "collections", "numpy"], "imported_by": [], "coupling_score": 7}, "scripts.analysis.enrichment_analyzer": {"imports": ["sys", "json", "time", "pathlib", "typing", "typing", "typing", "typing", "datetime", "collections", "collections", "numpy", "scipy", "glob"], "imported_by": [], "coupling_score": 14}, "scripts.analysis.explore_discoveries": {"imports": ["json", "pickle", "glob", "pathlib", "collections", "collections"], "imported_by": [], "coupling_score": 6}, "scripts.analysis.investigate_causal_event_chains": {"imports": ["json", "pickle", "numpy", "pandas", "datetime", "datetime", "glob", "pathlib", "collections", "collections", "matplotlib.pyplot", "seaborn", "itertools", "warnings"], "imported_by": [], "coupling_score": 14}, "matplotlib.pyplot": {"imports": [], "imported_by": ["scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.investigate_pattern_subarchitecture", "scripts.analysis.phase2_validation_framework", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_full_session_analysis", "scripts.utilities.pattern_correlation_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 10}, "seaborn": {"imports": [], "imported_by": ["scripts.analysis.investigate_causal_event_chains", "scripts.analysis.investigate_cross_session_synchronization", "scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.investigate_liquidity_sweep_catalyst", "scripts.analysis.phase2_validation_framework", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_full_session_analysis", "scripts.utilities.pattern_correlation_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 9}, "itertools": {"imports": [], "imported_by": ["scripts.analysis.investigate_causal_event_chains"], "coupling_score": 1}, "scripts.analysis.investigate_cross_session_synchronization": {"imports": ["json", "pickle", "numpy", "pandas", "datetime", "datetime", "glob", "pathlib", "collections", "collections", "matplotlib.pyplot", "seaborn"], "imported_by": [], "coupling_score": 12}, "scripts.analysis.investigate_htf_structural_inheritance": {"imports": ["json", "pickle", "numpy", "pandas", "datetime", "datetime", "glob", "pathlib", "collections", "collections", "matplotlib.pyplot", "seaborn", "scipy.stats", "scipy.stats", "warnings", "matplotlib.patches"], "imported_by": [], "coupling_score": 16}, "matplotlib.patches": {"imports": [], "imported_by": ["scripts.analysis.investigate_htf_structural_inheritance", "scripts.analysis.run_archaeology_demonstration", "scripts.analysis.run_archaeology_demonstration", "scripts.utilities.pattern_correlation_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 7}, "scripts.analysis.investigate_liquidity_sweep_catalyst": {"imports": ["json", "pickle", "numpy", "pandas", "datetime", "datetime", "glob", "pathlib", "collections", "collections", "matplotlib.pyplot", "seaborn", "warnings"], "imported_by": [], "coupling_score": 13}, "scripts.analysis.investigate_pattern_subarchitecture": {"imports": ["json", "pickle", "numpy", "glob", "pathlib", "collections", "collections", "sklearn.cluster", "sklearn.decomposition", "sklearn.preprocessing", "matplotlib.pyplot", "sklearn.metrics"], "imported_by": [], "coupling_score": 12}, "sklearn.cluster": {"imports": [], "imported_by": ["scripts.analysis.investigate_pattern_subarchitecture", "scripts.utilities.pattern_intelligence"], "coupling_score": 2}, "sklearn.decomposition": {"imports": [], "imported_by": ["scripts.analysis.investigate_pattern_subarchitecture"], "coupling_score": 1}, "sklearn.preprocessing": {"imports": [], "imported_by": ["scripts.analysis.investigate_pattern_subarchitecture", "scripts.utilities.pattern_intelligence"], "coupling_score": 2}, "scripts.analysis.phase2_feature_pipeline_enhancement": {"imports": ["json", "os", "datetime", "datetime", "pathlib", "numpy", "typing", "typing", "typing", "typing", "typing", "logging"], "imported_by": [], "coupling_score": 12}, "scripts.analysis.phase2_validation_framework": {"imports": ["json", "numpy", "pathlib", "typing", "typing", "typing", "typing", "logging", "collections", "matplotlib.pyplot", "seaborn", "pandas"], "imported_by": [], "coupling_score": 12}, "scripts.analysis.phase2_validation_summary": {"imports": ["json", "os", "pathlib", "numpy"], "imported_by": [], "coupling_score": 4}, "scripts.analysis.phase4_full_scale_archaeological_discovery": {"imports": ["json", "glob", "time", "pathlib", "typing", "typing", "typing", "learning.enhanced_graph_builder", "learning.tgat_discovery", "traceback"], "imported_by": [], "coupling_score": 10}, "learning.enhanced_graph_builder": {"imports": [], "imported_by": ["scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.quick_pattern_discovery", "scripts.analysis.run_full_session_analysis", "scripts.utilities.debug_features", "scripts.utilities.debug_graph_structure", "scripts.utilities.example_htf_output", "scripts.utilities.graph_builder_diagnostic", "scripts.utilities.pattern_monitor"], "coupling_score": 13}, "learning.tgat_discovery": {"imports": [], "imported_by": ["scripts.analysis.phase4_full_scale_archaeological_discovery", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_head_analysis", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4b_attention_verification", "scripts.analysis.phase4c_temporal_resonance", "scripts.analysis.phase4d_profile_run", "scripts.analysis.phase5_direct_tgat_validation", "scripts.analysis.phase5_enhanced_session_validation", "scripts.utilities.debug_tgat_init", "scripts.utilities.ironforge_discovery_sdk"], "coupling_score": 11}, "scripts.analysis.phase4b_attention_head_analysis": {"imports": ["json", "torch", "numpy", "pathlib", "typing", "typing", "typing", "typing", "learning.enhanced_graph_builder", "learning.tgat_discovery", "learning.tgat_discovery", "glob", "traceback"], "imported_by": [], "coupling_score": 13}, "scripts.analysis.phase4b_attention_verification": {"imports": ["json", "torch", "numpy", "glob", "pathlib", "typing", "typing", "typing", "typing", "learning.enhanced_graph_builder", "learning.tgat_discovery", "learning.tgat_discovery", "traceback"], "imported_by": [], "coupling_score": 13}, "scripts.analysis.phase4c_temporal_resonance": {"imports": ["json", "glob", "torch", "numpy", "pathlib", "typing", "typing", "typing", "typing", "datetime", "datetime", "collections", "learning.enhanced_graph_builder", "learning.tgat_discovery", "traceback"], "imported_by": [], "coupling_score": 15}, "scripts.analysis.phase4d_profile_run": {"imports": ["json", "glob", "time", "tracemalloc", "psutil", "torch", "csv", "pathlib", "typing", "typing", "typing", "typing", "learning.enhanced_graph_builder", "learning.tgat_discovery", "traceback"], "imported_by": [], "coupling_score": 15}, "tracemalloc": {"imports": [], "imported_by": ["scripts.analysis.phase4d_profile_run"], "coupling_score": 1}, "csv": {"imports": [], "imported_by": ["scripts.analysis.phase4d_profile_run"], "coupling_score": 1}, "scripts.analysis.phase5_archaeological_discovery_validation": {"imports": ["os", "sys", "json", "time", "pathlib", "datetime", "typing", "typing", "typing", "typing", "collections", "collections", "iron_core.performance.container"], "imported_by": [], "coupling_score": 13}, "scripts.analysis.phase5_direct_tgat_validation": {"imports": ["os", "sys", "json", "torch", "numpy", "pathlib", "datetime", "typing", "typing", "typing", "collections", "learning.tgat_discovery", "learning.enhanced_graph_builder", "traceback"], "imported_by": [], "coupling_score": 14}, "scripts.analysis.phase5_enhanced_session_validation": {"imports": ["os", "sys", "json", "torch", "numpy", "pathlib", "typing", "typing", "typing", "collections", "learning.tgat_discovery"], "imported_by": [], "coupling_score": 11}, "scripts.analysis.process_all_sessions": {"imports": ["orchestrator", "glob", "time", "pathlib", "pickle"], "imported_by": [], "coupling_score": 5}, "scripts.analysis.quick_pattern_discovery": {"imports": ["os", "json", "<PERSON><PERSON><PERSON><PERSON>", "datetime", "learning.enhanced_graph_builder", "logging"], "imported_by": [], "coupling_score": 6}, "scripts.analysis.real_pattern_finder": {"imports": ["glob", "json", "pickle", "os", "re"], "imported_by": [], "coupling_score": 5}, "scripts.analysis.run_archaeology_demonstration": {"imports": ["os", "sys", "json", "time", "numpy", "pathlib", "typing", "typing", "typing", "datetime", "datetime", "dataclasses", "dataclasses", "enum", "matplotlib.pyplot", "matplotlib.patches", "matplotlib.patches", "seaborn", "collections", "traceback"], "imported_by": [], "coupling_score": 20}, "scripts.analysis.run_contaminated_session_enhancement": {"imports": ["json", "phase2_feature_pipeline_enhancement", "logging"], "imported_by": [], "coupling_score": 3}, "phase2_feature_pipeline_enhancement": {"imports": [], "imported_by": ["scripts.analysis.run_contaminated_session_enhancement"], "coupling_score": 1}, "scripts.analysis.run_direct_discovery": {"imports": ["json", "glob", "os"], "imported_by": [], "coupling_score": 3}, "scripts.analysis.run_enhanced_adapter_demonstration": {"imports": ["sys", "json", "time", "pathlib", "typing", "typing", "typing", "datetime", "analysis.enhanced_session_adapter", "analysis.enhanced_session_adapter", "analysis.enhanced_session_adapter", "unittest.mock", "sys", "unittest.mock"], "imported_by": [], "coupling_score": 14}, "analysis.enhanced_session_adapter": {"imports": [], "imported_by": ["scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration", "scripts.utilities.debug_lattice", "scripts.utilities.lattice_population_runner"], "coupling_score": 5}, "unittest.mock": {"imports": [], "imported_by": ["scripts.analysis.run_enhanced_adapter_demonstration", "scripts.analysis.run_enhanced_adapter_demonstration"], "coupling_score": 2}, "scripts.analysis.run_full_archaeology_discovery": {"imports": ["os", "sys", "json", "time", "logging", "pathlib", "typing", "typing", "typing", "datetime", "analysis.broad_spectrum_archaeology", "analysis.broad_spectrum_archaeology", "analysis.timeframe_lattice_mapper", "analysis.timeframe_lattice_mapper", "analysis.temporal_clustering_engine", "analysis.temporal_clustering_engine", "analysis.structural_link_analyzer", "analysis.structural_link_analyzer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "imported_by": [], "coupling_score": 20}, "analysis.broad_spectrum_archaeology": {"imports": [], "imported_by": ["scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_archaeology_discovery", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 4}, "analysis.timeframe_lattice_mapper": {"imports": [], "imported_by": ["scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_archaeology_discovery", "scripts.utilities.debug_lattice", "scripts.utilities.lattice_population_runner", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 8}, "analysis.temporal_clustering_engine": {"imports": [], "imported_by": ["scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_archaeology_discovery", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 4}, "analysis.structural_link_analyzer": {"imports": [], "imported_by": ["scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_archaeology_discovery", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 6}, "visualizations.lattice_visualizer": {"imports": ["json", "numpy", "pandas", "matplotlib.pyplot", "matplotlib.patches", "matplotlib.patches", "matplotlib.patches", "seaborn", "typing", "typing", "typing", "typing", "typing", "collections", "collections", "dataclasses", "pathlib", "logging", "datetime", "warnings", "plotly.graph_objects", "plotly.express", "plotly.subplots", "plotly.offline", "analysis.broad_spectrum_archaeology", "analysis.broad_spectrum_archaeology", "analysis.timeframe_lattice_mapper", "analysis.timeframe_lattice_mapper", "analysis.timeframe_lattice_mapper", "analysis.timeframe_lattice_mapper", "analysis.temporal_clustering_engine", "analysis.temporal_clustering_engine", "analysis.structural_link_analyzer", "analysis.structural_link_analyzer", "analysis.structural_link_analyzer", "analysis.structural_link_analyzer", "sys", "pathlib", "broad_spectrum_archaeology", "broad_spectrum_archaeology", "timeframe_lattice_mapper", "timeframe_lattice_mapper", "timeframe_lattice_mapper", "timeframe_lattice_mapper", "temporal_clustering_engine", "temporal_clustering_engine", "structural_link_analyzer", "structural_link_analyzer", "structural_link_analyzer", "structural_link_analyzer", "networkx"], "imported_by": ["scripts.analysis.run_full_archaeology_discovery", "scripts.analysis.run_full_archaeology_discovery"], "coupling_score": 53}, "scripts.analysis.run_full_scale_discovery": {"imports": ["os", "sys", "time", "json", "glob", "datetime", "pathlib", "typing", "typing", "typing", "orchestrator", "traceback", "traceback"], "imported_by": [], "coupling_score": 13}, "scripts.analysis.run_full_session_analysis": {"imports": ["os", "json", "pandas", "numpy", "matplotlib.pyplot", "seaborn", "datetime", "datetime", "pathlib", "logging", "typing", "typing", "typing", "typing", "warnings", "learning.enhanced_graph_builder", "pattern_correlation_visualizer"], "imported_by": [], "coupling_score": 17}, "pattern_correlation_visualizer": {"imports": [], "imported_by": ["scripts.analysis.run_full_session_analysis"], "coupling_score": 1}, "scripts.analysis.run_htf_orchestrator": {"imports": ["os", "glob", "orchestrator", "traceback"], "imported_by": [], "coupling_score": 4}, "scripts.analysis.run_manual_discovery": {"imports": ["sys", "IRONFORGE.unicode_fix", "IRONFORGE.orchestrator", "json"], "imported_by": [], "coupling_score": 4}, "IRONFORGE.unicode_fix": {"imports": [], "imported_by": ["scripts.analysis.run_manual_discovery"], "coupling_score": 1}, "IRONFORGE.orchestrator": {"imports": [], "imported_by": ["scripts.analysis.run_manual_discovery"], "coupling_score": 1}, "scripts.data_processing.enhanced_session_relativity_processor": {"imports": ["json", "glob", "os", "numpy", "datetime", "datetime", "typing", "typing", "typing", "typing", "pathlib"], "imported_by": [], "coupling_score": 11}, "scripts.data_processing.enhanced_sessions_price_relativity_processor": {"imports": ["json", "glob", "os", "numpy", "datetime", "datetime", "typing", "typing", "typing", "typing", "pathlib"], "imported_by": [], "coupling_score": 11}, "scripts.data_processing.price_field_standardizer": {"imports": ["json", "os", "pathlib", "typing", "typing", "typing"], "imported_by": [], "coupling_score": 6}, "scripts.data_processing.price_relativity_generator": {"imports": ["json", "glob", "os", "numpy", "datetime", "datetime", "typing", "typing", "typing", "typing", "pathlib"], "imported_by": [], "coupling_score": 11}, "scripts.data_processing.session_quality_assessor": {"imports": ["json", "os", "datetime", "datetime", "typing", "typing", "typing", "typing", "typing", "pathlib", "statistics"], "imported_by": [], "coupling_score": 11}, "statistics": {"imports": [], "imported_by": ["scripts.data_processing.session_quality_assessor"], "coupling_score": 1}, "scripts.data_processing.unicode_fix": {"imports": ["json", "os", "glob"], "imported_by": [], "coupling_score": 3}, "scripts.utilities.benchmark_performance": {"imports": ["time", "sys", "importlib", "pathlib", "ironforge.integration.ironforge_container"], "imported_by": [], "coupling_score": 5}, "scripts.utilities.daily_discovery_workflows": {"imports": ["os", "sys", "json", "time", "pathlib", "typing", "typing", "typing", "typing", "datetime", "datetime", "dataclasses", "pandas", "numpy", "ironforge_discovery_sdk", "ironforge_discovery_sdk", "pattern_intelligence", "pattern_intelligence"], "imported_by": [], "coupling_score": 18}, "ironforge_discovery_sdk": {"imports": [], "imported_by": ["scripts.utilities.daily_discovery_workflows", "scripts.utilities.daily_discovery_workflows", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence", "scripts.utilities.pattern_intelligence"], "coupling_score": 5}, "pattern_intelligence": {"imports": [], "imported_by": ["scripts.utilities.daily_discovery_workflows", "scripts.utilities.daily_discovery_workflows"], "coupling_score": 2}, "scripts.utilities.debug_features": {"imports": ["json", "torch", "learning.enhanced_graph_builder", "traceback"], "imported_by": [], "coupling_score": 4}, "scripts.utilities.debug_graph_structure": {"imports": ["json", "pathlib", "learning.enhanced_graph_builder", "traceback"], "imported_by": [], "coupling_score": 4}, "scripts.utilities.debug_lattice": {"imports": ["sys", "json", "pathlib", "analysis.timeframe_lattice_mapper", "analysis.enhanced_session_adapter", "traceback"], "imported_by": [], "coupling_score": 6}, "scripts.utilities.debug_tgat_init": {"imports": ["torch", "learning.tgat_discovery"], "imported_by": [], "coupling_score": 2}, "scripts.utilities.example_htf_output": {"imports": ["json", "learning.enhanced_graph_builder"], "imported_by": [], "coupling_score": 2}, "scripts.utilities.graph_builder_diagnostic": {"imports": ["sys", "os", "json", "pathlib", "learning.enhanced_graph_builder", "traceback", "traceback"], "imported_by": [], "coupling_score": 7}, "scripts.utilities.htf_builder": {"imports": ["json", "glob", "datetime", "datetime", "collections", "math", "os"], "imported_by": [], "coupling_score": 7}, "scripts.utilities.ironforge_discovery_sdk": {"imports": ["os", "sys", "json", "time", "logging", "pathlib", "typing", "typing", "typing", "typing", "typing", "typing", "datetime", "datetime", "dataclasses", "collections", "collections", "concurrent.futures", "concurrent.futures", "torch", "numpy", "pandas", "orchestrator", "learning.tgat_discovery"], "imported_by": [], "coupling_score": 24}, "scripts.utilities.lattice_population_runner": {"imports": ["sys", "json", "time", "pathlib", "typing", "typing", "typing", "typing", "datetime", "glob", "analysis.timeframe_lattice_mapper", "analysis.enhanced_session_adapter"], "imported_by": [], "coupling_score": 12}, "scripts.utilities.pattern_correlation_visualizer": {"imports": ["numpy", "pandas", "matplotlib.pyplot", "matplotlib.patches", "datetime", "datetime", "typing", "typing", "typing", "typing", "seaborn", "pathlib"], "imported_by": [], "coupling_score": 12}, "scripts.utilities.pattern_intelligence": {"imports": ["os", "sys", "json", "numpy", "pandas", "pathlib", "typing", "typing", "typing", "typing", "typing", "datetime", "datetime", "dataclasses", "dataclasses", "collections", "collections", "scipy", "sklearn.cluster", "sklearn.preprocessing", "ironforge_discovery_sdk", "ironforge_discovery_sdk", "ironforge_discovery_sdk"], "imported_by": [], "coupling_score": 23}, "scripts.utilities.pattern_monitor": {"imports": ["os", "json", "time", "<PERSON><PERSON><PERSON><PERSON>", "datetime", "learning.enhanced_graph_builder", "logging"], "imported_by": [], "coupling_score": 7}, "scripts.utilities.performance_monitor": {"imports": ["time", "psutil", "json", "logging", "datetime", "typing", "typing", "typing", "typing", "typing", "dataclasses", "numpy", "pathlib", "<PERSON><PERSON><PERSON><PERSON>", "time"], "imported_by": [], "coupling_score": 15}, "setup": {"imports": ["setuptools", "setuptools", "os"], "imported_by": [], "coupling_score": 3}, "plotly.graph_objects": {"imports": [], "imported_by": ["visualizations.lattice_visualizer"], "coupling_score": 1}, "plotly.express": {"imports": [], "imported_by": ["visualizations.lattice_visualizer"], "coupling_score": 1}, "plotly.subplots": {"imports": [], "imported_by": ["visualizations.lattice_visualizer"], "coupling_score": 1}, "plotly.offline": {"imports": [], "imported_by": ["visualizations.lattice_visualizer"], "coupling_score": 1}, "broad_spectrum_archaeology": {"imports": [], "imported_by": ["visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 2}, "timeframe_lattice_mapper": {"imports": [], "imported_by": ["visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 4}, "temporal_clustering_engine": {"imports": [], "imported_by": ["visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 2}, "structural_link_analyzer": {"imports": [], "imported_by": ["visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer", "visualizations.lattice_visualizer"], "coupling_score": 4}}, "cross_engine_flows": {"module_engine_classification": {"__init__": "unknown", "sys": "unknown", "os": "unknown", "config": "integration", "json": "unknown", "pathlib": "unknown", "typing": "unknown", "logging": "unknown", "data_migration.batch_migrate_graphs": "learning", "argparse": "unknown", "shutil": "utilities", "datetime": "unknown", "concurrent.futures": "unknown", "traceback": "unknown", "schema_normalizer": "unknown", "data_migration.schema_normalizer": "utilities", "extract_lattice_summary": "analysis", "iron_core.__init__": "unknown", "iron_core.performance.container": "integration", "iron_core.performance.lazy_loader": "unknown", "iron_core.mathematical.adaptive_rg_optimizer": "unknown", "numpy": "unknown", "dataclasses": "unknown", "scipy": "unknown", "scipy.stats": "unknown", "warnings": "unknown", "iron_core.mathematical.cascade_classifier": "unknown", "enum": "unknown", "iron_core.mathematical.constraints": "unknown", "decimal": "unknown", "math": "unknown", "iron_core.mathematical.fisher_information_monitor": "unknown", "iron_core.mathematical.grammar_fisher_correlator": "unknown", "collections": "unknown", "iron_core.mathematical.hawkes_engine": "unknown", "iron_core.mathematical.invariants": "unknown", "ast": "unknown", "hashlib": "unknown", "inspect": "unknown", "time": "unknown", "iron_core.mathematical.mathematical_hooks": "unknown", "abc": "unknown", "asyncio": "unknown", "iron_core.mathematical.mathematical_layers.api_interface": "unknown", "iron_core.mathematical.mathematical_layers.integration_layer": "integration", "fastapi": "unknown", "fastapi.responses": "unknown", "pydantic": "unknown", "uvicorn": "unknown", "iron_core.mathematical.mathematical_layers.validation_framework": "validation", "iron_core.mathematical.mathematical_layers.core_algorithms": "unknown", "scipy.fft": "unknown", "scipy.optimize": "unknown", "iron_core.mathematical.mathematical_layers.theory_abstraction": "unknown", "psutil": "utilities", "iron_core.mathematical.rg_scaler_production": "synthesis", "hypothesis": "unknown", "iron_core.mathematical.scaling_patterns": "unknown", "threading": "unknown", "contextlib": "unknown", "iron_core.mathematical.temporal_correlator": "unknown", "iron_core.performance.__init__": "unknown", "weakref": "unknown", "functools": "unknown", "importlib": "unknown", "iron_core.setup": "unknown", "setuptools": "unknown", "ironforge.__init__": "unknown", "ironforge.integration.ironforge_container": "integration", "ironforge.analysis.__init__": "analysis", "ironforge.analysis.timeframe_lattice_mapper": "analysis", "ironforge.analysis.enhanced_session_adapter": "analysis", "ironforge.analysis.broad_spectrum_archaeology": "analysis", "ironforge.analysis.fpfvg.chain_builder": "analysis", "ironforge.analysis.fpfvg.features": "analysis", "ironforge.analysis.fpfvg.runner": "analysis", "ironforge.analysis.fpfvg.validators": "analysis", "ironforge.analysis.fpfvg_network_analyzer": "analysis", "pandas": "unknown", "ironforge.analysis.fpfvg_redelivery_lattice": "analysis", "ironforge.analysis.global_lattice_builder": "analysis", "ironforge.analysis.lattice_terrain_analyzer": "analysis", "ironforge.analysis.refined_sweep_detector": "analysis", "ironforge.analysis.specialized_lattice_builder": "analysis", "ironforge.analysis.weekly_daily_sweep_cascade_analyzer": "analysis", "ironforge.analysis.weekly_daily_sweep_cascade_lattice": "analysis", "ironforge.contracts": "unknown", "ironforge.data_engine.parquet_writer": "unknown", "pyarrow": "unknown", "pyarrow.parquet": "unknown", "ironforge.data_engine.schemas": "unknown", "ironforge.graph_builder.igraph_builder": "learning", "igraph": "learning", "ironforge.graph_builder.pyg_converters": "learning", "torch": "unknown", "torch_geometric.data": "unknown", "ironforge.integration.__init__": "integration", "iron_core.performance": "unknown", "ironforge.learning.enhanced_graph_builder": "learning", "ironforge.learning.tgat_discovery": "learning", "ironforge.synthesis.pattern_graduation": "synthesis", "ironforge.learning.__init__": "learning", "ironforge.learning.simple_event_clustering": "learning", "ironforge.learning.regime_segmentation": "learning", "ironforge.learning.discovery_pipeline": "learning", "__future__": "unknown", "collections.abc": "unknown", "torch_geometric.loader": "unknown", "networkx": "unknown", "torch.nn": "unknown", "torch.nn.functional": "unknown", "ironforge.metrics.__init__": "unknown", "ironforge.metrics.confluence": "unknown", "ironforge.motifs.__init__": "unknown", "ironforge.motifs.cards": "unknown", "ironforge.motifs.scanner": "unknown", "bisect": "unknown", "ironforge.reporting.__init__": "unknown", "ironforge.reporting.confluence": "unknown", "ironforge.reporting.heatmap": "unknown", "PIL": "unknown", "ironforge.reporting.html": "unknown", "base64": "unknown", "io": "unknown", "ironforge.reporting.writer": "unknown", "ironforge.scripts.prepare_motifs_input": "utilities", "ironforge.sdk.__main__": "unknown", "ironforge.sdk.cli": "unknown", "ironforge.validation.runner": "validation", "ironforge.reporting": "unknown", "ironforge.synthesis.__init__": "synthesis", "ironforge.synthesis.production_graduation": "synthesis", "ironforge.utilities.__init__": "utilities", "ironforge.utilities.performance_monitor": "utilities", "ironforge.validation.__init__": "validation", "ironforge.validation.statistical_validator": "validation", "ironforge.validation.regression_tester": "validation", "ironforge.validation.performance_monitor": "validation", "ironforge.validation.integration_tester": "integration", "ironforge.validation.splits": "validation", "ironforge.validation.controls": "validation", "ironforge.validation.metrics": "validation", "sklearn.metrics": "unknown", "gc": "unknown", "orchestrator": "unknown", "pickle": "unknown", "performance_monitor": "unknown", "learning.graph_builder": "learning", "run_fpfvg_network_analysis": "analysis", "run_fpfvg_network_analysis_simple": "analysis", "run_fpfvg_redelivery_lattice": "analysis", "run_global_lattice": "analysis", "run_specialized_lattice": "analysis", "run_terrain_analysis": "analysis", "run_weekly_daily_cascade_lattice": "analysis", "run_weekly_daily_sweep_cascade_step_3b": "unknown", "run_weekly_daily_sweep_cascade_step_3b_refined": "unknown", "run_working_cascade_analysis": "analysis", "scripts.analysis.analyze_concrete_patterns": "analysis", "glob": "unknown", "scripts.analysis.analyze_nypm_patterns": "analysis", "re": "unknown", "scripts.analysis.bridge_node_mapper": "analysis", "scripts.analysis.complete_phase2_enhancement": "analysis", "random": "unknown", "scripts.analysis.comprehensive_discovery_report": "analysis", "scripts.analysis.enrichment_analyzer": "analysis", "scripts.analysis.explore_discoveries": "analysis", "scripts.analysis.investigate_causal_event_chains": "analysis", "matplotlib.pyplot": "unknown", "seaborn": "unknown", "itertools": "unknown", "scripts.analysis.investigate_cross_session_synchronization": "analysis", "scripts.analysis.investigate_htf_structural_inheritance": "analysis", "matplotlib.patches": "unknown", "scripts.analysis.investigate_liquidity_sweep_catalyst": "analysis", "scripts.analysis.investigate_pattern_subarchitecture": "analysis", "sklearn.cluster": "unknown", "sklearn.decomposition": "unknown", "sklearn.preprocessing": "unknown", "scripts.analysis.phase2_feature_pipeline_enhancement": "analysis", "scripts.analysis.phase2_validation_framework": "analysis", "scripts.analysis.phase2_validation_summary": "analysis", "scripts.analysis.phase4_full_scale_archaeological_discovery": "analysis", "learning.enhanced_graph_builder": "learning", "learning.tgat_discovery": "learning", "scripts.analysis.phase4b_attention_head_analysis": "analysis", "scripts.analysis.phase4b_attention_verification": "analysis", "scripts.analysis.phase4c_temporal_resonance": "analysis", "scripts.analysis.phase4d_profile_run": "analysis", "tracemalloc": "unknown", "csv": "unknown", "scripts.analysis.phase5_archaeological_discovery_validation": "analysis", "scripts.analysis.phase5_direct_tgat_validation": "analysis", "scripts.analysis.phase5_enhanced_session_validation": "analysis", "scripts.analysis.process_all_sessions": "analysis", "scripts.analysis.quick_pattern_discovery": "analysis", "scripts.analysis.real_pattern_finder": "analysis", "scripts.analysis.run_archaeology_demonstration": "analysis", "scripts.analysis.run_contaminated_session_enhancement": "analysis", "phase2_feature_pipeline_enhancement": "unknown", "scripts.analysis.run_direct_discovery": "analysis", "scripts.analysis.run_enhanced_adapter_demonstration": "analysis", "analysis.enhanced_session_adapter": "analysis", "unittest.mock": "validation", "scripts.analysis.run_full_archaeology_discovery": "analysis", "analysis.broad_spectrum_archaeology": "analysis", "analysis.timeframe_lattice_mapper": "analysis", "analysis.temporal_clustering_engine": "analysis", "analysis.structural_link_analyzer": "analysis", "visualizations.lattice_visualizer": "analysis", "scripts.analysis.run_full_scale_discovery": "analysis", "scripts.analysis.run_full_session_analysis": "analysis", "pattern_correlation_visualizer": "unknown", "scripts.analysis.run_htf_orchestrator": "analysis", "scripts.analysis.run_manual_discovery": "analysis", "IRONFORGE.unicode_fix": "unknown", "IRONFORGE.orchestrator": "unknown", "scripts.data_processing.enhanced_session_relativity_processor": "analysis", "scripts.data_processing.enhanced_sessions_price_relativity_processor": "analysis", "scripts.data_processing.price_field_standardizer": "utilities", "scripts.data_processing.price_relativity_generator": "utilities", "scripts.data_processing.session_quality_assessor": "analysis", "statistics": "unknown", "scripts.data_processing.unicode_fix": "utilities", "scripts.utilities.benchmark_performance": "utilities", "scripts.utilities.daily_discovery_workflows": "learning", "ironforge_discovery_sdk": "learning", "pattern_intelligence": "unknown", "scripts.utilities.debug_features": "utilities", "scripts.utilities.debug_graph_structure": "learning", "scripts.utilities.debug_lattice": "analysis", "scripts.utilities.debug_tgat_init": "learning", "scripts.utilities.example_htf_output": "utilities", "scripts.utilities.graph_builder_diagnostic": "learning", "scripts.utilities.htf_builder": "utilities", "scripts.utilities.ironforge_discovery_sdk": "learning", "scripts.utilities.lattice_population_runner": "analysis", "scripts.utilities.pattern_correlation_visualizer": "utilities", "scripts.utilities.pattern_intelligence": "utilities", "scripts.utilities.pattern_monitor": "utilities", "scripts.utilities.performance_monitor": "utilities", "setup": "unknown", "plotly.graph_objects": "learning", "plotly.express": "unknown", "plotly.subplots": "unknown", "plotly.offline": "unknown", "broad_spectrum_archaeology": "unknown", "timeframe_lattice_mapper": "analysis", "temporal_clustering_engine": "unknown", "structural_link_analyzer": "unknown"}, "cross_engine_dependencies": {"learning -> utilities": [{"from_module": "data_migration.batch_migrate_graphs", "to_module": "shutil", "import_type": "import"}], "integration -> synthesis": [{"from_module": "iron_core.mathematical.mathematical_layers.integration_layer", "to_module": "iron_core.mathematical.rg_scaler_production", "import_type": "from_import"}, {"from_module": "ironforge.integration.ironforge_container", "to_module": "ironforge.synthesis.pattern_graduation", "import_type": "from_import"}], "validation -> utilities": [{"from_module": "iron_core.mathematical.mathematical_layers.validation_framework", "to_module": "psutil", "import_type": "import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "psutil", "import_type": "import"}], "integration -> learning": [{"from_module": "ironforge.integration.ironforge_container", "to_module": "ironforge.learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "ironforge.integration.ironforge_container", "to_module": "ironforge.learning.tgat_discovery", "import_type": "from_import"}], "analysis -> integration": [{"from_module": "ironforge.analysis.fpfvg.runner", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.fpfvg_network_analyzer", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.fpfvg_redelivery_lattice", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.global_lattice_builder", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.global_lattice_builder", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.lattice_terrain_analyzer", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.lattice_terrain_analyzer", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.refined_sweep_detector", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.specialized_lattice_builder", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.specialized_lattice_builder", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "to_module": "config", "import_type": "from_import"}, {"from_module": "run_fpfvg_network_analysis_simple", "to_module": "config", "import_type": "from_import"}, {"from_module": "run_working_cascade_analysis", "to_module": "config", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase5_archaeological_discovery_validation", "to_module": "iron_core.performance.container", "import_type": "from_import"}], "learning -> integration": [{"from_module": "ironforge.learning.discovery_pipeline", "to_module": "ironforge.integration.ironforge_container", "import_type": "from_import"}], "synthesis -> integration": [{"from_module": "ironforge.synthesis.production_graduation", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.synthesis.production_graduation", "to_module": "config", "import_type": "from_import"}], "validation -> integration": [{"from_module": "ironforge.validation.__init__", "to_module": "ironforge.validation.integration_tester", "import_type": "from_import"}, {"from_module": "ironforge.validation.statistical_validator", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.validation.statistical_validator", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "config", "import_type": "from_import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "ironforge.integration.ironforge_container", "import_type": "from_import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "ironforge.integration.ironforge_container", "import_type": "from_import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "ironforge.integration.ironforge_container", "import_type": "from_import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "ironforge.integration.ironforge_container", "import_type": "from_import"}, {"from_module": "ironforge.validation.performance_monitor", "to_module": "ironforge.integration.ironforge_container", "import_type": "from_import"}], "validation -> learning": [{"from_module": "ironforge.validation.performance_monitor", "to_module": "ironforge.learning.enhanced_graph_builder", "import_type": "from_import"}], "validation -> synthesis": [{"from_module": "ironforge.validation.performance_monitor", "to_module": "ironforge.synthesis.pattern_graduation", "import_type": "from_import"}], "analysis -> learning": [{"from_module": "scripts.analysis.phase4_full_scale_archaeological_discovery", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4_full_scale_archaeological_discovery", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4b_attention_head_analysis", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4b_attention_head_analysis", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4b_attention_head_analysis", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4b_attention_verification", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4b_attention_verification", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4b_attention_verification", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4c_temporal_resonance", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4c_temporal_resonance", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4d_profile_run", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase4d_profile_run", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase5_direct_tgat_validation", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase5_direct_tgat_validation", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.analysis.phase5_enhanced_session_validation", "to_module": "learning.tgat_discovery", "import_type": "from_import"}, {"from_module": "scripts.analysis.quick_pattern_discovery", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "visualizations.lattice_visualizer", "to_module": "plotly.graph_objects", "import_type": "import"}, {"from_module": "scripts.analysis.run_full_session_analysis", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}], "analysis -> utilities": [{"from_module": "scripts.analysis.phase4d_profile_run", "to_module": "psutil", "import_type": "import"}], "analysis -> validation": [{"from_module": "scripts.analysis.run_enhanced_adapter_demonstration", "to_module": "unittest.mock", "import_type": "from_import"}, {"from_module": "scripts.analysis.run_enhanced_adapter_demonstration", "to_module": "unittest.mock", "import_type": "from_import"}], "utilities -> integration": [{"from_module": "scripts.utilities.benchmark_performance", "to_module": "ironforge.integration.ironforge_container", "import_type": "from_import"}], "utilities -> learning": [{"from_module": "scripts.utilities.debug_features", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.utilities.example_htf_output", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}, {"from_module": "scripts.utilities.pattern_intelligence", "to_module": "ironforge_discovery_sdk", "import_type": "from_import"}, {"from_module": "scripts.utilities.pattern_intelligence", "to_module": "ironforge_discovery_sdk", "import_type": "from_import"}, {"from_module": "scripts.utilities.pattern_intelligence", "to_module": "ironforge_discovery_sdk", "import_type": "from_import"}, {"from_module": "scripts.utilities.pattern_monitor", "to_module": "learning.enhanced_graph_builder", "import_type": "from_import"}]}, "flow_summary": {"learning -> utilities": 1, "integration -> synthesis": 2, "validation -> utilities": 2, "integration -> learning": 2, "analysis -> integration": 15, "learning -> integration": 1, "synthesis -> integration": 2, "validation -> integration": 10, "validation -> learning": 1, "validation -> synthesis": 1, "analysis -> learning": 18, "analysis -> utilities": 1, "analysis -> validation": 2, "utilities -> integration": 1, "utilities -> learning": 6}, "problematic_flows": [{"flow": "analysis -> integration", "count": 15, "issue": "high_coupling", "recommendation": "Consider reducing cross-engine dependencies"}, {"flow": "analysis -> learning", "count": 18, "issue": "high_coupling", "recommendation": "Consider reducing cross-engine dependencies"}], "recommended_flow_pattern": "analysis -> learning -> synthesis"}, "circular_dependencies": [], "coupling_metrics": {"afferent_coupling": {"__init__": 0, "sys": 46, "os": 39, "config": 21, "json": 82, "pathlib": 81, "typing": 324, "logging": 69, "data_migration.batch_migrate_graphs": 0, "argparse": 7, "shutil": 1, "datetime": 96, "concurrent.futures": 5, "traceback": 16, "schema_normalizer": 3, "data_migration.schema_normalizer": 0, "extract_lattice_summary": 0, "iron_core.__init__": 0, "iron_core.performance.container": 7, "iron_core.performance.lazy_loader": 11, "iron_core.mathematical.adaptive_rg_optimizer": 0, "numpy": 68, "dataclasses": 40, "scipy": 6, "scipy.stats": 11, "warnings": 9, "iron_core.mathematical.cascade_classifier": 0, "enum": 9, "iron_core.mathematical.constraints": 1, "decimal": 5, "math": 4, "iron_core.mathematical.fisher_information_monitor": 1, "iron_core.mathematical.grammar_fisher_correlator": 0, "collections": 42, "iron_core.mathematical.hawkes_engine": 1, "iron_core.mathematical.invariants": 0, "ast": 1, "hashlib": 1, "inspect": 1, "time": 25, "iron_core.mathematical.mathematical_hooks": 4, "abc": 14, "asyncio": 8, "iron_core.mathematical.mathematical_layers.api_interface": 0, "iron_core.mathematical.mathematical_layers.integration_layer": 3, "fastapi": 5, "fastapi.responses": 1, "pydantic": 2, "uvicorn": 1, "iron_core.mathematical.mathematical_layers.validation_framework": 2, "iron_core.mathematical.mathematical_layers.core_algorithms": 5, "scipy.fft": 1, "scipy.optimize": 2, "iron_core.mathematical.mathematical_layers.theory_abstraction": 4, "psutil": 6, "iron_core.mathematical.rg_scaler_production": 1, "hypothesis": 4, "iron_core.mathematical.scaling_patterns": 0, "threading": 3, "contextlib": 3, "iron_core.mathematical.temporal_correlator": 0, "iron_core.performance.__init__": 0, "weakref": 1, "functools": 1, "importlib": 2, "iron_core.setup": 0, "setuptools": 4, "ironforge.__init__": 0, "ironforge.integration.ironforge_container": 13, "ironforge.analysis.__init__": 0, "ironforge.analysis.timeframe_lattice_mapper": 2, "ironforge.analysis.enhanced_session_adapter": 1, "ironforge.analysis.broad_spectrum_archaeology": 1, "ironforge.analysis.fpfvg.chain_builder": 4, "ironforge.analysis.fpfvg.features": 10, "ironforge.analysis.fpfvg.runner": 0, "ironforge.analysis.fpfvg.validators": 4, "ironforge.analysis.fpfvg_network_analyzer": 1, "pandas": 15, "ironforge.analysis.fpfvg_redelivery_lattice": 1, "ironforge.analysis.global_lattice_builder": 1, "ironforge.analysis.lattice_terrain_analyzer": 1, "ironforge.analysis.refined_sweep_detector": 1, "ironforge.analysis.specialized_lattice_builder": 1, "ironforge.analysis.weekly_daily_sweep_cascade_analyzer": 3, "ironforge.analysis.weekly_daily_sweep_cascade_lattice": 1, "ironforge.contracts": 0, "ironforge.data_engine.parquet_writer": 0, "pyarrow": 1, "pyarrow.parquet": 1, "ironforge.data_engine.schemas": 3, "ironforge.graph_builder.igraph_builder": 0, "igraph": 1, "ironforge.graph_builder.pyg_converters": 0, "torch": 17, "torch_geometric.data": 2, "ironforge.integration.__init__": 0, "iron_core.performance": 1, "ironforge.learning.enhanced_graph_builder": 4, "ironforge.learning.tgat_discovery": 3, "ironforge.synthesis.pattern_graduation": 4, "ironforge.learning.__init__": 0, "ironforge.learning.simple_event_clustering": 2, "ironforge.learning.regime_segmentation": 1, "ironforge.learning.discovery_pipeline": 1, "__future__": 14, "collections.abc": 8, "torch_geometric.loader": 1, "networkx": 3, "torch.nn": 1, "torch.nn.functional": 1, "ironforge.metrics.__init__": 0, "ironforge.metrics.confluence": 3, "ironforge.motifs.__init__": 0, "ironforge.motifs.cards": 5, "ironforge.motifs.scanner": 3, "bisect": 1, "ironforge.reporting.__init__": 0, "ironforge.reporting.confluence": 2, "ironforge.reporting.heatmap": 2, "PIL": 3, "ironforge.reporting.html": 0, "base64": 1, "io": 1, "ironforge.reporting.writer": 0, "ironforge.scripts.prepare_motifs_input": 1, "ironforge.sdk.__main__": 0, "ironforge.sdk.cli": 1, "ironforge.validation.runner": 4, "ironforge.reporting": 4, "ironforge.synthesis.__init__": 0, "ironforge.synthesis.production_graduation": 1, "ironforge.utilities.__init__": 0, "ironforge.utilities.performance_monitor": 1, "ironforge.validation.__init__": 0, "ironforge.validation.statistical_validator": 1, "ironforge.validation.regression_tester": 1, "ironforge.validation.performance_monitor": 1, "ironforge.validation.integration_tester": 1, "ironforge.validation.splits": 6, "ironforge.validation.controls": 7, "ironforge.validation.metrics": 7, "sklearn.metrics": 2, "gc": 1, "orchestrator": 4, "pickle": 12, "performance_monitor": 3, "learning.graph_builder": 1, "run_fpfvg_network_analysis": 0, "run_fpfvg_network_analysis_simple": 0, "run_fpfvg_redelivery_lattice": 0, "run_global_lattice": 0, "run_specialized_lattice": 0, "run_terrain_analysis": 0, "run_weekly_daily_cascade_lattice": 0, "run_weekly_daily_sweep_cascade_step_3b": 0, "run_weekly_daily_sweep_cascade_step_3b_refined": 0, "run_working_cascade_analysis": 0, "scripts.analysis.analyze_concrete_patterns": 0, "glob": 27, "scripts.analysis.analyze_nypm_patterns": 0, "re": 3, "scripts.analysis.bridge_node_mapper": 0, "scripts.analysis.complete_phase2_enhancement": 0, "random": 1, "scripts.analysis.comprehensive_discovery_report": 0, "scripts.analysis.enrichment_analyzer": 0, "scripts.analysis.explore_discoveries": 0, "scripts.analysis.investigate_causal_event_chains": 0, "matplotlib.pyplot": 10, "seaborn": 9, "itertools": 1, "scripts.analysis.investigate_cross_session_synchronization": 0, "scripts.analysis.investigate_htf_structural_inheritance": 0, "matplotlib.patches": 7, "scripts.analysis.investigate_liquidity_sweep_catalyst": 0, "scripts.analysis.investigate_pattern_subarchitecture": 0, "sklearn.cluster": 2, "sklearn.decomposition": 1, "sklearn.preprocessing": 2, "scripts.analysis.phase2_feature_pipeline_enhancement": 0, "scripts.analysis.phase2_validation_framework": 0, "scripts.analysis.phase2_validation_summary": 0, "scripts.analysis.phase4_full_scale_archaeological_discovery": 0, "learning.enhanced_graph_builder": 13, "learning.tgat_discovery": 11, "scripts.analysis.phase4b_attention_head_analysis": 0, "scripts.analysis.phase4b_attention_verification": 0, "scripts.analysis.phase4c_temporal_resonance": 0, "scripts.analysis.phase4d_profile_run": 0, "tracemalloc": 1, "csv": 1, "scripts.analysis.phase5_archaeological_discovery_validation": 0, "scripts.analysis.phase5_direct_tgat_validation": 0, "scripts.analysis.phase5_enhanced_session_validation": 0, "scripts.analysis.process_all_sessions": 0, "scripts.analysis.quick_pattern_discovery": 0, "scripts.analysis.real_pattern_finder": 0, "scripts.analysis.run_archaeology_demonstration": 0, "scripts.analysis.run_contaminated_session_enhancement": 0, "phase2_feature_pipeline_enhancement": 1, "scripts.analysis.run_direct_discovery": 0, "scripts.analysis.run_enhanced_adapter_demonstration": 0, "analysis.enhanced_session_adapter": 5, "unittest.mock": 2, "scripts.analysis.run_full_archaeology_discovery": 0, "analysis.broad_spectrum_archaeology": 4, "analysis.timeframe_lattice_mapper": 8, "analysis.temporal_clustering_engine": 4, "analysis.structural_link_analyzer": 6, "visualizations.lattice_visualizer": 2, "scripts.analysis.run_full_scale_discovery": 0, "scripts.analysis.run_full_session_analysis": 0, "pattern_correlation_visualizer": 1, "scripts.analysis.run_htf_orchestrator": 0, "scripts.analysis.run_manual_discovery": 0, "IRONFORGE.unicode_fix": 1, "IRONFORGE.orchestrator": 1, "scripts.data_processing.enhanced_session_relativity_processor": 0, "scripts.data_processing.enhanced_sessions_price_relativity_processor": 0, "scripts.data_processing.price_field_standardizer": 0, "scripts.data_processing.price_relativity_generator": 0, "scripts.data_processing.session_quality_assessor": 0, "statistics": 1, "scripts.data_processing.unicode_fix": 0, "scripts.utilities.benchmark_performance": 0, "scripts.utilities.daily_discovery_workflows": 0, "ironforge_discovery_sdk": 5, "pattern_intelligence": 2, "scripts.utilities.debug_features": 0, "scripts.utilities.debug_graph_structure": 0, "scripts.utilities.debug_lattice": 0, "scripts.utilities.debug_tgat_init": 0, "scripts.utilities.example_htf_output": 0, "scripts.utilities.graph_builder_diagnostic": 0, "scripts.utilities.htf_builder": 0, "scripts.utilities.ironforge_discovery_sdk": 0, "scripts.utilities.lattice_population_runner": 0, "scripts.utilities.pattern_correlation_visualizer": 0, "scripts.utilities.pattern_intelligence": 0, "scripts.utilities.pattern_monitor": 0, "scripts.utilities.performance_monitor": 0, "setup": 0, "plotly.graph_objects": 1, "plotly.express": 1, "plotly.subplots": 1, "plotly.offline": 1, "broad_spectrum_archaeology": 2, "timeframe_lattice_mapper": 4, "temporal_clustering_engine": 2, "structural_link_analyzer": 4}, "efferent_coupling": {"__init__": 2, "sys": 0, "os": 0, "config": 7, "json": 0, "pathlib": 0, "typing": 0, "logging": 0, "data_migration.batch_migrate_graphs": 16, "argparse": 0, "shutil": 0, "datetime": 0, "concurrent.futures": 0, "traceback": 0, "schema_normalizer": 0, "data_migration.schema_normalizer": 10, "extract_lattice_summary": 3, "iron_core.__init__": 7, "iron_core.performance.container": 9, "iron_core.performance.lazy_loader": 14, "iron_core.mathematical.adaptive_rg_optimizer": 13, "numpy": 0, "dataclasses": 0, "scipy": 0, "scipy.stats": 0, "warnings": 0, "iron_core.mathematical.cascade_classifier": 11, "enum": 0, "iron_core.mathematical.constraints": 8, "decimal": 0, "math": 0, "iron_core.mathematical.fisher_information_monitor": 10, "iron_core.mathematical.grammar_fisher_correlator": 10, "collections": 0, "iron_core.mathematical.hawkes_engine": 12, "iron_core.mathematical.invariants": 10, "ast": 0, "hashlib": 0, "inspect": 0, "time": 0, "iron_core.mathematical.mathematical_hooks": 19, "abc": 0, "asyncio": 0, "iron_core.mathematical.mathematical_layers.api_interface": 31, "iron_core.mathematical.mathematical_layers.integration_layer": 27, "fastapi": 0, "fastapi.responses": 0, "pydantic": 0, "uvicorn": 0, "iron_core.mathematical.mathematical_layers.validation_framework": 25, "iron_core.mathematical.mathematical_layers.core_algorithms": 23, "scipy.fft": 0, "scipy.optimize": 0, "iron_core.mathematical.mathematical_layers.theory_abstraction": 13, "psutil": 0, "iron_core.mathematical.rg_scaler_production": 8, "hypothesis": 0, "iron_core.mathematical.scaling_patterns": 21, "threading": 0, "contextlib": 0, "iron_core.mathematical.temporal_correlator": 11, "iron_core.performance.__init__": 7, "weakref": 0, "functools": 0, "importlib": 0, "iron_core.setup": 3, "setuptools": 0, "ironforge.__init__": 2, "ironforge.integration.ironforge_container": 5, "ironforge.analysis.__init__": 3, "ironforge.analysis.timeframe_lattice_mapper": 5, "ironforge.analysis.enhanced_session_adapter": 5, "ironforge.analysis.broad_spectrum_archaeology": 5, "ironforge.analysis.fpfvg.chain_builder": 3, "ironforge.analysis.fpfvg.features": 5, "ironforge.analysis.fpfvg.runner": 26, "ironforge.analysis.fpfvg.validators": 3, "ironforge.analysis.fpfvg_network_analyzer": 17, "pandas": 0, "ironforge.analysis.fpfvg_redelivery_lattice": 12, "ironforge.analysis.global_lattice_builder": 16, "ironforge.analysis.lattice_terrain_analyzer": 16, "ironforge.analysis.refined_sweep_detector": 8, "ironforge.analysis.specialized_lattice_builder": 16, "ironforge.analysis.weekly_daily_sweep_cascade_analyzer": 10, "ironforge.analysis.weekly_daily_sweep_cascade_lattice": 13, "ironforge.contracts": 2, "ironforge.data_engine.parquet_writer": 6, "pyarrow": 0, "pyarrow.parquet": 0, "ironforge.data_engine.schemas": 0, "ironforge.graph_builder.igraph_builder": 2, "igraph": 0, "ironforge.graph_builder.pyg_converters": 2, "torch": 0, "torch_geometric.data": 0, "ironforge.integration.__init__": 3, "iron_core.performance": 0, "ironforge.learning.enhanced_graph_builder": 9, "ironforge.learning.tgat_discovery": 12, "ironforge.synthesis.pattern_graduation": 8, "ironforge.learning.__init__": 4, "ironforge.learning.simple_event_clustering": 0, "ironforge.learning.regime_segmentation": 0, "ironforge.learning.discovery_pipeline": 20, "__future__": 0, "collections.abc": 0, "torch_geometric.loader": 0, "networkx": 0, "torch.nn": 0, "torch.nn.functional": 0, "ironforge.metrics.__init__": 3, "ironforge.metrics.confluence": 6, "ironforge.motifs.__init__": 5, "ironforge.motifs.cards": 2, "ironforge.motifs.scanner": 7, "bisect": 0, "ironforge.reporting.__init__": 4, "ironforge.reporting.confluence": 5, "ironforge.reporting.heatmap": 5, "PIL": 0, "ironforge.reporting.html": 3, "base64": 0, "io": 0, "ironforge.reporting.writer": 2, "ironforge.scripts.prepare_motifs_input": 6, "ironforge.sdk.__main__": 1, "ironforge.sdk.cli": 16, "ironforge.validation.runner": 16, "ironforge.reporting": 0, "ironforge.synthesis.__init__": 2, "ironforge.synthesis.production_graduation": 14, "ironforge.utilities.__init__": 1, "ironforge.utilities.performance_monitor": 7, "ironforge.validation.__init__": 21, "ironforge.validation.statistical_validator": 15, "ironforge.validation.regression_tester": 0, "ironforge.validation.performance_monitor": 27, "ironforge.validation.integration_tester": 0, "ironforge.validation.splits": 5, "ironforge.validation.controls": 4, "ironforge.validation.metrics": 5, "sklearn.metrics": 0, "gc": 0, "orchestrator": 24, "pickle": 0, "performance_monitor": 0, "learning.graph_builder": 0, "run_fpfvg_network_analysis": 4, "run_fpfvg_network_analysis_simple": 13, "run_fpfvg_redelivery_lattice": 4, "run_global_lattice": 4, "run_specialized_lattice": 4, "run_terrain_analysis": 4, "run_weekly_daily_cascade_lattice": 4, "run_weekly_daily_sweep_cascade_step_3b": 4, "run_weekly_daily_sweep_cascade_step_3b_refined": 9, "run_working_cascade_analysis": 12, "scripts.analysis.analyze_concrete_patterns": 7, "glob": 0, "scripts.analysis.analyze_nypm_patterns": 6, "re": 0, "scripts.analysis.bridge_node_mapper": 15, "scripts.analysis.complete_phase2_enhancement": 8, "random": 0, "scripts.analysis.comprehensive_discovery_report": 7, "scripts.analysis.enrichment_analyzer": 14, "scripts.analysis.explore_discoveries": 6, "scripts.analysis.investigate_causal_event_chains": 14, "matplotlib.pyplot": 0, "seaborn": 0, "itertools": 0, "scripts.analysis.investigate_cross_session_synchronization": 12, "scripts.analysis.investigate_htf_structural_inheritance": 16, "matplotlib.patches": 0, "scripts.analysis.investigate_liquidity_sweep_catalyst": 13, "scripts.analysis.investigate_pattern_subarchitecture": 12, "sklearn.cluster": 0, "sklearn.decomposition": 0, "sklearn.preprocessing": 0, "scripts.analysis.phase2_feature_pipeline_enhancement": 12, "scripts.analysis.phase2_validation_framework": 12, "scripts.analysis.phase2_validation_summary": 4, "scripts.analysis.phase4_full_scale_archaeological_discovery": 10, "learning.enhanced_graph_builder": 0, "learning.tgat_discovery": 0, "scripts.analysis.phase4b_attention_head_analysis": 13, "scripts.analysis.phase4b_attention_verification": 13, "scripts.analysis.phase4c_temporal_resonance": 15, "scripts.analysis.phase4d_profile_run": 15, "tracemalloc": 0, "csv": 0, "scripts.analysis.phase5_archaeological_discovery_validation": 13, "scripts.analysis.phase5_direct_tgat_validation": 14, "scripts.analysis.phase5_enhanced_session_validation": 11, "scripts.analysis.process_all_sessions": 5, "scripts.analysis.quick_pattern_discovery": 6, "scripts.analysis.real_pattern_finder": 5, "scripts.analysis.run_archaeology_demonstration": 20, "scripts.analysis.run_contaminated_session_enhancement": 3, "phase2_feature_pipeline_enhancement": 0, "scripts.analysis.run_direct_discovery": 3, "scripts.analysis.run_enhanced_adapter_demonstration": 14, "analysis.enhanced_session_adapter": 0, "unittest.mock": 0, "scripts.analysis.run_full_archaeology_discovery": 20, "analysis.broad_spectrum_archaeology": 0, "analysis.timeframe_lattice_mapper": 0, "analysis.temporal_clustering_engine": 0, "analysis.structural_link_analyzer": 0, "visualizations.lattice_visualizer": 51, "scripts.analysis.run_full_scale_discovery": 13, "scripts.analysis.run_full_session_analysis": 17, "pattern_correlation_visualizer": 0, "scripts.analysis.run_htf_orchestrator": 4, "scripts.analysis.run_manual_discovery": 4, "IRONFORGE.unicode_fix": 0, "IRONFORGE.orchestrator": 0, "scripts.data_processing.enhanced_session_relativity_processor": 11, "scripts.data_processing.enhanced_sessions_price_relativity_processor": 11, "scripts.data_processing.price_field_standardizer": 6, "scripts.data_processing.price_relativity_generator": 11, "scripts.data_processing.session_quality_assessor": 11, "statistics": 0, "scripts.data_processing.unicode_fix": 3, "scripts.utilities.benchmark_performance": 5, "scripts.utilities.daily_discovery_workflows": 18, "ironforge_discovery_sdk": 0, "pattern_intelligence": 0, "scripts.utilities.debug_features": 4, "scripts.utilities.debug_graph_structure": 4, "scripts.utilities.debug_lattice": 6, "scripts.utilities.debug_tgat_init": 2, "scripts.utilities.example_htf_output": 2, "scripts.utilities.graph_builder_diagnostic": 7, "scripts.utilities.htf_builder": 7, "scripts.utilities.ironforge_discovery_sdk": 24, "scripts.utilities.lattice_population_runner": 12, "scripts.utilities.pattern_correlation_visualizer": 12, "scripts.utilities.pattern_intelligence": 23, "scripts.utilities.pattern_monitor": 7, "scripts.utilities.performance_monitor": 15, "setup": 3, "plotly.graph_objects": 0, "plotly.express": 0, "plotly.subplots": 0, "plotly.offline": 0, "broad_spectrum_archaeology": 0, "timeframe_lattice_mapper": 0, "temporal_clustering_engine": 0, "structural_link_analyzer": 0}, "instability": {"__init__": 1.0, "sys": 0.0, "os": 0.0, "config": 0.25, "json": 0.0, "pathlib": 0.0, "typing": 0.0, "logging": 0.0, "data_migration.batch_migrate_graphs": 1.0, "argparse": 0.0, "shutil": 0.0, "datetime": 0.0, "concurrent.futures": 0.0, "traceback": 0.0, "schema_normalizer": 0.0, "data_migration.schema_normalizer": 1.0, "extract_lattice_summary": 1.0, "iron_core.__init__": 1.0, "iron_core.performance.container": 0.562, "iron_core.performance.lazy_loader": 0.56, "iron_core.mathematical.adaptive_rg_optimizer": 1.0, "numpy": 0.0, "dataclasses": 0.0, "scipy": 0.0, "scipy.stats": 0.0, "warnings": 0.0, "iron_core.mathematical.cascade_classifier": 1.0, "enum": 0.0, "iron_core.mathematical.constraints": 0.889, "decimal": 0.0, "math": 0.0, "iron_core.mathematical.fisher_information_monitor": 0.909, "iron_core.mathematical.grammar_fisher_correlator": 1.0, "collections": 0.0, "iron_core.mathematical.hawkes_engine": 0.923, "iron_core.mathematical.invariants": 1.0, "ast": 0.0, "hashlib": 0.0, "inspect": 0.0, "time": 0.0, "iron_core.mathematical.mathematical_hooks": 0.826, "abc": 0.0, "asyncio": 0.0, "iron_core.mathematical.mathematical_layers.api_interface": 1.0, "iron_core.mathematical.mathematical_layers.integration_layer": 0.9, "fastapi": 0.0, "fastapi.responses": 0.0, "pydantic": 0.0, "uvicorn": 0.0, "iron_core.mathematical.mathematical_layers.validation_framework": 0.926, "iron_core.mathematical.mathematical_layers.core_algorithms": 0.821, "scipy.fft": 0.0, "scipy.optimize": 0.0, "iron_core.mathematical.mathematical_layers.theory_abstraction": 0.765, "psutil": 0.0, "iron_core.mathematical.rg_scaler_production": 0.889, "hypothesis": 0.0, "iron_core.mathematical.scaling_patterns": 1.0, "threading": 0.0, "contextlib": 0.0, "iron_core.mathematical.temporal_correlator": 1.0, "iron_core.performance.__init__": 1.0, "weakref": 0.0, "functools": 0.0, "importlib": 0.0, "iron_core.setup": 1.0, "setuptools": 0.0, "ironforge.__init__": 1.0, "ironforge.integration.ironforge_container": 0.278, "ironforge.analysis.__init__": 1.0, "ironforge.analysis.timeframe_lattice_mapper": 0.714, "ironforge.analysis.enhanced_session_adapter": 0.833, "ironforge.analysis.broad_spectrum_archaeology": 0.833, "ironforge.analysis.fpfvg.chain_builder": 0.429, "ironforge.analysis.fpfvg.features": 0.333, "ironforge.analysis.fpfvg.runner": 1.0, "ironforge.analysis.fpfvg.validators": 0.429, "ironforge.analysis.fpfvg_network_analyzer": 0.944, "pandas": 0.0, "ironforge.analysis.fpfvg_redelivery_lattice": 0.923, "ironforge.analysis.global_lattice_builder": 0.941, "ironforge.analysis.lattice_terrain_analyzer": 0.941, "ironforge.analysis.refined_sweep_detector": 0.889, "ironforge.analysis.specialized_lattice_builder": 0.941, "ironforge.analysis.weekly_daily_sweep_cascade_analyzer": 0.769, "ironforge.analysis.weekly_daily_sweep_cascade_lattice": 0.929, "ironforge.contracts": 1.0, "ironforge.data_engine.parquet_writer": 1.0, "pyarrow": 0.0, "pyarrow.parquet": 0.0, "ironforge.data_engine.schemas": 0.0, "ironforge.graph_builder.igraph_builder": 1.0, "igraph": 0.0, "ironforge.graph_builder.pyg_converters": 1.0, "torch": 0.0, "torch_geometric.data": 0.0, "ironforge.integration.__init__": 1.0, "iron_core.performance": 0.0, "ironforge.learning.enhanced_graph_builder": 0.692, "ironforge.learning.tgat_discovery": 0.8, "ironforge.synthesis.pattern_graduation": 0.667, "ironforge.learning.__init__": 1.0, "ironforge.learning.simple_event_clustering": 0.0, "ironforge.learning.regime_segmentation": 0.0, "ironforge.learning.discovery_pipeline": 0.952, "__future__": 0.0, "collections.abc": 0.0, "torch_geometric.loader": 0.0, "networkx": 0.0, "torch.nn": 0.0, "torch.nn.functional": 0.0, "ironforge.metrics.__init__": 1.0, "ironforge.metrics.confluence": 0.667, "ironforge.motifs.__init__": 1.0, "ironforge.motifs.cards": 0.286, "ironforge.motifs.scanner": 0.7, "bisect": 0.0, "ironforge.reporting.__init__": 1.0, "ironforge.reporting.confluence": 0.714, "ironforge.reporting.heatmap": 0.714, "PIL": 0.0, "ironforge.reporting.html": 1.0, "base64": 0.0, "io": 0.0, "ironforge.reporting.writer": 1.0, "ironforge.scripts.prepare_motifs_input": 0.857, "ironforge.sdk.__main__": 1.0, "ironforge.sdk.cli": 0.941, "ironforge.validation.runner": 0.8, "ironforge.reporting": 0.0, "ironforge.synthesis.__init__": 1.0, "ironforge.synthesis.production_graduation": 0.933, "ironforge.utilities.__init__": 1.0, "ironforge.utilities.performance_monitor": 0.875, "ironforge.validation.__init__": 1.0, "ironforge.validation.statistical_validator": 0.938, "ironforge.validation.regression_tester": 0.0, "ironforge.validation.performance_monitor": 0.964, "ironforge.validation.integration_tester": 0.0, "ironforge.validation.splits": 0.455, "ironforge.validation.controls": 0.364, "ironforge.validation.metrics": 0.417, "sklearn.metrics": 0.0, "gc": 0.0, "orchestrator": 0.857, "pickle": 0.0, "performance_monitor": 0.0, "learning.graph_builder": 0.0, "run_fpfvg_network_analysis": 1.0, "run_fpfvg_network_analysis_simple": 1.0, "run_fpfvg_redelivery_lattice": 1.0, "run_global_lattice": 1.0, "run_specialized_lattice": 1.0, "run_terrain_analysis": 1.0, "run_weekly_daily_cascade_lattice": 1.0, "run_weekly_daily_sweep_cascade_step_3b": 1.0, "run_weekly_daily_sweep_cascade_step_3b_refined": 1.0, "run_working_cascade_analysis": 1.0, "scripts.analysis.analyze_concrete_patterns": 1.0, "glob": 0.0, "scripts.analysis.analyze_nypm_patterns": 1.0, "re": 0.0, "scripts.analysis.bridge_node_mapper": 1.0, "scripts.analysis.complete_phase2_enhancement": 1.0, "random": 0.0, "scripts.analysis.comprehensive_discovery_report": 1.0, "scripts.analysis.enrichment_analyzer": 1.0, "scripts.analysis.explore_discoveries": 1.0, "scripts.analysis.investigate_causal_event_chains": 1.0, "matplotlib.pyplot": 0.0, "seaborn": 0.0, "itertools": 0.0, "scripts.analysis.investigate_cross_session_synchronization": 1.0, "scripts.analysis.investigate_htf_structural_inheritance": 1.0, "matplotlib.patches": 0.0, "scripts.analysis.investigate_liquidity_sweep_catalyst": 1.0, "scripts.analysis.investigate_pattern_subarchitecture": 1.0, "sklearn.cluster": 0.0, "sklearn.decomposition": 0.0, "sklearn.preprocessing": 0.0, "scripts.analysis.phase2_feature_pipeline_enhancement": 1.0, "scripts.analysis.phase2_validation_framework": 1.0, "scripts.analysis.phase2_validation_summary": 1.0, "scripts.analysis.phase4_full_scale_archaeological_discovery": 1.0, "learning.enhanced_graph_builder": 0.0, "learning.tgat_discovery": 0.0, "scripts.analysis.phase4b_attention_head_analysis": 1.0, "scripts.analysis.phase4b_attention_verification": 1.0, "scripts.analysis.phase4c_temporal_resonance": 1.0, "scripts.analysis.phase4d_profile_run": 1.0, "tracemalloc": 0.0, "csv": 0.0, "scripts.analysis.phase5_archaeological_discovery_validation": 1.0, "scripts.analysis.phase5_direct_tgat_validation": 1.0, "scripts.analysis.phase5_enhanced_session_validation": 1.0, "scripts.analysis.process_all_sessions": 1.0, "scripts.analysis.quick_pattern_discovery": 1.0, "scripts.analysis.real_pattern_finder": 1.0, "scripts.analysis.run_archaeology_demonstration": 1.0, "scripts.analysis.run_contaminated_session_enhancement": 1.0, "phase2_feature_pipeline_enhancement": 0.0, "scripts.analysis.run_direct_discovery": 1.0, "scripts.analysis.run_enhanced_adapter_demonstration": 1.0, "analysis.enhanced_session_adapter": 0.0, "unittest.mock": 0.0, "scripts.analysis.run_full_archaeology_discovery": 1.0, "analysis.broad_spectrum_archaeology": 0.0, "analysis.timeframe_lattice_mapper": 0.0, "analysis.temporal_clustering_engine": 0.0, "analysis.structural_link_analyzer": 0.0, "visualizations.lattice_visualizer": 0.962, "scripts.analysis.run_full_scale_discovery": 1.0, "scripts.analysis.run_full_session_analysis": 1.0, "pattern_correlation_visualizer": 0.0, "scripts.analysis.run_htf_orchestrator": 1.0, "scripts.analysis.run_manual_discovery": 1.0, "IRONFORGE.unicode_fix": 0.0, "IRONFORGE.orchestrator": 0.0, "scripts.data_processing.enhanced_session_relativity_processor": 1.0, "scripts.data_processing.enhanced_sessions_price_relativity_processor": 1.0, "scripts.data_processing.price_field_standardizer": 1.0, "scripts.data_processing.price_relativity_generator": 1.0, "scripts.data_processing.session_quality_assessor": 1.0, "statistics": 0.0, "scripts.data_processing.unicode_fix": 1.0, "scripts.utilities.benchmark_performance": 1.0, "scripts.utilities.daily_discovery_workflows": 1.0, "ironforge_discovery_sdk": 0.0, "pattern_intelligence": 0.0, "scripts.utilities.debug_features": 1.0, "scripts.utilities.debug_graph_structure": 1.0, "scripts.utilities.debug_lattice": 1.0, "scripts.utilities.debug_tgat_init": 1.0, "scripts.utilities.example_htf_output": 1.0, "scripts.utilities.graph_builder_diagnostic": 1.0, "scripts.utilities.htf_builder": 1.0, "scripts.utilities.ironforge_discovery_sdk": 1.0, "scripts.utilities.lattice_population_runner": 1.0, "scripts.utilities.pattern_correlation_visualizer": 1.0, "scripts.utilities.pattern_intelligence": 1.0, "scripts.utilities.pattern_monitor": 1.0, "scripts.utilities.performance_monitor": 1.0, "setup": 1.0, "plotly.graph_objects": 0.0, "plotly.express": 0.0, "plotly.subplots": 0.0, "plotly.offline": 0.0, "broad_spectrum_archaeology": 0.0, "timeframe_lattice_mapper": 0.0, "temporal_clustering_engine": 0.0, "structural_link_analyzer": 0.0}, "coupling_distribution": {"mean": 11.69, "max": 324, "min": 1, "modules_with_high_coupling": 95}, "highly_coupled_modules": [{"module": "sys", "total_coupling": 46, "afferent": 46, "efferent": 0, "instability": 0.0}, {"module": "os", "total_coupling": 39, "afferent": 39, "efferent": 0, "instability": 0.0}, {"module": "config", "total_coupling": 28, "afferent": 21, "efferent": 7, "instability": 0.25}, {"module": "json", "total_coupling": 82, "afferent": 82, "efferent": 0, "instability": 0.0}, {"module": "pathlib", "total_coupling": 81, "afferent": 81, "efferent": 0, "instability": 0.0}, {"module": "typing", "total_coupling": 324, "afferent": 324, "efferent": 0, "instability": 0.0}, {"module": "logging", "total_coupling": 69, "afferent": 69, "efferent": 0, "instability": 0.0}, {"module": "data_migration.batch_migrate_graphs", "total_coupling": 16, "afferent": 0, "efferent": 16, "instability": 1.0}, {"module": "datetime", "total_coupling": 96, "afferent": 96, "efferent": 0, "instability": 0.0}, {"module": "traceback", "total_coupling": 16, "afferent": 16, "efferent": 0, "instability": 0.0}, {"module": "iron_core.performance.container", "total_coupling": 16, "afferent": 7, "efferent": 9, "instability": 0.5625}, {"module": "iron_core.performance.lazy_loader", "total_coupling": 25, "afferent": 11, "efferent": 14, "instability": 0.56}, {"module": "iron_core.mathematical.adaptive_rg_optimizer", "total_coupling": 13, "afferent": 0, "efferent": 13, "instability": 1.0}, {"module": "numpy", "total_coupling": 68, "afferent": 68, "efferent": 0, "instability": 0.0}, {"module": "dataclasses", "total_coupling": 40, "afferent": 40, "efferent": 0, "instability": 0.0}, {"module": "scipy.stats", "total_coupling": 11, "afferent": 11, "efferent": 0, "instability": 0.0}, {"module": "iron_core.mathematical.cascade_classifier", "total_coupling": 11, "afferent": 0, "efferent": 11, "instability": 1.0}, {"module": "iron_core.mathematical.fisher_information_monitor", "total_coupling": 11, "afferent": 1, "efferent": 10, "instability": 0.9090909090909091}, {"module": "collections", "total_coupling": 42, "afferent": 42, "efferent": 0, "instability": 0.0}, {"module": "iron_core.mathematical.hawkes_engine", "total_coupling": 13, "afferent": 1, "efferent": 12, "instability": 0.9230769230769231}, {"module": "time", "total_coupling": 25, "afferent": 25, "efferent": 0, "instability": 0.0}, {"module": "iron_core.mathematical.mathematical_hooks", "total_coupling": 23, "afferent": 4, "efferent": 19, "instability": 0.8260869565217391}, {"module": "abc", "total_coupling": 14, "afferent": 14, "efferent": 0, "instability": 0.0}, {"module": "iron_core.mathematical.mathematical_layers.api_interface", "total_coupling": 31, "afferent": 0, "efferent": 31, "instability": 1.0}, {"module": "iron_core.mathematical.mathematical_layers.integration_layer", "total_coupling": 30, "afferent": 3, "efferent": 27, "instability": 0.9}, {"module": "iron_core.mathematical.mathematical_layers.validation_framework", "total_coupling": 27, "afferent": 2, "efferent": 25, "instability": 0.9259259259259259}, {"module": "iron_core.mathematical.mathematical_layers.core_algorithms", "total_coupling": 28, "afferent": 5, "efferent": 23, "instability": 0.8214285714285714}, {"module": "iron_core.mathematical.mathematical_layers.theory_abstraction", "total_coupling": 17, "afferent": 4, "efferent": 13, "instability": 0.7647058823529411}, {"module": "iron_core.mathematical.scaling_patterns", "total_coupling": 21, "afferent": 0, "efferent": 21, "instability": 1.0}, {"module": "iron_core.mathematical.temporal_correlator", "total_coupling": 11, "afferent": 0, "efferent": 11, "instability": 1.0}, {"module": "ironforge.integration.ironforge_container", "total_coupling": 18, "afferent": 13, "efferent": 5, "instability": 0.2777777777777778}, {"module": "ironforge.analysis.fpfvg.features", "total_coupling": 15, "afferent": 10, "efferent": 5, "instability": 0.3333333333333333}, {"module": "ironforge.analysis.fpfvg.runner", "total_coupling": 26, "afferent": 0, "efferent": 26, "instability": 1.0}, {"module": "ironforge.analysis.fpfvg_network_analyzer", "total_coupling": 18, "afferent": 1, "efferent": 17, "instability": 0.9444444444444444}, {"module": "pandas", "total_coupling": 15, "afferent": 15, "efferent": 0, "instability": 0.0}, {"module": "ironforge.analysis.fpfvg_redelivery_lattice", "total_coupling": 13, "afferent": 1, "efferent": 12, "instability": 0.9230769230769231}, {"module": "ironforge.analysis.global_lattice_builder", "total_coupling": 17, "afferent": 1, "efferent": 16, "instability": 0.9411764705882353}, {"module": "ironforge.analysis.lattice_terrain_analyzer", "total_coupling": 17, "afferent": 1, "efferent": 16, "instability": 0.9411764705882353}, {"module": "ironforge.analysis.specialized_lattice_builder", "total_coupling": 17, "afferent": 1, "efferent": 16, "instability": 0.9411764705882353}, {"module": "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "total_coupling": 13, "afferent": 3, "efferent": 10, "instability": 0.7692307692307693}, {"module": "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "total_coupling": 14, "afferent": 1, "efferent": 13, "instability": 0.9285714285714286}, {"module": "torch", "total_coupling": 17, "afferent": 17, "efferent": 0, "instability": 0.0}, {"module": "ironforge.learning.enhanced_graph_builder", "total_coupling": 13, "afferent": 4, "efferent": 9, "instability": 0.6923076923076923}, {"module": "ironforge.learning.tgat_discovery", "total_coupling": 15, "afferent": 3, "efferent": 12, "instability": 0.8}, {"module": "ironforge.synthesis.pattern_graduation", "total_coupling": 12, "afferent": 4, "efferent": 8, "instability": 0.6666666666666666}, {"module": "ironforge.learning.discovery_pipeline", "total_coupling": 21, "afferent": 1, "efferent": 20, "instability": 0.9523809523809523}, {"module": "__future__", "total_coupling": 14, "afferent": 14, "efferent": 0, "instability": 0.0}, {"module": "ironforge.sdk.cli", "total_coupling": 17, "afferent": 1, "efferent": 16, "instability": 0.9411764705882353}, {"module": "ironforge.validation.runner", "total_coupling": 20, "afferent": 4, "efferent": 16, "instability": 0.8}, {"module": "ironforge.synthesis.production_graduation", "total_coupling": 15, "afferent": 1, "efferent": 14, "instability": 0.9333333333333333}, {"module": "ironforge.validation.__init__", "total_coupling": 21, "afferent": 0, "efferent": 21, "instability": 1.0}, {"module": "ironforge.validation.statistical_validator", "total_coupling": 16, "afferent": 1, "efferent": 15, "instability": 0.9375}, {"module": "ironforge.validation.performance_monitor", "total_coupling": 28, "afferent": 1, "efferent": 27, "instability": 0.9642857142857143}, {"module": "ironforge.validation.splits", "total_coupling": 11, "afferent": 6, "efferent": 5, "instability": 0.45454545454545453}, {"module": "ironforge.validation.controls", "total_coupling": 11, "afferent": 7, "efferent": 4, "instability": 0.36363636363636365}, {"module": "ironforge.validation.metrics", "total_coupling": 12, "afferent": 7, "efferent": 5, "instability": 0.4166666666666667}, {"module": "orchestrator", "total_coupling": 28, "afferent": 4, "efferent": 24, "instability": 0.8571428571428571}, {"module": "pickle", "total_coupling": 12, "afferent": 12, "efferent": 0, "instability": 0.0}, {"module": "run_fpfvg_network_analysis_simple", "total_coupling": 13, "afferent": 0, "efferent": 13, "instability": 1.0}, {"module": "run_working_cascade_analysis", "total_coupling": 12, "afferent": 0, "efferent": 12, "instability": 1.0}, {"module": "glob", "total_coupling": 27, "afferent": 27, "efferent": 0, "instability": 0.0}, {"module": "scripts.analysis.bridge_node_mapper", "total_coupling": 15, "afferent": 0, "efferent": 15, "instability": 1.0}, {"module": "scripts.analysis.enrichment_analyzer", "total_coupling": 14, "afferent": 0, "efferent": 14, "instability": 1.0}, {"module": "scripts.analysis.investigate_causal_event_chains", "total_coupling": 14, "afferent": 0, "efferent": 14, "instability": 1.0}, {"module": "scripts.analysis.investigate_cross_session_synchronization", "total_coupling": 12, "afferent": 0, "efferent": 12, "instability": 1.0}, {"module": "scripts.analysis.investigate_htf_structural_inheritance", "total_coupling": 16, "afferent": 0, "efferent": 16, "instability": 1.0}, {"module": "scripts.analysis.investigate_liquidity_sweep_catalyst", "total_coupling": 13, "afferent": 0, "efferent": 13, "instability": 1.0}, {"module": "scripts.analysis.investigate_pattern_subarchitecture", "total_coupling": 12, "afferent": 0, "efferent": 12, "instability": 1.0}, {"module": "scripts.analysis.phase2_feature_pipeline_enhancement", "total_coupling": 12, "afferent": 0, "efferent": 12, "instability": 1.0}, {"module": "scripts.analysis.phase2_validation_framework", "total_coupling": 12, "afferent": 0, "efferent": 12, "instability": 1.0}, {"module": "learning.enhanced_graph_builder", "total_coupling": 13, "afferent": 13, "efferent": 0, "instability": 0.0}, {"module": "learning.tgat_discovery", "total_coupling": 11, "afferent": 11, "efferent": 0, "instability": 0.0}, {"module": "scripts.analysis.phase4b_attention_head_analysis", "total_coupling": 13, "afferent": 0, "efferent": 13, "instability": 1.0}, {"module": "scripts.analysis.phase4b_attention_verification", "total_coupling": 13, "afferent": 0, "efferent": 13, "instability": 1.0}, {"module": "scripts.analysis.phase4c_temporal_resonance", "total_coupling": 15, "afferent": 0, "efferent": 15, "instability": 1.0}, {"module": "scripts.analysis.phase4d_profile_run", "total_coupling": 15, "afferent": 0, "efferent": 15, "instability": 1.0}, {"module": "scripts.analysis.phase5_archaeological_discovery_validation", "total_coupling": 13, "afferent": 0, "efferent": 13, "instability": 1.0}, {"module": "scripts.analysis.phase5_direct_tgat_validation", "total_coupling": 14, "afferent": 0, "efferent": 14, "instability": 1.0}, {"module": "scripts.analysis.phase5_enhanced_session_validation", "total_coupling": 11, "afferent": 0, "efferent": 11, "instability": 1.0}, {"module": "scripts.analysis.run_archaeology_demonstration", "total_coupling": 20, "afferent": 0, "efferent": 20, "instability": 1.0}, {"module": "scripts.analysis.run_enhanced_adapter_demonstration", "total_coupling": 14, "afferent": 0, "efferent": 14, "instability": 1.0}, {"module": "scripts.analysis.run_full_archaeology_discovery", "total_coupling": 20, "afferent": 0, "efferent": 20, "instability": 1.0}, {"module": "visualizations.lattice_visualizer", "total_coupling": 53, "afferent": 2, "efferent": 51, "instability": 0.9622641509433962}, {"module": "scripts.analysis.run_full_scale_discovery", "total_coupling": 13, "afferent": 0, "efferent": 13, "instability": 1.0}, {"module": "scripts.analysis.run_full_session_analysis", "total_coupling": 17, "afferent": 0, "efferent": 17, "instability": 1.0}, {"module": "scripts.data_processing.enhanced_session_relativity_processor", "total_coupling": 11, "afferent": 0, "efferent": 11, "instability": 1.0}, {"module": "scripts.data_processing.enhanced_sessions_price_relativity_processor", "total_coupling": 11, "afferent": 0, "efferent": 11, "instability": 1.0}, {"module": "scripts.data_processing.price_relativity_generator", "total_coupling": 11, "afferent": 0, "efferent": 11, "instability": 1.0}, {"module": "scripts.data_processing.session_quality_assessor", "total_coupling": 11, "afferent": 0, "efferent": 11, "instability": 1.0}, {"module": "scripts.utilities.daily_discovery_workflows", "total_coupling": 18, "afferent": 0, "efferent": 18, "instability": 1.0}, {"module": "scripts.utilities.ironforge_discovery_sdk", "total_coupling": 24, "afferent": 0, "efferent": 24, "instability": 1.0}, {"module": "scripts.utilities.lattice_population_runner", "total_coupling": 12, "afferent": 0, "efferent": 12, "instability": 1.0}, {"module": "scripts.utilities.pattern_correlation_visualizer", "total_coupling": 12, "afferent": 0, "efferent": 12, "instability": 1.0}, {"module": "scripts.utilities.pattern_intelligence", "total_coupling": 23, "afferent": 0, "efferent": 23, "instability": 1.0}, {"module": "scripts.utilities.performance_monitor", "total_coupling": 15, "afferent": 0, "efferent": 15, "instability": 1.0}]}, "critical_paths": [{"rank": 1, "path": ["ironforge.sdk.__main__", "ironforge.sdk.cli", "ironforge.learning.discovery_pipeline", "ironforge.integration.ironforge_container", "ironforge.learning.tgat_discovery", "ironforge.learning.enhanced_graph_builder", "torch"], "length": 7, "risk_level": "medium"}, {"rank": 2, "path": ["ironforge.sdk.cli", "ironforge.learning.discovery_pipeline", "ironforge.integration.ironforge_container", "ironforge.learning.tgat_discovery", "ironforge.learning.enhanced_graph_builder", "torch"], "length": 6, "risk_level": "medium"}, {"rank": 3, "path": ["ironforge.validation.__init__", "ironforge.validation.performance_monitor", "ironforge.integration.ironforge_container", "ironforge.learning.tgat_discovery", "ironforge.learning.enhanced_graph_builder", "torch"], "length": 6, "risk_level": "medium"}, {"rank": 4, "path": ["scripts.analysis.process_all_sessions", "orchestrator", "ironforge.integration.ironforge_container", "ironforge.learning.tgat_discovery", "ironforge.learning.enhanced_graph_builder", "torch"], "length": 6, "risk_level": "medium"}, {"rank": 5, "path": ["scripts.analysis.run_full_scale_discovery", "orchestrator", "ironforge.integration.ironforge_container", "ironforge.learning.tgat_discovery", "ironforge.learning.enhanced_graph_builder", "torch"], "length": 6, "risk_level": "medium"}], "hub_modules": [{"module": "typing", "centrality": 324, "imports": 0, "imported_by": 324, "hub_type": "provider_hub"}, {"module": "datetime", "centrality": 96, "imports": 0, "imported_by": 96, "hub_type": "provider_hub"}, {"module": "json", "centrality": 82, "imports": 0, "imported_by": 82, "hub_type": "provider_hub"}, {"module": "pathlib", "centrality": 81, "imports": 0, "imported_by": 81, "hub_type": "provider_hub"}, {"module": "logging", "centrality": 69, "imports": 0, "imported_by": 69, "hub_type": "provider_hub"}, {"module": "numpy", "centrality": 68, "imports": 0, "imported_by": 68, "hub_type": "provider_hub"}, {"module": "visualizations.lattice_visualizer", "centrality": 53, "imports": 51, "imported_by": 2, "hub_type": "consumer_hub"}, {"module": "sys", "centrality": 46, "imports": 0, "imported_by": 46, "hub_type": "provider_hub"}, {"module": "collections", "centrality": 42, "imports": 0, "imported_by": 42, "hub_type": "provider_hub"}, {"module": "dataclasses", "centrality": 40, "imports": 0, "imported_by": 40, "hub_type": "provider_hub"}, {"module": "os", "centrality": 39, "imports": 0, "imported_by": 39, "hub_type": "provider_hub"}, {"module": "iron_core.mathematical.mathematical_layers.api_interface", "centrality": 31, "imports": 31, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.mathematical_layers.integration_layer", "centrality": 30, "imports": 27, "imported_by": 3, "hub_type": "consumer_hub"}, {"module": "config", "centrality": 28, "imports": 7, "imported_by": 21, "hub_type": "provider_hub"}, {"module": "iron_core.mathematical.mathematical_layers.core_algorithms", "centrality": 28, "imports": 23, "imported_by": 5, "hub_type": "consumer_hub"}, {"module": "ironforge.validation.performance_monitor", "centrality": 28, "imports": 27, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "orchestrator", "centrality": 28, "imports": 24, "imported_by": 4, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.mathematical_layers.validation_framework", "centrality": 27, "imports": 25, "imported_by": 2, "hub_type": "consumer_hub"}, {"module": "glob", "centrality": 27, "imports": 0, "imported_by": 27, "hub_type": "provider_hub"}, {"module": "ironforge.analysis.fpfvg.runner", "centrality": 26, "imports": 26, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.performance.lazy_loader", "centrality": 25, "imports": 14, "imported_by": 11, "hub_type": "mediator_hub"}, {"module": "time", "centrality": 25, "imports": 0, "imported_by": 25, "hub_type": "provider_hub"}, {"module": "scripts.utilities.ironforge_discovery_sdk", "centrality": 24, "imports": 24, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.mathematical_hooks", "centrality": 23, "imports": 19, "imported_by": 4, "hub_type": "consumer_hub"}, {"module": "scripts.utilities.pattern_intelligence", "centrality": 23, "imports": 23, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.scaling_patterns", "centrality": 21, "imports": 21, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "ironforge.learning.discovery_pipeline", "centrality": 21, "imports": 20, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "ironforge.validation.__init__", "centrality": 21, "imports": 21, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "ironforge.validation.runner", "centrality": 20, "imports": 16, "imported_by": 4, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.run_archaeology_demonstration", "centrality": 20, "imports": 20, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.run_full_archaeology_discovery", "centrality": 20, "imports": 20, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "ironforge.integration.ironforge_container", "centrality": 18, "imports": 5, "imported_by": 13, "hub_type": "provider_hub"}, {"module": "ironforge.analysis.fpfvg_network_analyzer", "centrality": 18, "imports": 17, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "scripts.utilities.daily_discovery_workflows", "centrality": 18, "imports": 18, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.mathematical_layers.theory_abstraction", "centrality": 17, "imports": 13, "imported_by": 4, "hub_type": "consumer_hub"}, {"module": "ironforge.analysis.global_lattice_builder", "centrality": 17, "imports": 16, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "ironforge.analysis.lattice_terrain_analyzer", "centrality": 17, "imports": 16, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "ironforge.analysis.specialized_lattice_builder", "centrality": 17, "imports": 16, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "torch", "centrality": 17, "imports": 0, "imported_by": 17, "hub_type": "provider_hub"}, {"module": "ironforge.sdk.cli", "centrality": 17, "imports": 16, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.run_full_session_analysis", "centrality": 17, "imports": 17, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "data_migration.batch_migrate_graphs", "centrality": 16, "imports": 16, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "traceback", "centrality": 16, "imports": 0, "imported_by": 16, "hub_type": "provider_hub"}, {"module": "iron_core.performance.container", "centrality": 16, "imports": 9, "imported_by": 7, "hub_type": "mediator_hub"}, {"module": "ironforge.validation.statistical_validator", "centrality": 16, "imports": 15, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.investigate_htf_structural_inheritance", "centrality": 16, "imports": 16, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "ironforge.analysis.fpfvg.features", "centrality": 15, "imports": 5, "imported_by": 10, "hub_type": "mediator_hub"}, {"module": "pandas", "centrality": 15, "imports": 0, "imported_by": 15, "hub_type": "provider_hub"}, {"module": "ironforge.learning.tgat_discovery", "centrality": 15, "imports": 12, "imported_by": 3, "hub_type": "consumer_hub"}, {"module": "ironforge.synthesis.production_graduation", "centrality": 15, "imports": 14, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.bridge_node_mapper", "centrality": 15, "imports": 15, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.phase4c_temporal_resonance", "centrality": 15, "imports": 15, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.phase4d_profile_run", "centrality": 15, "imports": 15, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.utilities.performance_monitor", "centrality": 15, "imports": 15, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "abc", "centrality": 14, "imports": 0, "imported_by": 14, "hub_type": "provider_hub"}, {"module": "ironforge.analysis.weekly_daily_sweep_cascade_lattice", "centrality": 14, "imports": 13, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "__future__", "centrality": 14, "imports": 0, "imported_by": 14, "hub_type": "provider_hub"}, {"module": "scripts.analysis.enrichment_analyzer", "centrality": 14, "imports": 14, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.investigate_causal_event_chains", "centrality": 14, "imports": 14, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.phase5_direct_tgat_validation", "centrality": 14, "imports": 14, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.run_enhanced_adapter_demonstration", "centrality": 14, "imports": 14, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.adaptive_rg_optimizer", "centrality": 13, "imports": 13, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.hawkes_engine", "centrality": 13, "imports": 12, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "ironforge.analysis.fpfvg_redelivery_lattice", "centrality": 13, "imports": 12, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "ironforge.analysis.weekly_daily_sweep_cascade_analyzer", "centrality": 13, "imports": 10, "imported_by": 3, "hub_type": "consumer_hub"}, {"module": "ironforge.learning.enhanced_graph_builder", "centrality": 13, "imports": 9, "imported_by": 4, "hub_type": "consumer_hub"}, {"module": "run_fpfvg_network_analysis_simple", "centrality": 13, "imports": 13, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.investigate_liquidity_sweep_catalyst", "centrality": 13, "imports": 13, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "learning.enhanced_graph_builder", "centrality": 13, "imports": 0, "imported_by": 13, "hub_type": "provider_hub"}, {"module": "scripts.analysis.phase4b_attention_head_analysis", "centrality": 13, "imports": 13, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.phase4b_attention_verification", "centrality": 13, "imports": 13, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.phase5_archaeological_discovery_validation", "centrality": 13, "imports": 13, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.run_full_scale_discovery", "centrality": 13, "imports": 13, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "ironforge.synthesis.pattern_graduation", "centrality": 12, "imports": 8, "imported_by": 4, "hub_type": "mediator_hub"}, {"module": "ironforge.validation.metrics", "centrality": 12, "imports": 5, "imported_by": 7, "hub_type": "mediator_hub"}, {"module": "pickle", "centrality": 12, "imports": 0, "imported_by": 12, "hub_type": "provider_hub"}, {"module": "run_working_cascade_analysis", "centrality": 12, "imports": 12, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.investigate_cross_session_synchronization", "centrality": 12, "imports": 12, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.investigate_pattern_subarchitecture", "centrality": 12, "imports": 12, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.phase2_feature_pipeline_enhancement", "centrality": 12, "imports": 12, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.analysis.phase2_validation_framework", "centrality": 12, "imports": 12, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.utilities.lattice_population_runner", "centrality": 12, "imports": 12, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.utilities.pattern_correlation_visualizer", "centrality": 12, "imports": 12, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scipy.stats", "centrality": 11, "imports": 0, "imported_by": 11, "hub_type": "provider_hub"}, {"module": "iron_core.mathematical.cascade_classifier", "centrality": 11, "imports": 11, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.fisher_information_monitor", "centrality": 11, "imports": 10, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.temporal_correlator", "centrality": 11, "imports": 11, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "ironforge.validation.splits", "centrality": 11, "imports": 5, "imported_by": 6, "hub_type": "mediator_hub"}, {"module": "ironforge.validation.controls", "centrality": 11, "imports": 4, "imported_by": 7, "hub_type": "mediator_hub"}, {"module": "learning.tgat_discovery", "centrality": 11, "imports": 0, "imported_by": 11, "hub_type": "provider_hub"}, {"module": "scripts.analysis.phase5_enhanced_session_validation", "centrality": 11, "imports": 11, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.data_processing.enhanced_session_relativity_processor", "centrality": 11, "imports": 11, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.data_processing.enhanced_sessions_price_relativity_processor", "centrality": 11, "imports": 11, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.data_processing.price_relativity_generator", "centrality": 11, "imports": 11, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "scripts.data_processing.session_quality_assessor", "centrality": 11, "imports": 11, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "data_migration.schema_normalizer", "centrality": 10, "imports": 10, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.grammar_fisher_correlator", "centrality": 10, "imports": 10, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.invariants", "centrality": 10, "imports": 10, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "ironforge.motifs.scanner", "centrality": 10, "imports": 7, "imported_by": 3, "hub_type": "consumer_hub"}, {"module": "matplotlib.pyplot", "centrality": 10, "imports": 0, "imported_by": 10, "hub_type": "provider_hub"}, {"module": "scripts.analysis.phase4_full_scale_archaeological_discovery", "centrality": 10, "imports": 10, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "warnings", "centrality": 9, "imports": 0, "imported_by": 9, "hub_type": "provider_hub"}, {"module": "enum", "centrality": 9, "imports": 0, "imported_by": 9, "hub_type": "provider_hub"}, {"module": "iron_core.mathematical.constraints", "centrality": 9, "imports": 8, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "iron_core.mathematical.rg_scaler_production", "centrality": 9, "imports": 8, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "ironforge.analysis.refined_sweep_detector", "centrality": 9, "imports": 8, "imported_by": 1, "hub_type": "consumer_hub"}, {"module": "ironforge.metrics.confluence", "centrality": 9, "imports": 6, "imported_by": 3, "hub_type": "mediator_hub"}, {"module": "run_weekly_daily_sweep_cascade_step_3b_refined", "centrality": 9, "imports": 9, "imported_by": 0, "hub_type": "consumer_hub"}, {"module": "seaborn", "centrality": 9, "imports": 0, "imported_by": 9, "hub_type": "provider_hub"}]}, "complexity_analysis": {"hotspots": [{"file": "run_weekly_daily_cascade_lattice.py", "function": "main", "complexity": 43, "line_number": 36}, {"file": "run_fpfvg_network_analysis.py", "function": "main", "complexity": 38, "line_number": 41}, {"file": "scripts/analysis/analyze_concrete_patterns.py", "function": "analyze_actual_events_by_subpattern", "complexity": 29, "line_number": 15}, {"file": "run_specialized_lattice.py", "function": "main", "complexity": 25, "line_number": 30}, {"file": "scripts/analysis/run_archaeology_demonstration.py", "function": "demonstrate_structural_analysis", "complexity": 25, "line_number": 543}, {"file": "scripts/utilities/example_htf_output.py", "function": "demonstrate_htf_integration", "complexity": 25, "line_number": 10}, {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "function": "extract_liquidity_sweep_sequences", "complexity": 24, "line_number": 22}, {"file": "scripts/analysis/phase5_direct_tgat_validation.py", "function": "make_serializable", "complexity": 23, "line_number": 28}, {"file": "ironforge/sdk/cli.py", "function": "main", "complexity": 22, "line_number": 196}, {"file": "run_fpfvg_redelivery_lattice.py", "function": "main", "complexity": 22, "line_number": 35}], "summary": {"files_with_high_complexity": 39, "total_hotspot_functions": 39}, "high_complexity_files": ["data_migration/batch_migrate_graphs.py", "data_migration/schema_normalizer.py", "extract_lattice_summary.py", "ironforge/analysis/fpfvg/validators.py", "ironforge/motifs/scanner.py", "ironforge/reporting/confluence.py", "ironforge/sdk/cli.py", "run_fpfvg_network_analysis.py", "run_fpfvg_redelivery_lattice.py", "run_global_lattice.py", "run_specialized_lattice.py", "run_terrain_analysis.py", "run_weekly_daily_cascade_lattice.py", "run_weekly_daily_sweep_cascade_step_3b.py", "run_weekly_daily_sweep_cascade_step_3b_refined.py", "run_working_cascade_analysis.py", "scripts/analysis/analyze_concrete_patterns.py", "scripts/analysis/analyze_nypm_patterns.py", "scripts/analysis/comprehensive_discovery_report.py", "scripts/analysis/explore_discoveries.py", "scripts/analysis/investigate_causal_event_chains.py", "scripts/analysis/investigate_cross_session_synchronization.py", "scripts/analysis/investigate_htf_structural_inheritance.py", "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "scripts/analysis/phase4c_temporal_resonance.py", "scripts/analysis/phase5_direct_tgat_validation.py", "scripts/analysis/phase5_enhanced_session_validation.py", "scripts/analysis/process_all_sessions.py", "scripts/analysis/real_pattern_finder.py", "scripts/analysis/run_archaeology_demonstration.py", "scripts/analysis/run_contaminated_session_enhancement.py", "scripts/analysis/run_direct_discovery.py", "scripts/analysis/run_full_scale_discovery.py", "scripts/analysis/run_htf_orchestrator.py", "scripts/data_processing/session_quality_assessor.py", "scripts/utilities/debug_features.py", "scripts/utilities/debug_graph_structure.py", "scripts/utilities/example_htf_output.py", "scripts/utilities/graph_builder_diagnostic.py"]}, "public_interfaces": {"analysis": {"classes": {"temporal_correlator.CorrelationResult": {"file": "iron_core/mathematical/temporal_correlator.py", "description": "Result from temporal correlation analysis", "methods": []}, "temporal_correlator.TemporalCorrelationEngine": {"file": "iron_core/mathematical/temporal_correlator.py", "description": "Engine for correlating predictions with validation data across sequences", "methods": ["correlate_prediction_validation", "get_correlation_statistics"]}, "temporal_correlator.SequencePatternAnalyzer": {"file": "iron_core/mathematical/temporal_correlator.py", "description": "Analyzer for detecting patterns in cascade sequences", "methods": ["analyze_sequence_pattern", "detect_emerging_patterns"]}, "temporal_correlator.HTFEvent": {"file": "iron_core/mathematical/temporal_correlator.py", "description": "Higher Timeframe event", "methods": []}, "temporal_correlator.HTFIntensity": {"file": "iron_core/mathematical/temporal_correlator.py", "description": "HTF intensity calculation result", "methods": []}, "temporal_correlator.HTFMasterController": {"file": "iron_core/mathematical/temporal_correlator.py", "description": "Higher Timeframe Master Controller\n\nImplements fractal cascade architecture where HTF events serve as master\ncontrollers for session-level prediction activation.\n\nMathematical Foundation: λ_HTF(t) = μ_h + Σ α_h · exp(-β_h (t - t_j)) · magnitude", "methods": ["add_htf_event", "calculate_htf_intensity", "generate_activation_signal"]}, "temporal_correlator.CascadeType": {"file": "iron_core/mathematical/temporal_correlator.py", "description": null, "methods": []}, "temporal_correlator.MockEvent": {"file": "iron_core/mathematical/temporal_correlator.py", "description": null, "methods": []}, "broad_spectrum_archaeology.BroadSpectrumArchaeology": {"file": "ironforge/analysis/broad_spectrum_archaeology.py", "description": "Comprehensive archaeological analysis across multiple sessions\nDiscovers broad spectrum patterns and cross-session relationships", "methods": ["analyze_broad_spectrum"]}, "enhanced_session_adapter.EnhancedSessionAdapter": {"file": "ironforge/analysis/enhanced_session_adapter.py", "description": "Adapts raw session data for enhanced archaeological analysis\nProvides session context and enhancement capabilities", "methods": ["adapt_session"]}, "runner.FPFVGNetworkAnalyzer": {"file": "ironforge/analysis/fpfvg/runner.py", "description": "FPFVG Redelivery Network Analyzer - Main orchestration class\n\nCoordinates the analysis pipeline while delegating heavy lifting to specialized modules.", "methods": ["analyze_fpfvg_network"]}, "fpfvg_network_analyzer.FPFVGNetworkAnalyzer": {"file": "ironforge/analysis/fpfvg_network_analyzer.py", "description": "FPFVG Redelivery Network Analyzer\n\nImplements comprehensive network analysis to prove FPFVG redelivery alignment\nwith Theory B dimensional zones and PM belt timing patterns.", "methods": ["analyze_fpfvg_network"]}, "fpfvg_redelivery_lattice.FPFVGRedeliveryLattice": {"file": "ironforge/analysis/fpfvg_redelivery_lattice.py", "description": "FPFVG (Fair Value Gap) Redelivery Network Lattice Builder\n\nConstructs specialized lattice views focused on FVG formation and redelivery patterns\nto test Theory B's temporal non-locality hypothesis.", "methods": ["build_fpfvg_redelivery_lattice"]}, "global_lattice_builder.GlobalLatticeBuilder": {"file": "ironforge/analysis/global_lattice_builder.py", "description": "Global lattice builder for comprehensive Monthly→1m analysis across all sessions.\n\nFeatures:\n- Multi-session lattice aggregation\n- Hot zone identification and clustering  \n- Vertical cascade tracing (Monthly→Weekly→Daily→PM)\n- Statistical enrichment analysis\n- Bridge node discovery", "methods": ["build_global_lattice", "get_lattice_summary"]}, "lattice_terrain_analyzer.LatticeTerrainAnalyzer": {"file": "ironforge/analysis/lattice_terrain_analyzer.py", "description": "Analyzes global lattice terrain to identify hot zones and cascade patterns.\n\nBased on the successful global lattice build:\n- 57 sessions processed\n- 249 hot zones identified  \n- 10,568 vertical cascades traced\n- 12,546 bridge nodes found", "methods": ["analyze_terrain_from_log", "get_terrain_summary"]}, "refined_sweep_detector.RefinedSweepEvent": {"file": "ironforge/analysis/refined_sweep_detector.py", "description": "Enhanced sweep event with refined detection", "methods": []}, "refined_sweep_detector.RefinedSweepDetector": {"file": "ironforge/analysis/refined_sweep_detector.py", "description": "Refined sweep detector with relaxed thresholds and enhanced detection", "methods": ["detect_refined_sweeps", "serialize_refined_sweep"]}, "specialized_lattice_builder.SpecializedLatticeBuilder": {"file": "ironforge/analysis/specialized_lattice_builder.py", "description": "Builds specialized lattice views for deep archaeological analysis.\n\nFocus Areas:\n1. NY PM Archaeological Belt (14:35-38 with Theory B validation)\n2. FPFVG Redelivery Networks (4H → 1H → 15m cascades)\n3. Weekly → Daily Liquidity Sweep Cascades", "methods": ["build_ny_pm_archaeological_belt", "get_specialized_builder_summary"]}, "timeframe_lattice_mapper.TimeframeLatticeMapper": {"file": "ironforge/analysis/timeframe_lattice_mapper.py", "description": "Maps discovered patterns across different timeframes\nAnalyzes multi-timeframe pattern relationships", "methods": ["map_timeframe_patterns", "map_session_lattice"]}, "weekly_daily_sweep_cascade_analyzer.SweepEvent": {"file": "ironforge/analysis/weekly_daily_sweep_cascade_analyzer.py", "description": "Structured sweep event representation", "methods": []}, "weekly_daily_sweep_cascade_analyzer.CascadeLink": {"file": "ironforge/analysis/weekly_daily_sweep_cascade_analyzer.py", "description": "Cascade relationship between events", "methods": []}, "weekly_daily_sweep_cascade_analyzer.WeeklyDailySweepCascadeAnalyzer": {"file": "ironforge/analysis/weekly_daily_sweep_cascade_analyzer.py", "description": "Weekly→Daily Liquidity Sweep Cascade Analyzer (Step 3B)\n\nImplements comprehensive macro driver analysis to verify Weekly dominance\nthrough cascade pattern detection and statistical validation.", "methods": ["analyze_weekly_daily_cascades"]}, "weekly_daily_sweep_cascade_lattice.WeeklyDailySweepCascadeLattice": {"file": "ironforge/analysis/weekly_daily_sweep_cascade_lattice.py", "description": "Weekly→Daily Liquidity Sweep Cascade Lattice Builder\n\nMaps macro-level cascade patterns where Weekly HTF events influence Daily session structure\nthrough liquidity sweep transmission mechanisms.", "methods": ["build_weekly_daily_cascade_lattice"]}, "scanner.MotifMatch": {"file": "ironforge/motifs/scanner.py", "description": null, "methods": []}, "run_fpfvg_network_analysis_simple.SimpleFPFVGAnalyzer": {"file": "run_fpfvg_network_analysis_simple.py", "description": "Simplified FPFVG network analyzer focused on key statistical tests", "methods": ["analyze"]}, "run_weekly_daily_sweep_cascade_step_3b_refined.RefinedCascadeAnalyzer": {"file": "run_weekly_daily_sweep_cascade_step_3b_refined.py", "description": "Enhanced cascade analyzer with refined detection thresholds", "methods": ["analyze_weekly_daily_cascades_refined"]}, "run_working_cascade_analysis.WorkingSweepEvent": {"file": "run_working_cascade_analysis.py", "description": "Streamlined sweep event based on actual data patterns", "methods": []}, "run_working_cascade_analysis.WorkingCascadeLink": {"file": "run_working_cascade_analysis.py", "description": "Streamlined cascade link", "methods": []}, "run_working_cascade_analysis.WorkingCascadeAnalyzer": {"file": "run_working_cascade_analysis.py", "description": "Streamlined cascade analyzer using proven data patterns", "methods": ["analyze_working_cascades"]}, "bridge_node_mapper.BridgeNodeMapper": {"file": "scripts/analysis/bridge_node_mapper.py", "description": "Maps HTF → PM cascade pathways and bridge node relationships", "methods": ["run_bridge_analysis"]}, "enrichment_analyzer.EnrichmentAnalyzer": {"file": "scripts/analysis/enrichment_analyzer.py", "description": "Analyzes statistical enrichment of PM belt events in lattice zones", "methods": ["run_enrichment_analysis"]}, "phase2_feature_pipeline_enhancement.FeaturePipelineEnhancer": {"file": "scripts/analysis/phase2_feature_pipeline_enhancement.py", "description": "Phase 2 Feature Pipeline Enhancement for TGAT Model Quality Recovery\n\nReplaces artificial default values with authentic market-derived calculations\nto restore genuine archaeological discovery capability.", "methods": ["calculate_authentic_htf_carryover_strength", "calculate_authentic_energy_density", "generate_authentic_liquidity_events", "validate_feature_authenticity", "enhance_session", "run_batch_enhancement"]}, "phase4_full_scale_archaeological_discovery.FullScaleArchaeologicalDiscovery": {"file": "scripts/analysis/phase4_full_scale_archaeological_discovery.py", "description": "Full scale archaeological discovery with no session limits or chunking.", "methods": ["discover_all_sessions"]}, "phase4b_attention_head_analysis.AttentionHeadAnalyzer": {"file": "scripts/analysis/phase4b_attention_head_analysis.py", "description": "Analyze TGAT attention heads to verify archeological pattern specialization.", "methods": ["analyze_attention_heads"]}, "phase4b_attention_verification.AttentionHeadAnalyzer": {"file": "scripts/analysis/phase4b_attention_verification.py", "description": "Analyze TGAT attention heads to verify archeological pattern specialization.", "methods": ["analyze_attention_heads"]}, "phase4c_temporal_resonance.TemporalResonanceAnalyzer": {"file": "scripts/analysis/phase4c_temporal_resonance.py", "description": "Analyze temporal resonance across multiple sessions.", "methods": ["build_cross_session_test_set", "implement_anchor_projection", "compute_resonance_scores", "extract_resonant_motifs", "analyze_head_attribution", "run_temporal_resonance_analysis"]}, "phase5_archaeological_discovery_validation.Phase5ArchaeologicalValidator": {"file": "scripts/analysis/phase5_archaeological_discovery_validation.py", "description": "Phase 5 TGAT Archaeological Discovery Validation\nTests pattern discovery on authentic enhanced features", "methods": ["load_enhanced_session", "validate_feature_authenticity", "run_tgat_discovery", "analyze_pattern_quality", "calculate_authenticity_score", "run_comparative_analysis", "run_validation"]}, "phase5_direct_tgat_validation.Phase5DirectTGATValidator": {"file": "scripts/analysis/phase5_direct_tgat_validation.py", "description": "Direct TGAT validation without container dependencies", "methods": ["load_enhanced_session", "build_session_graph", "run_tgat_discovery_direct", "analyze_pattern_quality", "calculate_authenticity_score", "run_validation"]}, "quick_pattern_discovery.QuickPatternDiscovery": {"file": "scripts/analysis/quick_pattern_discovery.py", "description": "Fast pattern discovery for immediate insights", "methods": ["discover_patterns"]}, "run_archaeology_demonstration.DemoTimeframe": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": null, "methods": []}, "run_archaeology_demonstration.DemoEventType": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": null, "methods": []}, "run_archaeology_demonstration.DemoSessionPhase": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": null, "methods": []}, "run_archaeology_demonstration.DemoArchaeologicalEvent": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Enhanced synthetic archaeological event", "methods": []}, "run_enhanced_adapter_demonstration.EnhancedAdapterDemo": {"file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "description": "Live demonstration system for Enhanced Session Adapter", "methods": ["run_live_demonstration"]}, "run_full_session_analysis.SessionAnalyzer": {"file": "scripts/analysis/run_full_session_analysis.py", "description": "Comprehensive session analysis with pattern extraction and visualization", "methods": ["analyze_all_sessions", "generate_visualizations", "generate_reports"]}, "lattice_population_runner.LatticePopulationRunner": {"file": "scripts/utilities/lattice_population_runner.py", "description": "Populates the complete IRONFORGE lattice with all available enhanced sessions", "methods": ["run_complete_population"]}, "lattice_visualizer.VisualizationConfig": {"file": "visualizations/lattice_visualizer.py", "description": "Configuration for visualization settings", "methods": []}, "lattice_visualizer.LatticeVisualizer": {"file": "visualizations/lattice_visualizer.py", "description": "Comprehensive visualization system for market archaeology lattice", "methods": ["create_comprehensive_visualization", "create_lattice_diagram", "create_temporal_heatmaps", "create_hot_zone_visualization", "create_network_visualization", "create_clustering_visualizations", "create_structural_visualizations", "create_interactive_dashboard"]}}, "functions": {"extract_lattice_summary.extract_lattice_summary": {"file": "extract_lattice_summary.py", "description": "Extract key summary information from lattice file", "parameters": [{"name": "lattice_file", "annotation": null, "default": null, "kind": "positional"}]}, "chain_builder.construct_directed_network": {"file": "ironforge/analysis/fpfvg/chain_builder.py", "description": "Construct directed network of FPFVG events\n\nNetwork Rules:\n- Node = FPFVG instance (formation or redelivery)\n- Edge A→B if:\n  1. B.price_level within ±ε of A.price_level OR B.range_pos within ±δ of A.range_pos\n  2. A.end_ts < B.start_ts (temporal ordering)\n  3. Optional: same structural strand (within same HTF range)", "parameters": [{"name": "candidates", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}, {"name": "price_epsilon", "annotation": "float", "default": "5.0", "kind": "positional"}, {"name": "range_pos_delta", "annotation": "float", "default": "0.05", "kind": "positional"}, {"name": "max_temporal_gap_hours", "annotation": "float", "default": "12.0", "kind": "positional"}]}, "chain_builder.calculate_network_density": {"file": "ironforge/analysis/fpfvg/chain_builder.py", "description": "Calculate network density (edges / max_possible_edges)", "parameters": [{"name": "network_graph", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}]}, "chain_builder.identify_network_motifs": {"file": "ironforge/analysis/fpfvg/chain_builder.py", "description": "Identify common network motifs (chains, convergences, divergences)", "parameters": [{"name": "network_graph", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}]}, "chain_builder.find_chains": {"file": "ironforge/analysis/fpfvg/chain_builder.py", "description": "Find chains in the network using DFS", "parameters": [{"name": "adjacency", "annotation": "dict[str, list[str]]", "default": null, "kind": "positional"}, {"name": "min_length", "annotation": "int", "default": "3", "kind": "positional"}]}, "chain_builder.dfs_chain": {"file": "ironforge/analysis/fpfvg/chain_builder.py", "description": null, "parameters": [{"name": "node", "annotation": null, "default": null, "kind": "positional"}, {"name": "current_chain", "annotation": null, "default": null, "kind": "positional"}, {"name": "visited", "annotation": null, "default": null, "kind": "positional"}]}, "features.score_redelivery_strength": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Score re-delivery strength using weighted factors\n\nFormula: w1·(price proximity) + w2·(range_pos proximity) + w3·(zone_confluence) - w4·(Δt penalty)", "parameters": [{"name": "network_graph", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}, {"name": "scoring_weights", "annotation": "dict[str, float]", "default": "None", "kind": "positional"}, {"name": "price_epsilon", "annotation": "float", "default": "5.0", "kind": "positional"}, {"name": "range_pos_delta", "annotation": "float", "default": "0.05", "kind": "positional"}, {"name": "max_temporal_gap_hours", "annotation": "float", "default": "12.0", "kind": "positional"}, {"name": "theory_b_zones", "annotation": "list[float]", "default": "None", "kind": "positional"}]}, "features.calculate_price_proximity_score": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Calculate price proximity score (1.0 = identical prices)", "parameters": [{"name": "edge", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}, {"name": "price_epsilon", "annotation": "float", "default": "5.0", "kind": "positional"}]}, "features.calculate_range_pos_proximity_score": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Calculate range position proximity score", "parameters": [{"name": "edge", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}, {"name": "range_pos_delta", "annotation": "float", "default": "0.05", "kind": "positional"}]}, "features.calculate_zone_confluence_score": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Calculate zone confluence score", "parameters": [{"name": "edge", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}, {"name": "theory_b_zones", "annotation": "list[float]", "default": "None", "kind": "positional"}]}, "features.calculate_temporal_penalty_score": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Calculate temporal penalty score (higher for longer delays)", "parameters": [{"name": "edge", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}, {"name": "max_temporal_gap_hours", "annotation": "float", "default": "12.0", "kind": "positional"}]}, "features.analyze_score_distribution": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Analyze distribution of redelivery scores", "parameters": [{"name": "redelivery_scores", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}]}, "features.calculate_range_position": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Calculate range position (0-1) for a price level within session range\n\nArgs:\n    price_level: Price to calculate position for\n    session_id: Session identifier\n    session_ranges: Dict of session ranges {session_id: {\"low\": float, \"high\": float}}\n\nReturns:\n    float: Position in range (0 = low, 1 = high)", "parameters": [{"name": "price_level", "annotation": "float", "default": null, "kind": "positional"}, {"name": "session_id", "annotation": "str", "default": null, "kind": "positional"}, {"name": "session_ranges", "annotation": "dict[str, dict]", "default": "None", "kind": "positional"}]}, "features.get_zone_proximity": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Calculate proximity to Theory B zones\n\nArgs:\n    range_pos: Range position (0-1)\n    theory_b_zones: List of zone positions\n    zone_tolerance: Tolerance for zone proximity\n\nReturns:\n    dict: Zone proximity information", "parameters": [{"name": "range_pos", "annotation": "float", "default": null, "kind": "positional"}, {"name": "theory_b_zones", "annotation": "list[float]", "default": "None", "kind": "positional"}, {"name": "zone_tolerance", "annotation": "float", "default": "0.03", "kind": "positional"}]}, "features.extract_magnitude": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Extract magnitude/importance of event from event data", "parameters": [{"name": "event_data", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}]}, "features.get_candidate_summary_stats": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Calculate summary statistics for FPFVG candidates", "parameters": [{"name": "candidates", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}]}, "features.test_zone_enrichment": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Test zone enrichment: are redeliveries enriched in Theory B zones?\n\nUses Fisher exact test to compare observed vs expected redeliveries in zones.", "parameters": [{"name": "candidates", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}, {"name": "theory_b_zones", "annotation": "list[float]", "default": "None", "kind": "positional"}, {"name": "zone_tolerance", "annotation": "float", "default": "0.03", "kind": "positional"}, {"name": "alpha", "annotation": "float", "default": "0.05", "kind": "positional"}]}, "features.test_pm_belt_interaction": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Test PM-belt interaction: P(redelivery hits 14:35-:38 | prior FVG in session) vs baseline\n\nH0: No increased PM belt interaction after FVG formation\nH1: FVG formations increase probability of PM belt redelivery", "parameters": [{"name": "candidates", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}, {"name": "network_graph", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}, {"name": "alpha", "annotation": "float", "default": "0.05", "kind": "positional"}]}, "features.test_reproducibility": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Test reproducibility with per-session bootstrap analysis\n\nGoal: Validate that findings are reproducible across sessions", "parameters": [{"name": "candidates", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}, {"name": "network_graph", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}, {"name": "bootstrap_iterations", "annotation": "int", "default": "1000", "kind": "positional"}, {"name": "confidence_level", "annotation": "float", "default": "0.95", "kind": "positional"}]}, "features.generate_summary_insights": {"file": "ironforge/analysis/fpfvg/features.py", "description": "Generate high-level insights from analysis results", "parameters": [{"name": "analysis_results", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}]}, "runner.build_chains": {"file": "ironforge/analysis/fpfvg/runner.py", "description": "Build chains from adjacency list (re-exported from chain_builder)", "parameters": [{"name": "adjacency", "annotation": "dict[str, list[str]]", "default": null, "kind": "positional"}, {"name": "min_length", "annotation": "int", "default": "3", "kind": "positional"}]}, "runner.validate_chain": {"file": "ironforge/analysis/fpfvg/runner.py", "description": "Validate FPFVG chain data (re-exported from validators)", "parameters": [{"name": "candidates", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}]}, "runner.compute_chain_features": {"file": "ironforge/analysis/fpfvg/runner.py", "description": "Compute chain features (re-exported from features)", "parameters": [{"name": "network_graph", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}]}, "validators.validate_candidates": {"file": "ironforge/analysis/fpfvg/validators.py", "description": "Validate FPFVG candidates for required fields and data consistency\n\nReturns validation results with any errors or warnings found.", "parameters": [{"name": "candidates", "annotation": "list[dict[str, Any]]", "default": null, "kind": "positional"}]}, "validators.validate_network_graph": {"file": "ironforge/analysis/fpfvg/validators.py", "description": "Validate network graph structure and consistency", "parameters": [{"name": "network_graph", "annotation": "dict[str, Any]", "default": null, "kind": "positional"}]}, "validators.is_in_pm_belt": {"file": "ironforge/analysis/fpfvg/validators.py", "description": "Check if timestamp falls within PM belt window\n\nArgs:\n    timestamp: ISO timestamp string\n    pm_belt_start: PM belt start time (default 14:35)\n    pm_belt_end: PM belt end time (default 14:38)\n\nReturns:\n    bool: True if timestamp is in PM belt", "parameters": [{"name": "timestamp", "annotation": "str", "default": null, "kind": "positional"}, {"name": "pm_belt_start", "annotation": "time", "default": "time(14, 35)", "kind": "positional"}, {"name": "pm_belt_end", "annotation": "time", "default": "time(14, 38)", "kind": "positional"}]}, "validators.safe_float": {"file": "ironforge/analysis/fpfvg/validators.py", "description": "Safely convert value to float with fallback", "parameters": [{"name": "value", "annotation": "Any", "default": null, "kind": "positional"}, {"name": "default", "annotation": "float", "default": "0.0", "kind": "positional"}]}, "fpfvg_network_analyzer.dfs_chain": {"file": "ironforge/analysis/fpfvg_network_analyzer.py", "description": null, "parameters": [{"name": "node", "annotation": null, "default": null, "kind": "positional"}, {"name": "current_chain", "annotation": null, "default": null, "kind": "positional"}, {"name": "visited", "annotation": null, "default": null, "kind": "positional"}]}, "scanner.scan_session_for_cards": {"file": "ironforge/motifs/scanner.py", "description": null, "parameters": [{"name": "session_id", "annotation": "str", "default": null, "kind": "positional"}, {"name": "events", "annotation": "list[dict]", "default": null, "kind": "positional"}, {"name": "confluence", "annotation": "np.n<PERSON><PERSON> | None", "default": null, "kind": "positional"}, {"name": "cards", "annotation": "list[MotifCard] | None", "default": "None", "kind": "positional"}, {"name": "min_confluence", "annotation": "float", "default": "65.0", "kind": "positional"}]}, "scanner.run_cli_scan": {"file": "ironforge/motifs/scanner.py", "description": null, "parameters": [{"name": "input_json_path", "annotation": null, "default": null, "kind": "positional"}, {"name": "top_k", "annotation": "int", "default": "3", "kind": "positional"}, {"name": "min_confluence", "annotation": "float", "default": "65.0", "kind": "positional"}, {"name": "preset", "annotation": "str", "default": "'default'", "kind": "positional"}]}, "run_fpfvg_network_analysis.main": {"file": "run_fpfvg_network_analysis.py", "description": "Execute FPFVG Network Analysis (Step 3A)", "parameters": []}, "run_fpfvg_network_analysis_simple.main": {"file": "run_fpfvg_network_analysis_simple.py", "description": "Execute simplified FPFVG network analysis", "parameters": []}, "run_fpfvg_redelivery_lattice.main": {"file": "run_fpfvg_redelivery_lattice.py", "description": "Execute FPFVG Redelivery Network Lattice analysis", "parameters": []}, "run_global_lattice.main": {"file": "run_global_lattice.py", "description": "Execute global lattice build", "parameters": []}, "run_specialized_lattice.main": {"file": "run_specialized_lattice.py", "description": "Execute specialized lattice building for archaeological deep dive", "parameters": []}, "run_terrain_analysis.main": {"file": "run_terrain_analysis.py", "description": "Execute terrain analysis for hot zones and cascades", "parameters": []}, "run_weekly_daily_cascade_lattice.main": {"file": "run_weekly_daily_cascade_lattice.py", "description": "Execute Weekly→Daily Liquidity Sweep Cascade Lattice analysis", "parameters": []}, "run_weekly_daily_sweep_cascade_step_3b_refined.main": {"file": "run_weekly_daily_sweep_cascade_step_3b_refined.py", "description": "Execute REFINED Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B)", "parameters": []}, "run_working_cascade_analysis.main": {"file": "run_working_cascade_analysis.py", "description": "Execute working cascade analysis", "parameters": []}, "analyze_concrete_patterns.analyze_actual_events_by_subpattern": {"file": "scripts/analysis/analyze_concrete_patterns.py", "description": "Analyze what actual semantic events occur in each sub-pattern", "parameters": []}, "analyze_concrete_patterns.analyze_market_mechanics": {"file": "scripts/analysis/analyze_concrete_patterns.py", "description": "Analyze what these patterns mean in terms of actual market mechanics", "parameters": []}, "analyze_concrete_patterns.analyze_specific_examples": {"file": "scripts/analysis/analyze_concrete_patterns.py", "description": "Look at specific examples of each sub-pattern", "parameters": []}, "analyze_concrete_patterns.decode_price_levels": {"file": "scripts/analysis/analyze_concrete_patterns.py", "description": "Analyze what specific price levels and movements these patterns represent", "parameters": []}, "analyze_concrete_patterns.main": {"file": "scripts/analysis/analyze_concrete_patterns.py", "description": "Main concrete analysis", "parameters": []}, "analyze_nypm_patterns.analyze_nypm_patterns": {"file": "scripts/analysis/analyze_nypm_patterns.py", "description": "Analyze discovered patterns specifically for NY PM sessions", "parameters": []}, "bridge_node_mapper.main": {"file": "scripts/analysis/bridge_node_mapper.py", "description": "Run bridge node analysis", "parameters": []}, "bridge_node_mapper.convert_numpy_types": {"file": "scripts/analysis/bridge_node_mapper.py", "description": null, "parameters": [{"name": "obj", "annotation": null, "default": null, "kind": "positional"}]}, "comprehensive_discovery_report.analyze_all_sessions": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Analyze all preserved sessions comprehensively", "parameters": []}, "comprehensive_discovery_report.analyze_single_session": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Comprehensive analysis of a single session", "parameters": [{"name": "graph_data", "annotation": null, "default": null, "kind": "positional"}, {"name": "session_name", "annotation": null, "default": null, "kind": "positional"}]}, "comprehensive_discovery_report.create_time_clusters": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Create time clusters from events", "parameters": [{"name": "time_based_events", "annotation": null, "default": null, "kind": "positional"}, {"name": "bin_size", "annotation": null, "default": "5", "kind": "positional"}]}, "comprehensive_discovery_report.extract_session_type": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Extract session type from session name", "parameters": [{"name": "session_name", "annotation": null, "default": null, "kind": "positional"}]}, "comprehensive_discovery_report.analyze_temporal_patterns": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Analyze temporal patterns across sessions", "parameters": [{"name": "session_data", "annotation": null, "default": null, "kind": "positional"}]}, "comprehensive_discovery_report.analyze_pattern_quality": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Analyze the quality and characteristics of discovered patterns", "parameters": [{"name": "session_data", "annotation": null, "default": null, "kind": "positional"}]}, "comprehensive_discovery_report.analyze_discovered_patterns": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Analyze the 500 patterns from TGAT discovery", "parameters": []}, "comprehensive_discovery_report.main": {"file": "scripts/analysis/comprehensive_discovery_report.py", "description": "Main analysis function", "parameters": []}, "decode_subpattern_findings.decode_feature_7": {"file": "scripts/analysis/decode_subpattern_findings.py", "description": "Decode what Feature 7 actually represents in the 47D feature vector", "parameters": []}, "decode_subpattern_findings.decode_feature_clusters": {"file": "scripts/analysis/decode_subpattern_findings.py", "description": "Decode the other significant features from the clustering", "parameters": []}, "decode_subpattern_findings.decode_sub_pattern_0": {"file": "scripts/analysis/decode_subpattern_findings.py", "description": "Decode what Sub-Pattern 0 actually represents", "parameters": []}, "decode_subpattern_findings.decode_sub_pattern_1": {"file": "scripts/analysis/decode_subpattern_findings.py", "description": "Decode what Sub-Pattern 1 represents", "parameters": []}, "decode_subpattern_findings.decode_sub_pattern_2": {"file": "scripts/analysis/decode_subpattern_findings.py", "description": "Decode what Sub-Pattern 2 represents", "parameters": []}, "decode_subpattern_findings.synthesize_discovery": {"file": "scripts/analysis/decode_subpattern_findings.py", "description": "Synthesize the complete discovery meaning", "parameters": []}, "decode_subpattern_findings.main": {"file": "scripts/analysis/decode_subpattern_findings.py", "description": "Main analysis function", "parameters": []}, "enrichment_analyzer.main": {"file": "scripts/analysis/enrichment_analyzer.py", "description": "Run enrichment analysis", "parameters": []}, "explore_discoveries.load_time_patterns_from_graphs": {"file": "scripts/analysis/explore_discoveries.py", "description": "Load time patterns from preserved graphs", "parameters": []}, "explore_discoveries.analyze_discovered_patterns": {"file": "scripts/analysis/explore_discoveries.py", "description": "Load and analyze the 500 discovered patterns", "parameters": []}, "explore_discoveries.explore_time_clustering_insights": {"file": "scripts/analysis/explore_discoveries.py", "description": "Analyze the time clustering discoveries", "parameters": [{"name": "time_patterns", "annotation": null, "default": null, "kind": "positional"}, {"name": "session_summaries", "annotation": null, "default": null, "kind": "positional"}]}, "explore_discoveries.show_session_insights": {"file": "scripts/analysis/explore_discoveries.py", "description": "Show insights by session", "parameters": [{"name": "session_summaries", "annotation": null, "default": null, "kind": "positional"}]}, "explore_discoveries.main": {"file": "scripts/analysis/explore_discoveries.py", "description": "Main exploration function", "parameters": []}, "investigate_cross_session_synchronization.extract_event_timing_data": {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "description": "Extract absolute time-of-day for each semantic event across all sessions", "parameters": []}, "investigate_cross_session_synchronization.build_synchronization_matrix": {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "description": "Build co-occurrence matrix: Time_Bin[i] vs Sessions[j]", "parameters": [{"name": "event_data", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_cross_session_synchronization.identify_synchronized_time_slots": {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "description": "Identify time slots with >60% cross-session occurrence", "parameters": [{"name": "sync_matrices", "annotation": null, "default": null, "kind": "positional"}, {"name": "threshold", "annotation": null, "default": "0.6", "kind": "positional"}]}, "investigate_cross_session_synchronization.analyze_temporal_patterns": {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "description": "Analyze the temporal patterns for evidence of systematic timing", "parameters": [{"name": "event_data", "annotation": null, "default": null, "kind": "positional"}, {"name": "sync_discoveries", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_cross_session_synchronization.test_synchronization_hypothesis": {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "description": "Test the core hypothesis: IF event@time occurs on Day_N, THEN probability increases on Day_N+1", "parameters": [{"name": "sync_discoveries", "annotation": null, "default": null, "kind": "positional"}, {"name": "event_data", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_cross_session_synchronization.create_synchronization_visualization": {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "description": "Create visualization of temporal synchronization patterns", "parameters": [{"name": "sync_discoveries", "annotation": null, "default": null, "kind": "positional"}, {"name": "event_data", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_cross_session_synchronization.main": {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "description": "Main cross-session synchronization investigation", "parameters": []}, "investigate_htf_structural_inheritance.extract_htf_ltf_relationships": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Extract HTF context and corresponding LTF event characteristics", "parameters": []}, "investigate_htf_structural_inheritance.get_dominant_event": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Determine the dominant LTF event type", "parameters": [{"name": "ltf_characteristics", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_htf_structural_inheritance.analyze_htf_ltf_correlations": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Analyze correlations between HTF context and LTF event characteristics", "parameters": [{"name": "htf_ltf_data", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_htf_structural_inheritance.discover_htf_inheritance_rules": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Discover rules for how HTF states influence LTF event characteristics", "parameters": [{"name": "htf_ltf_data", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_htf_structural_inheritance.analyze_event_type_htf_preferences": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Analyze HTF context preferences for different LTF event types", "parameters": [{"name": "htf_ltf_data", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_htf_structural_inheritance.test_htf_coherence_hypothesis": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Test the specific hypothesis that discovered patterns align across timeframes", "parameters": [{"name": "htf_ltf_data", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_htf_structural_inheritance.create_htf_inheritance_visualization": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Create comprehensive visualization of HTF-LTF inheritance patterns", "parameters": [{"name": "htf_ltf_data", "annotation": null, "default": null, "kind": "positional"}, {"name": "correlation_results", "annotation": null, "default": null, "kind": "positional"}, {"name": "inheritance_rules", "annotation": null, "default": null, "kind": "positional"}, {"name": "coherence_results", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_htf_structural_inheritance.main": {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "description": "Main HTF structural inheritance investigation", "parameters": []}, "investigate_pattern_subarchitecture.load_tgat_patterns": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Load the 568 TGAT patterns with full feature data", "parameters": []}, "investigate_pattern_subarchitecture.load_feature_vectors": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Load 38D feature vectors from preserved graphs", "parameters": []}, "investigate_pattern_subarchitecture.analyze_pattern_archetypes": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Analyze the distribution and characteristics of the 3 main pattern types", "parameters": [{"name": "patterns", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_pattern_subarchitecture.discover_sub_patterns": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Discover sub-patterns using k-means clustering on 38D feature space", "parameters": [{"name": "features", "annotation": null, "default": null, "kind": "positional"}, {"name": "pattern_metadata", "annotation": null, "default": null, "kind": "positional"}, {"name": "n_clusters_range", "annotation": null, "default": "[3, 5, 7]", "kind": "positional"}]}, "investigate_pattern_subarchitecture.analyze_clusters": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Analyze the characteristics of discovered clusters", "parameters": [{"name": "features", "annotation": null, "default": null, "kind": "positional"}, {"name": "cluster_labels", "annotation": null, "default": null, "kind": "positional"}, {"name": "metadata", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_pattern_subarchitecture.calculate_silhouette_score": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Calculate silhouette score for cluster quality assessment", "parameters": [{"name": "features", "annotation": null, "default": null, "kind": "positional"}, {"name": "labels", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_pattern_subarchitecture.visualize_sub_patterns": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Create visualization of discovered sub-patterns", "parameters": [{"name": "features", "annotation": null, "default": null, "kind": "positional"}, {"name": "cluster_labels", "annotation": null, "default": null, "kind": "positional"}, {"name": "cluster_analysis", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_pattern_subarchitecture.characterize_sub_patterns": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Characterize the discovered sub-patterns with detailed analysis", "parameters": [{"name": "cluster_analysis", "annotation": null, "default": null, "kind": "positional"}, {"name": "features", "annotation": null, "default": null, "kind": "positional"}, {"name": "cluster_labels", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_pattern_subarchitecture.main": {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "description": "Main sub-pattern discovery analysis", "parameters": []}, "phase2_feature_pipeline_enhancement.main": {"file": "scripts/analysis/phase2_feature_pipeline_enhancement.py", "description": "Main execution for Phase 2 Feature Pipeline Enhancement.", "parameters": []}, "phase4_full_scale_archaeological_discovery.run_phase4a_full_scale": {"file": "scripts/analysis/phase4_full_scale_archaeological_discovery.py", "description": "Run Phase 4a full scale archaeological discovery.", "parameters": []}, "phase4b_attention_head_analysis.run_phase4b_attention_analysis": {"file": "scripts/analysis/phase4b_attention_head_analysis.py", "description": "Run Phase 4b attention head analysis.", "parameters": []}, "phase4b_attention_head_analysis.attention_hook": {"file": "scripts/analysis/phase4b_attention_head_analysis.py", "description": null, "parameters": [{"name": "head_idx", "annotation": null, "default": null, "kind": "positional"}]}, "phase4b_attention_head_analysis.hook_fn": {"file": "scripts/analysis/phase4b_attention_head_analysis.py", "description": null, "parameters": [{"name": "module", "annotation": null, "default": null, "kind": "positional"}, {"name": "input", "annotation": null, "default": null, "kind": "positional"}, {"name": "output", "annotation": null, "default": null, "kind": "positional"}]}, "phase4b_attention_verification.run_phase4b_attention_analysis": {"file": "scripts/analysis/phase4b_attention_verification.py", "description": "Run Phase 4b attention head analysis.", "parameters": []}, "phase4c_temporal_resonance.run_phase4c_temporal_resonance": {"file": "scripts/analysis/phase4c_temporal_resonance.py", "description": "Run Phase 4c temporal resonance analysis.", "parameters": []}, "phase4c_temporal_resonance.extract_date": {"file": "scripts/analysis/phase4c_temporal_resonance.py", "description": null, "parameters": [{"name": "session_name", "annotation": null, "default": null, "kind": "positional"}]}, "phase5_archaeological_discovery_validation.main": {"file": "scripts/analysis/phase5_archaeological_discovery_validation.py", "description": "Execute Phase 5 Archaeological Discovery Validation", "parameters": []}, "phase5_direct_tgat_validation.make_serializable": {"file": "scripts/analysis/phase5_direct_tgat_validation.py", "description": "Convert complex objects to JSON-serializable format.\n<PERSON>les RichNodeFeature, torch.Tensor, and other complex objects.", "parameters": [{"name": "obj", "annotation": null, "default": null, "kind": "positional"}]}, "phase5_direct_tgat_validation.main": {"file": "scripts/analysis/phase5_direct_tgat_validation.py", "description": "Execute Phase 5 Direct TGAT Validation", "parameters": []}, "phase5_enhanced_session_validation.create_mock_graph_from_session": {"file": "scripts/analysis/phase5_enhanced_session_validation.py", "description": "Convert enhanced session data to graph format for TGAT\nReturns: (X, edge_index, edge_times, edge_attr, metadata)", "parameters": [{"name": "session_data", "annotation": "Dict", "default": null, "kind": "positional"}]}, "phase5_enhanced_session_validation.test_enhanced_session_pattern_discovery": {"file": "scripts/analysis/phase5_enhanced_session_validation.py", "description": "Test TGAT pattern discovery on enhanced sessions", "parameters": []}, "phase5_enhanced_session_validation.analyze_pattern_quality_improvement": {"file": "scripts/analysis/phase5_enhanced_session_validation.py", "description": "Analyze if enhanced sessions show improved pattern quality", "parameters": [{"name": "results", "annotation": "List[Dict]", "default": null, "kind": "positional"}]}, "phase5_enhanced_session_validation.main": {"file": "scripts/analysis/phase5_enhanced_session_validation.py", "description": "Run enhanced session validation", "parameters": []}, "process_all_sessions.process_all_sessions": {"file": "scripts/analysis/process_all_sessions.py", "description": "Process all available sessions with time pattern analysis", "parameters": []}, "process_all_sessions.analyze_all_time_patterns": {"file": "scripts/analysis/process_all_sessions.py", "description": "Analyze time patterns from all preserved graphs", "parameters": []}, "quick_pattern_discovery.main": {"file": "scripts/analysis/quick_pattern_discovery.py", "description": null, "parameters": []}, "real_pattern_finder.extract_price_from_node_feature": {"file": "scripts/analysis/real_pattern_finder.py", "description": "Extract price from RichNodeFeature string representation", "parameters": [{"name": "feature_str", "annotation": null, "default": null, "kind": "positional"}]}, "real_pattern_finder.find_real_patterns": {"file": "scripts/analysis/real_pattern_finder.py", "description": "Find ONE specific real pattern across all sessions:\n- 1m node near 23,000 level  \n- Has scale edge to 15m/1h/5m node\n- Parent node has PD Array or other structure", "parameters": []}, "run_archaeology_demonstration.setup_demonstration_environment": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Setup demonstration environment", "parameters": []}, "run_archaeology_demonstration.generate_synthetic_archaeological_events": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Generate comprehensive synthetic archaeological events", "parameters": []}, "run_archaeology_demonstration.demonstrate_lattice_mapping": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Demonstrate lattice mapping capabilities", "parameters": [{"name": "events", "annotation": "List[DemoArchaeologicalEvent]", "default": null, "kind": "positional"}]}, "run_archaeology_demonstration.demonstrate_temporal_clustering": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Demonstrate temporal clustering analysis", "parameters": [{"name": "events", "annotation": "List[DemoArchaeologicalEvent]", "default": null, "kind": "positional"}]}, "run_archaeology_demonstration.demonstrate_structural_analysis": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Demonstrate structural relationship analysis", "parameters": [{"name": "events", "annotation": "List[DemoArchaeologicalEvent]", "default": null, "kind": "positional"}]}, "run_archaeology_demonstration.create_demonstration_visualizations": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Create comprehensive demonstration visualizations", "parameters": [{"name": "lattice_dataset", "annotation": null, "default": null, "kind": "positional"}, {"name": "clustering_analysis", "annotation": null, "default": null, "kind": "positional"}, {"name": "structural_analysis", "annotation": null, "default": null, "kind": "positional"}]}, "run_archaeology_demonstration.generate_demonstration_report": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Generate comprehensive demonstration report", "parameters": [{"name": "events", "annotation": null, "default": null, "kind": "positional"}, {"name": "lattice_results", "annotation": null, "default": null, "kind": "positional"}, {"name": "clustering_results", "annotation": null, "default": null, "kind": "positional"}, {"name": "structural_results", "annotation": null, "default": null, "kind": "positional"}, {"name": "visualizations", "annotation": null, "default": null, "kind": "positional"}]}, "run_archaeology_demonstration.main": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": "Main demonstration workflow", "parameters": []}, "run_archaeology_demonstration.find_cascade_paths": {"file": "scripts/analysis/run_archaeology_demonstration.py", "description": null, "parameters": [{"name": "start_event", "annotation": null, "default": null, "kind": "positional"}, {"name": "current_path", "annotation": null, "default": null, "kind": "positional"}, {"name": "current_links", "annotation": null, "default": null, "kind": "positional"}]}, "run_contaminated_session_enhancement.get_contaminated_sessions": {"file": "scripts/analysis/run_contaminated_session_enhancement.py", "description": "Get list of contaminated sessions that need enhancement.", "parameters": []}, "run_contaminated_session_enhancement.main": {"file": "scripts/analysis/run_contaminated_session_enhancement.py", "description": "Run enhancement on contaminated sessions.", "parameters": []}, "run_enhanced_adapter_demonstration.run_quick_demo": {"file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "description": "Run a quick demonstration with 3 sessions", "parameters": []}, "run_enhanced_adapter_demonstration.run_full_demo": {"file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "description": "Run full demonstration with more sessions", "parameters": []}, "run_enhanced_adapter_demonstration.run_integration_test": {"file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "description": "Run integration test simulating production environment", "parameters": []}, "run_full_archaeology_discovery.setup_production_environment": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Setup production environment for archaeological discovery", "parameters": []}, "run_full_archaeology_discovery.discover_archaeological_phenomena": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Run comprehensive archaeological discovery", "parameters": []}, "run_full_archaeology_discovery.generate_lattice_mapping": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Generate complete lattice mapping with hot zones", "parameters": [{"name": "archaeological_events", "annotation": null, "default": null, "kind": "positional"}]}, "run_full_archaeology_discovery.analyze_temporal_patterns": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Analyze temporal clustering patterns", "parameters": [{"name": "archaeological_events", "annotation": null, "default": null, "kind": "positional"}]}, "run_full_archaeology_discovery.analyze_structural_relationships": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Analyze structural links and cascade patterns", "parameters": [{"name": "archaeological_events", "annotation": null, "default": null, "kind": "positional"}]}, "run_full_archaeology_discovery.create_comprehensive_visualizations": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Create comprehensive visualization suite", "parameters": [{"name": "lattice_dataset", "annotation": null, "default": null, "kind": "positional"}, {"name": "clustering_analysis", "annotation": null, "default": null, "kind": "positional"}, {"name": "structural_analysis", "annotation": null, "default": null, "kind": "positional"}]}, "run_full_archaeology_discovery.generate_executive_report": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Generate executive summary report", "parameters": [{"name": "discovery_results", "annotation": null, "default": null, "kind": "positional"}]}, "run_full_archaeology_discovery.main": {"file": "scripts/analysis/run_full_archaeology_discovery.py", "description": "Main production archaeological discovery workflow", "parameters": []}, "run_full_scale_discovery.run_full_scale_discovery": {"file": "scripts/analysis/run_full_scale_discovery.py", "description": "Execute full-scale archaeological discovery on all 66 sessions", "parameters": []}, "run_htf_orchestrator.main": {"file": "scripts/analysis/run_htf_orchestrator.py", "description": null, "parameters": []}, "lattice_population_runner.main": {"file": "scripts/utilities/lattice_population_runner.py", "description": "Run the complete lattice population", "parameters": []}}}, "learning": {"classes": {"contracts.DiscoveryResult": {"file": "ironforge/contracts.py", "description": null, "methods": []}, "discovery_pipeline.TemporalDiscoveryPipeline": {"file": "ironforge/learning/discovery_pipeline.py", "description": "Shard‑aware pipeline for temporal TGAT discovery.\n\nParameters\n----------\ndata_path : str or Path\n    Directory containing Parquet shards.\nnum_neighbors : list[int], optional\n    Fan‑out per TGAT layer (defaults to [10, 10, 5]).\nbatch_size : int\n    Mini‑batch size for neighbour sampling.\ntime_window : int, optional\n    Temporal window (hours) to restrict neighbours.\nstitch_policy : str\n    Policy for anchor stitching (\"session\" or \"global\").\ndevice : str, optional\n    Device identifier; falls back to CPU if unavailable.", "methods": ["load_shards", "build_temporal_graph", "create_neighbor_loader", "stitch_anchors", "run_discovery", "run"]}, "enhanced_graph_builder.RichNodeFeature": {"file": "ironforge/learning/enhanced_graph_builder.py", "description": "45D node feature vector with semantic preservation", "methods": ["set_semantic_event", "set_traditional_features"]}, "enhanced_graph_builder.RichEdgeFeature": {"file": "ironforge/learning/enhanced_graph_builder.py", "description": "20D edge feature vector with semantic relationships", "methods": ["set_semantic_relationship"]}, "enhanced_graph_builder.EnhancedGraphBuilder": {"file": "ironforge/learning/enhanced_graph_builder.py", "description": "Enhanced graph builder for archaeological discovery\nTransforms JSON session data into rich 45D/20D graph representations", "methods": ["build_session_graph", "validate_theory_b_implementation", "extract_features_for_tgat"]}, "tgat_discovery.TemporalAttentionLayer": {"file": "ironforge/learning/tgat_discovery.py", "description": "Multi-head temporal attention for 45D node features", "methods": ["forward"]}, "tgat_discovery.IRONFORGEDiscovery": {"file": "ironforge/learning/tgat_discovery.py", "description": "IRONFORGE Discovery Engine using TGAT\nArchaeological pattern discovery through temporal graph attention", "methods": ["forward", "discover_session_patterns"]}, "orchestrator.IRONFORGE": {"file": "orchestrator.py", "description": "Main orchestrator for discovery system", "methods": ["graph_builder", "discovery_engine", "graduation_pipeline", "process_sessions", "validate_discoveries", "freeze_for_production", "generate_performance_report"]}}, "functions": {"igraph_builder.from_parquet": {"file": "ironforge/graph_builder/igraph_builder.py", "description": "Create igraph Graph from nodes and edges DataFrames.", "parameters": [{"name": "nodes", "annotation": "pd.DataFrame", "default": null, "kind": "positional"}, {"name": "edges", "annotation": "pd.DataFrame", "default": null, "kind": "positional"}]}, "pyg_converters.igraph_to_pyg": {"file": "ironforge/graph_builder/pyg_converters.py", "description": "Convert igraph Graph to PyTorch Geometric Data object.", "parameters": [{"name": "g", "annotation": null, "default": null, "kind": "positional"}]}, "enhanced_graph_builder.get_zone": {"file": "ironforge/learning/enhanced_graph_builder.py", "description": null, "parameters": [{"name": "pos", "annotation": null, "default": null, "kind": "positional"}]}, "cli.main": {"file": "ironforge/sdk/cli.py", "description": "Entry point for the `ironforge` CLI.", "parameters": [{"name": "argv", "annotation": "list[str] | None", "default": "None", "kind": "positional"}]}}}, "synthesis": {"classes": {"pattern_graduation.PatternGraduation": {"file": "ironforge/synthesis/pattern_graduation.py", "description": "Validation system ensuring discovered patterns exceed 87% baseline accuracy", "methods": ["validate_patterns", "get_graduation_summary"]}, "production_graduation.ProductionGraduation": {"file": "ironforge/synthesis/production_graduation.py", "description": "Production feature export for graduated patterns\nConverts validated archaeological discoveries into production-ready features\n\nNOTE: This class maintains NO STATE between sessions to ensure complete session independence.\nEach export is completely isolated and cannot be contaminated by previous sessions.", "methods": ["export_graduated_patterns", "get_production_summary"]}, "runner.ValidationConfig": {"file": "ironforge/validation/runner.py", "description": "Configuration for validation experiments.\n\nParameters\n----------\nmode : str\n    Validation mode: \"oos\", \"purged-kfold\", or \"holdout\".\nfolds : int\n    Number of folds for k-fold validation.\nembargo_mins : int\n    Embargo period in minutes to prevent look-ahead bias.\ncontrols : List[str]\n    Negative controls to run: [\"time_shuffle\", \"label_perm\", etc.].\nablations : List[str]\n    Feature groups to ablate: [\"htf_prox\", \"cycles\", \"structure\"].\nreport_dir : Path\n    Directory to write validation reports.\nrandom_seed : int\n    Base random seed for reproducibility.", "methods": []}, "runner.ValidationRunner": {"file": "ironforge/validation/runner.py", "description": "Orchestrates comprehensive validation experiments.", "methods": ["run"]}}, "functions": {"metrics.precision_at_k": {"file": "ironforge/validation/metrics.py", "description": "Calculate precision at top-k predictions.\n\nParameters\n----------\ny_true : Sequence[int]\n    True binary labels (0 or 1).\ny_score : Sequence[float]\n    Prediction scores (higher = more likely positive).\nk : int\n    Number of top predictions to consider.\n\nReturns\n-------\nfloat\n    Precision at k (0.0 to 1.0).\n\nExamples\n--------\n>>> y_true = [1, 0, 1, 0, 1, 0]\n>>> y_score = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4]\n>>> precision_at_k(y_true, y_score, k=3)\n0.6666666666666666", "parameters": [{"name": "y_true", "annotation": "Sequence[int]", "default": null, "kind": "positional"}, {"name": "y_score", "annotation": "Sequence[float]", "default": null, "kind": "positional"}, {"name": "k", "annotation": "int", "default": "20", "kind": "positional"}]}, "metrics.temporal_auc": {"file": "ironforge/validation/metrics.py", "description": "AUC computed with chronological tie-breaking to reduce look-ahead bias.\n\nParameters\n----------\ny_true : Sequence[int]\n    True binary labels.\ny_score : Sequence[float]\n    Prediction scores.\ntimestamps : Sequence[int]\n    Timestamps for chronological ordering.\n\nReturns\n-------\nfloat\n    Temporal AUC score (0.0 to 1.0).", "parameters": [{"name": "y_true", "annotation": "Sequence[int]", "default": null, "kind": "positional"}, {"name": "y_score", "annotation": "Sequence[float]", "default": null, "kind": "positional"}, {"name": "timestamps", "annotation": "Sequence[int]", "default": null, "kind": "positional"}]}, "metrics.motif_half_life": {"file": "ironforge/validation/metrics.py", "description": "Estimate stability/decay using exponential fit on inter-hit intervals.\n\nParameters\n----------\nhits_timestamps : Sequence[int]\n    Timestamps when patterns/motifs were detected.\n\nReturns\n-------\nfloat\n    Estimated half-life in the same units as timestamps.\n    Higher values indicate more stable/persistent patterns.\n\nExamples\n--------\n>>> hits = [100, 120, 140, 180, 250]  # Increasing intervals\n>>> half_life = motif_half_life(hits)\n>>> # Returns estimated half-life of pattern occurrence", "parameters": [{"name": "hits_timestamps", "annotation": "Sequence[int]", "default": null, "kind": "positional"}]}, "metrics.pattern_stability_score": {"file": "ironforge/validation/metrics.py", "description": "Measure temporal stability of pattern scores across time windows.\n\nParameters\n----------\ny_score : Sequence[float]\n    Pattern confidence scores.\ntimestamps : Sequence[int]\n    Corresponding timestamps.\nwindow_size : int\n    Size of time windows for stability analysis.\n\nReturns\n-------\nfloat\n    Stability score (0.0 = unstable, 1.0 = perfectly stable).", "parameters": [{"name": "y_score", "annotation": "Sequence[float]", "default": null, "kind": "positional"}, {"name": "timestamps", "annotation": "Sequence[int]", "default": null, "kind": "positional"}, {"name": "window_size", "annotation": "int", "default": "60", "kind": "positional"}]}, "metrics.archaeological_significance": {"file": "ironforge/validation/metrics.py", "description": "Calculate archaeological significance metrics for discovered patterns.\n\nParameters\n----------\npattern_scores : Sequence[float]\n    Confidence scores for discovered patterns.\npattern_types : Sequence[str]\n    Types/categories of discovered patterns.\ntemporal_spans : Sequence[float]\n    Duration spans of each pattern.\n\nReturns\n-------\nDict[str, float]\n    Archaeological significance metrics.", "parameters": [{"name": "pattern_scores", "annotation": "Sequence[float]", "default": null, "kind": "positional"}, {"name": "pattern_types", "annotation": "Sequence[str]", "default": null, "kind": "positional"}, {"name": "temporal_spans", "annotation": "Sequence[float]", "default": null, "kind": "positional"}]}, "metrics.compute_validation_metrics": {"file": "ironforge/validation/metrics.py", "description": "Compute comprehensive validation metrics for pattern discovery.\n\nParameters\n----------\ny_true : Sequence[int]\n    True binary labels.\ny_score : Sequence[float]\n    Prediction scores.\ntimestamps : Sequence[int]\n    Temporal timestamps.\npattern_metadata : Dict, optional\n    Additional pattern information for archaeological metrics.\nk_values : Sequence[int]\n    K values for precision@k calculation.\n\nReturns\n-------\nDict[str, float]\n    Comprehensive metrics dictionary.", "parameters": [{"name": "y_true", "annotation": "Sequence[int]", "default": null, "kind": "positional"}, {"name": "y_score", "annotation": "Sequence[float]", "default": null, "kind": "positional"}, {"name": "timestamps", "annotation": "Sequence[int]", "default": null, "kind": "positional"}, {"name": "pattern_metadata", "annotation": "dict | None", "default": "None", "kind": "positional"}, {"name": "k_values", "annotation": "Sequence[int]", "default": "(5, 10, 20)", "kind": "positional"}]}}}, "integration": {"classes": {"config.IRONFORGEConfig": {"file": "config.py", "description": "Configuration manager for IRONFORGE system.\n\nEliminates hardcoded paths and provides environment-specific configuration.", "methods": ["get_path", "get_data_path", "get_preservation_path", "get_graphs_path", "get_embeddings_path", "get_discoveries_path", "get_reports_path", "get_htf_data_path", "get_session_data_path", "get_enhanced_data_path", "get_adapted_data_path", "get_integration_path", "get_workspace_root", "to_dict", "save_config"]}, "adaptive_rg_optimizer.AdaptiveRGParameters": {"file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Optimized RG parameters for current market regime", "methods": []}, "adaptive_rg_optimizer.ThresholdOptimizationResult": {"file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Result from information-theoretic threshold optimization", "methods": []}, "adaptive_rg_optimizer.ScalingCalibrationResult": {"file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Result from RG scaling exponent calibration", "methods": []}, "adaptive_rg_optimizer.AdaptiveRGOptimizer": {"file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Mathematical Physics Engine for RG Optimization\n\nImplements the complete transformation from heuristic to mathematical\napproaches using patterns discovered in enhanced_rg_scaler.py,\nfisher_information_monitor.py, and constraints.py.", "methods": ["optimize_information_theoretic_thresholds", "calibrate_rg_scaling_exponents", "create_adaptive_coupling_matrix", "integrate_historical_data", "refactor_and_eliminate_fallbacks", "optimize_complete_system"]}, "cascade_classifier.CascadeType": {"file": "iron_core/mathematical/cascade_classifier.py", "description": null, "methods": []}, "cascade_classifier.CascadeEvent": {"file": "iron_core/mathematical/cascade_classifier.py", "description": "Individual cascade event with full context", "methods": []}, "cascade_classifier.CascadeSequence": {"file": "iron_core/mathematical/cascade_classifier.py", "description": "Sequence of related cascade events", "methods": []}, "cascade_classifier.CascadeClassifier": {"file": "iron_core/mathematical/cascade_classifier.py", "description": "Advanced cascade classification system with sequential analysis\n\nClassifies individual cascade events and analyzes temporal sequences\nfor pattern recognition and correlation analysis.", "methods": ["classify_cascade", "create_cascade_event", "analyze_temporal_sequences", "get_classification_summary", "export_analysis"]}, "constraints.SystemConstants": {"file": "iron_core/mathematical/constraints.py", "description": "Core mathematical invariants - DO NOT MODIFY", "methods": []}, "constraints.HTFConstants": {"file": "iron_core/mathematical/constraints.py", "description": "Higher Timeframe (HTF) System Parameters - IMMUTABLE", "methods": []}, "constraints.RGConstants": {"file": "iron_core/mathematical/constraints.py", "description": "Renormalization Group (RG) Scaling Constants - IMMUTABLE", "methods": []}, "constraints.FPFVGConstants": {"file": "iron_core/mathematical/constraints.py", "description": "Fair Price Fair Value Gap (FPFVG) Constants - 87.5% Contamination Filtering", "methods": []}, "constraints.CascadeType": {"file": "iron_core/mathematical/constraints.py", "description": "CASCADE_TYPES v1.0 - IMMUTABLE Taxonomy", "methods": []}, "constraints.CASCADE_TYPES_V1": {"file": "iron_core/mathematical/constraints.py", "description": "Immutable cascade classification system - DO NOT MODIFY", "methods": ["get_all_types", "classify_cascade"]}, "constraints.TheoryWeights": {"file": "iron_core/mathematical/constraints.py", "description": "Multi-Theory Integration Weights - IMMUTABLE", "methods": ["get_weights_dict", "validate_weights_sum"]}, "constraints.ConsensusThresholds": {"file": "iron_core/mathematical/constraints.py", "description": "Multi-Theory Consensus Decision Thresholds", "methods": []}, "constraints.SessionPhases": {"file": "iron_core/mathematical/constraints.py", "description": "Session Phase Enumeration", "methods": []}, "constraints.SessionTypes": {"file": "iron_core/mathematical/constraints.py", "description": "Trading Session Types", "methods": []}, "constraints.BusinessRules": {"file": "iron_core/mathematical/constraints.py", "description": "Domain-Specific Business Logic - Critical for System Accuracy", "methods": ["calculate_energy_density", "calculate_volatility_adjusted_threshold", "calculate_power_law_multiplier", "calculate_htf_intensity", "calculate_rg_scale", "apply_energy_carryover", "is_htf_activation_threshold_met", "calculate_synthetic_volume_detection_score"]}, "constraints.ValidationRules": {"file": "iron_core/mathematical/constraints.py", "description": "Validation rules for mathematical integrity", "methods": ["validate_cascade_classification", "validate_energy_conservation", "validate_theory_weights", "validate_htf_parameters"]}, "fisher_information_monitor.FisherSpikeResult": {"file": "iron_core/mathematical/fisher_information_monitor.py", "description": "Result from Fisher Information spike analysis", "methods": []}, "fisher_information_monitor.RegimeTransition": {"file": "iron_core/mathematical/fisher_information_monitor.py", "description": "Detected regime transition event", "methods": []}, "fisher_information_monitor.FisherInformationMonitor": {"file": "iron_core/mathematical/fisher_information_monitor.py", "description": "Fisher Information Spike Detection System\n\nMonitors Fisher Information to detect the critical crystallization point\nwhere market behavior transitions from probabilistic to deterministic.", "methods": ["calculate_fisher_information", "is_red_alert_active", "get_current_regime", "get_monitoring_summary", "reset_monitoring_state"]}, "grammar_fisher_correlator.GrammarParseState": {"file": "iron_core/mathematical/grammar_fisher_correlator.py", "description": "Current state of grammatical parsing with Fisher correlation", "methods": []}, "grammar_fisher_correlator.FisherGrammarCorrelation": {"file": "iron_core/mathematical/grammar_fisher_correlator.py", "description": "Correlation between Fisher spike and grammar phrase boundary", "methods": []}, "grammar_fisher_correlator.GrammarFisherCorrelator": {"file": "iron_core/mathematical/grammar_fisher_correlator.py", "description": "Predictive parser that tracks Fisher Information as parsing confidence\n\nKey Discovery: Fisher spikes occur at grammatical phrase boundaries where\nmultiple parse paths converge to a single continuation, eliminating ambiguity\nand triggering deterministic cascade prediction.", "methods": ["parse_market_sequence", "predict_next_event", "is_deterministic_mode_active", "get_correlation_summary", "reset_parser_state"]}, "hawkes_engine.HawkesParameters": {"file": "iron_core/mathematical/hawkes_engine.py", "description": "Parameters for Hawke<PERSON> process", "methods": []}, "hawkes_engine.HawkesEvent": {"file": "iron_core/mathematical/hawkes_engine.py", "description": "Individual event in Hawkes process", "methods": []}, "hawkes_engine.HawkesEngine": {"file": "iron_core/mathematical/hawkes_engine.py", "description": "Enhanced Multi-Dimensional Hawkes Process Engine\n\nImplements proven HTF coupling with multi-dimensional enhancements\nwhile preserving all validated domain knowledge.", "methods": ["calculate_intensity", "calculate_multi_dimensional_intensity", "add_event", "predict_next_event_time", "calculate_htf_coupling", "apply_energy_carryover", "get_process_statistics", "reset_process"]}, "invariants.DriftEvent": {"file": "iron_core/mathematical/invariants.py", "description": "Record of architectural drift detection", "methods": []}, "invariants.Contract": {"file": "iron_core/mathematical/invariants.py", "description": "Semantic binding contract for functions", "methods": []}, "invariants.InvariantGuard": {"file": "iron_core/mathematical/invariants.py", "description": "Minimal viable architectural control system", "methods": ["register", "checkpoint", "function_health", "export_report"]}, "mathematical_hooks.HookType": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Types of mathematical model hooks", "methods": []}, "mathematical_hooks.AlertLevel": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Alert severity levels", "methods": []}, "mathematical_hooks.RecoveryAction": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Automated recovery actions", "methods": []}, "mathematical_hooks.HookContext": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Context information for hook execution", "methods": []}, "mathematical_hooks.AlertEvent": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Mathematical model alert event", "methods": []}, "mathematical_hooks.MathematicalHook": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Base class for mathematical model hooks", "methods": ["execute", "safe_execute"]}, "mathematical_hooks.ParameterDriftHook": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Detects parameter drift in mathematical models.\nUses statistical tests and trend analysis to identify when parameters\ndeviate significantly from their historical values.", "methods": ["execute"]}, "mathematical_hooks.PerformanceDegradationHook": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Monitors mathematical model performance degradation.\nTracks execution time, memory usage, and accuracy metrics.", "methods": ["execute"]}, "mathematical_hooks.MathematicalInvariantValidationHook": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Validates mathematical invariants and constraints.\nEnsures mathematical models maintain their theoretical properties.", "methods": ["execute"]}, "mathematical_hooks.HookManager": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Central manager for mathematical model hooks.\nCoordinates hook execution, manages alerts, and handles recovery actions.", "methods": ["register_hook", "trigger_hooks", "setup_standard_hooks", "get_active_alerts", "get_hook_performance_summary"]}, "api_interface.PredictionRequest": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Request schema for mathematical predictions", "methods": []}, "api_interface.PredictionResponse": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Response schema for mathematical predictions", "methods": []}, "api_interface.ValidationRequest": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Request schema for model validation", "methods": []}, "api_interface.ValidationResponse": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Response schema for model validation", "methods": []}, "api_interface.OptimizationRequest": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Request schema for parameter optimization", "methods": []}, "api_interface.OptimizationResponse": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Response schema for parameter optimization", "methods": []}, "api_interface.StatusResponse": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Response schema for system status", "methods": []}, "api_interface.APIInterfaceLayer": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Base class for API interface implementations.\nProvides framework for exposing mathematical models via APIs.", "methods": ["expose_prediction_endpoint", "health_check_endpoint", "start_api_server"]}, "api_interface.MathematicalModelAPI": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "FastAPI-based REST API for mathematical models.\nProvides comprehensive API endpoints for all mathematical operations.", "methods": ["expose_prediction_endpoint", "health_check_endpoint", "start_api_server"]}, "api_interface.BaseModel": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": null, "methods": []}, "api_interface.FastAPI": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": null, "methods": []}, "core_algorithms.AlgorithmPerformanceMetrics": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "Performance metrics for algorithm implementations", "methods": []}, "core_algorithms.CoreAlgorithmLayer": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "Base class for high-performance algorithm implementations.\nFocuses on computational efficiency and numerical stability.", "methods": ["initialize_parameters", "compute_core_function", "optimize_parameters", "benchmark_performance"]}, "core_algorithms.HawkesAlgorithmImplementation": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "High-performance Hawkes process implementation with numerical optimizations.\nBased on Oracle system validated formula: λ(t) = μ + Σ α·exp(-β(t-t_j))", "methods": ["initialize_parameters", "compute_core_function", "optimize_parameters", "benchmark_performance"]}, "core_algorithms.FFTOptimizedCorrelator": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "FFT-based correlation optimization reducing O(n²) to O(n log n).\nOptimizes cross-session temporal correlation analysis.", "methods": ["initialize_parameters", "compute_core_function", "optimize_parameters", "benchmark_performance"]}, "core_algorithms.QuantumInspiredOptimizer": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "Quantum-inspired optimization for complex parameter spaces.\nUses simulated annealing with quantum tunneling effects.", "methods": ["initialize_parameters", "compute_core_function", "optimize_parameters", "benchmark_performance"]}, "integration_layer.IntegrationStatus": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Status of model integration", "methods": []}, "integration_layer.ModelPriority": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Priority levels for model execution", "methods": []}, "integration_layer.ModelMetadata": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Metadata for registered mathematical models", "methods": []}, "integration_layer.ModelChainStep": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Individual step in a model execution chain", "methods": []}, "integration_layer.ModelChain": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Chain of mathematical models for complex predictions.\nSupports conditional execution, data transformation, and error handling.", "methods": ["add_step", "add_hawkes_prediction", "add_htf_coupling", "add_three_oracle_consensus"]}, "integration_layer.IntegrationLayer": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Base class for mathematical model integration with business systems.\nProvides framework for model registration, execution, and monitoring.", "methods": ["register_model", "create_model_chain", "execute_prediction_pipeline", "get_integration_status"]}, "integration_layer.MathematicalModelRegistry": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Registry for mathematical models with Oracle system integration.\nManages model lifecycle, dependencies, and performance monitoring.", "methods": ["register_model", "create_model_chain", "execute_prediction_pipeline", "get_integration_status", "get_model_performance_summary"]}, "theory_abstraction.MathematicalDomain": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Mathematical domains for model classification", "methods": []}, "theory_abstraction.MathematicalParameters": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Type-safe mathematical parameter container with constraints", "methods": []}, "theory_abstraction.MathematicalModel": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Protocol/Interface for mathematical models.\nDefines the contract that all mathematical models must satisfy.", "methods": ["mathematical_definition", "parameter_space", "computational_complexity", "mathematical_properties"]}, "theory_abstraction.TheoryAbstractionLayer": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Base class for theoretical mathematical model abstractions.\nFocuses purely on mathematical theory without implementation concerns.", "methods": ["define_mathematical_model", "specify_constraints", "validate_theoretical_consistency", "derive_theoretical_properties"]}, "theory_abstraction.HawkesTheoryAbstraction": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Theoretical abstraction of Hawkes processes.\nBased on the validated Oracle system formulation:\nλ(t) = μ + Σ α·exp(-β(t-t_j))", "methods": ["mathematical_definition", "parameter_space", "computational_complexity", "mathematical_properties", "define_mathematical_model", "specify_constraints", "validate_theoretical_consistency", "derive_theoretical_properties"]}, "theory_abstraction.HTFTheoryAbstraction": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Higher Time Frame (HTF) theoretical abstraction.\nMulti-scale coupling between HTF and session-level processes.\nλ_total(t) = λ_session(t) + γ(t)·λ_HTF(t)", "methods": ["mathematical_definition", "parameter_space", "computational_complexity", "mathematical_properties", "define_mathematical_model", "specify_constraints", "validate_theoretical_consistency", "derive_theoretical_properties"]}, "theory_abstraction.InformationTheoreticModel": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Information-theoretic mathematical model for Three-Oracle consensus.\nBased on mutual information maximization and entropy optimization.", "methods": ["mathematical_definition", "parameter_space", "computational_complexity", "mathematical_properties", "define_mathematical_model", "specify_constraints", "validate_theoretical_consistency", "derive_theoretical_properties"]}, "rg_scaler_production.RGScalingResult": {"file": "iron_core/mathematical/rg_scaler_production.py", "description": "Results from RG scaling transformation", "methods": []}, "rg_scaler_production.RGScaler": {"file": "iron_core/mathematical/rg_scaler_production.py", "description": "Production RG Scaler - The Universal Lens\n\nImplements the experimentally-discovered inverse scaling law that transforms\nraw event data into optimally-scaled time bins. This is the mandatory first\nstage of all data processing in the Oracle architecture.\n\nKey Discovery: s(d) = 15 - 5*log₁₀(d) with correlation -0.9197\n\nUsage:\n    scaler = RGScaler()\n    result = scaler.transform(event_timestamps)\n    # All downstream components use result.binned_counts", "methods": ["calculate_event_density", "inverse_scaling_law", "classify_density_regime", "transform", "transform_session_data", "get_scaling_statistics"]}, "scaling_patterns.ScalingStrategy": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Available scaling strategies", "methods": []}, "scaling_patterns.ComputationComplexity": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Computation complexity levels", "methods": []}, "scaling_patterns.ScalingConfig": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Configuration for scaling operations", "methods": []}, "scaling_patterns.ScalingMetrics": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Metrics from scaling operations", "methods": []}, "scaling_patterns.ScalingPattern": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Abstract base class for scaling patterns", "methods": ["execute", "estimate_resources"]}, "scaling_patterns.HorizontalScalingPattern": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Horizontal scaling with data partitioning", "methods": ["execute", "estimate_resources"]}, "scaling_patterns.VerticalScalingPattern": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Vertical scaling with memory optimization", "methods": ["execute", "estimate_resources"]}, "scaling_patterns.AdaptiveScalingManager": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Manager for adaptive scaling strategy selection", "methods": ["execute_with_optimal_scaling", "get_performance_summary"]}, "container.IRONContainer": {"file": "iron_core/performance/container.py", "description": "IRON-Core dependency injection container.\n\nProvides unified infrastructure for mathematical components, lazy loading,\nand performance optimization across the entire IRON ecosystem.", "methods": ["get_mathematical_component", "get_component", "register_component", "get_performance_metrics", "get_lazy_manager"]}, "lazy_loader.LazyComponent": {"file": "iron_core/performance/lazy_loader.py", "description": "Lazy loading wrapper for mathematical components.\n\nDelays initialization until first access to eliminate startup bottlenecks.\nPreserves mathematical accuracy through validation hooks.", "methods": ["is_loaded", "load_time", "validation_passed"]}, "lazy_loader.MathematicalComponentLoader": {"file": "iron_core/performance/lazy_loader.py", "description": "Specialized loader for IRON ecosystem mathematical components.\n\nProvides validation functions for mathematical accuracy preservation:\n- RG Scaler: s(d) = 15 - 5*log₁₀(d) correlation validation\n- Fisher Monitor: F>1000 threshold validation\n- HTF Controller: β_h=0.00442 decay parameter validation", "methods": ["validate_rg_scaler", "validate_fisher_monitor", "validate_hawkes_engine", "validate_htf_controller"]}, "lazy_loader.LazyLoadingManager": {"file": "iron_core/performance/lazy_loader.py", "description": "Manager for lazy loading of IRON ecosystem mathematical components.\n\nImplements performance optimization strategy:\n- Component initialization <4.2ms average\n- Memory usage <400MB vs 2.4GB current  \n- Cache hit rate 80.9% for repeated access", "methods": ["register_component", "get_component", "get_performance_report", "register_standard_mathematical_components"]}, "ironforge_container.IRONFORGEContainer": {"file": "ironforge/integration/ironforge_container.py", "description": "Container for lazy loading IRONFORGE components\n\nNOTE: Components are created fresh for each session to ensure complete session independence.\nNo state is shared between sessions.", "methods": ["get_enhanced_graph_builder", "get_tgat_discovery", "get_pattern_graduation"]}}, "functions": {"config.get_config": {"file": "config.py", "description": "Get or create global IRONFORGE configuration.", "parameters": [{"name": "config_file", "annotation": "Optional[str]", "default": "None", "kind": "positional"}]}, "config.initialize_config": {"file": "config.py", "description": "Initialize IRONFORGE configuration system.", "parameters": [{"name": "config_file", "annotation": "Optional[str]", "default": "None", "kind": "positional"}]}, "adaptive_rg_optimizer.create_adaptive_rg_optimizer": {"file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Factory function for production adaptive RG optimizer", "parameters": [{"name": "historical_data_path", "annotation": "Optional[str]", "default": "None", "kind": "positional"}]}, "adaptive_rg_optimizer.entropy_objective": {"file": "iron_core/mathematical/adaptive_rg_optimizer.py", "description": "Objective function for maximum entropy threshold optimization", "parameters": [{"name": "threshold_params", "annotation": null, "default": null, "kind": "positional"}]}, "constraints.perform_system_integrity_check": {"file": "iron_core/mathematical/constraints.py", "description": "Perform complete system integrity check on all constants\nReturns dict of validation results", "parameters": []}, "invariants.architectural_control": {"file": "iron_core/mathematical/invariants.py", "description": "Convenience wrapper for guard.register", "parameters": [{"name": "name", "annotation": "str", "default": null, "kind": "positional"}, {"name": "inputs", "annotation": "str", "default": null, "kind": "positional"}, {"name": "outputs", "annotation": "str", "default": null, "kind": "positional"}, {"name": "purpose", "annotation": "str", "default": null, "kind": "positional"}]}, "invariants.demo_function": {"file": "iron_core/mathematical/invariants.py", "description": null, "parameters": [{"name": "events", "annotation": null, "default": null, "kind": "positional"}]}, "invariants.decorator": {"file": "iron_core/mathematical/invariants.py", "description": null, "parameters": [{"name": "func", "annotation": null, "default": null, "kind": "positional"}]}, "invariants.wrapper": {"file": "iron_core/mathematical/invariants.py", "description": null, "parameters": [{"name": "args", "annotation": null, "kind": "var_positional"}, {"name": "kwargs", "annotation": null, "kind": "var_keyword"}]}, "mathematical_hooks.create_oracle_hook_manager": {"file": "iron_core/mathematical/mathematical_hooks.py", "description": "Create HookManager with Oracle-specific configuration", "parameters": []}, "api_interface.create_mathematical_api": {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "description": "Create mathematical model API with registry and hooks", "parameters": [{"name": "model_registry", "annotation": "MathematicalModelRegistry", "default": null, "kind": "positional"}, {"name": "hook_manager", "annotation": "Optional[HookManager]", "default": "None", "kind": "positional"}]}, "core_algorithms.create_algorithm_factory": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "Factory for creating algorithm implementations", "parameters": []}, "core_algorithms.benchmark_all_algorithms": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "Benchmark all algorithm implementations", "parameters": []}, "core_algorithms.negative_log_likelihood": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": "Negative log-likelihood objective function", "parameters": [{"name": "params", "annotation": null, "default": null, "kind": "positional"}]}, "core_algorithms.quadratic_objective": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": null, "parameters": [{"name": "params", "annotation": null, "default": null, "kind": "positional"}]}, "core_algorithms.test_function": {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "description": null, "parameters": [{"name": "x", "annotation": null, "default": null, "kind": "positional"}]}, "integration_layer.create_oracle_prediction_chain": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Create standard Oracle prediction chain", "parameters": []}, "integration_layer.oracle_data_transform": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Transform Oracle session data for <PERSON><PERSON> input", "parameters": [{"name": "data", "annotation": null, "default": null, "kind": "positional"}]}, "integration_layer.hawkes_output_transform": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Transform Hawkes output for Oracle consumption", "parameters": [{"name": "intensities", "annotation": null, "default": null, "kind": "positional"}]}, "integration_layer.htf_conditional_execution": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Execute HTF coupling only if intensity exceeds threshold", "parameters": [{"name": "previous_output", "annotation": null, "default": null, "kind": "positional"}]}, "integration_layer.htf_parameter_adjustment": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Adjust HTF parameters based on previous predictions", "parameters": [{"name": "data", "annotation": null, "default": null, "kind": "positional"}]}, "integration_layer.consensus_input_transform": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Prepare data for Three-Oracle system", "parameters": [{"name": "data", "annotation": null, "default": null, "kind": "positional"}]}, "integration_layer.consensus_output_transform": {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "description": "Transform consensus output for final prediction", "parameters": [{"name": "consensus_result", "annotation": null, "default": null, "kind": "positional"}]}, "theory_abstraction.create_mathematical_model_factory": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Factory for creating mathematical model instances", "parameters": []}, "theory_abstraction.validate_all_mathematical_models": {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "description": "Validate consistency of all mathematical models", "parameters": []}, "rg_scaler_production.create_production_rg_scaler": {"file": "iron_core/mathematical/rg_scaler_production.py", "description": "Create production-ready RG Scaler instance\n\nArgs:\n    config: Optional configuration overrides\n    \nReturns:\n    Configured RGScaler instance", "parameters": [{"name": "config", "annotation": "Optional[Dict]", "default": "None", "kind": "positional"}]}, "scaling_patterns.sample_computation": {"file": "iron_core/mathematical/scaling_patterns.py", "description": "Sample mathematical computation", "parameters": [{"name": "data", "annotation": null, "default": null, "kind": "positional"}]}, "container.get_container": {"file": "iron_core/performance/container.py", "description": "Get global IRON-Core dependency injection container with thread-safe singleton pattern.\n\nThis implementation uses double-checked locking to ensure:\n1. Only one container instance is ever created across all threads\n2. Thread-safe initialization without performance penalty after creation\n3. Proper error handling and logging for concurrent access\n\nReturns:\n    IRONContainer: The singleton container instance\n    \nThread Safety:\n    This function is fully thread-safe and can be called concurrently\n    from multiple threads without race conditions or duplicate instances.", "parameters": []}, "container.initialize_container": {"file": "iron_core/performance/container.py", "description": "Initialize IRON-Core container with performance reporting.", "parameters": []}, "lazy_loader.get_lazy_manager": {"file": "iron_core/performance/lazy_loader.py", "description": "Get global lazy loading manager with thread-safe singleton pattern.\n\nThread Safety:\n    This function is fully thread-safe and ensures only one\n    LazyLoadingManager instance exists across all threads.\n\nReturns:\n    LazyLoadingManager: The singleton lazy loading manager", "parameters": []}, "lazy_loader.lazy_load": {"file": "iron_core/performance/lazy_loader.py", "description": "Decorator for lazy loading components (thread-safe).", "parameters": [{"name": "name", "annotation": "str", "default": null, "kind": "positional"}, {"name": "module_path", "annotation": "str", "default": null, "kind": "positional"}, {"name": "class_name", "annotation": "str", "default": null, "kind": "positional"}, {"name": "validation_func", "annotation": "Optional[Callable]", "default": "None", "kind": "positional"}]}, "lazy_loader.initialize_lazy_loading": {"file": "iron_core/performance/lazy_loader.py", "description": "Initialize lazy loading with standard IRON ecosystem components (thread-safe).", "parameters": []}, "lazy_loader.decorator": {"file": "iron_core/performance/lazy_loader.py", "description": null, "parameters": [{"name": "func", "annotation": null, "default": null, "kind": "positional"}]}, "lazy_loader.wrapper": {"file": "iron_core/performance/lazy_loader.py", "description": null, "parameters": [{"name": "args", "annotation": null, "kind": "var_positional"}, {"name": "kwargs", "annotation": null, "kind": "var_keyword"}]}, "ironforge_container.get_ironforge_container": {"file": "ironforge/integration/ironforge_container.py", "description": "Get the global IRONFORGE container instance", "parameters": []}, "ironforge_container.initialize_ironforge_lazy_loading": {"file": "ironforge/integration/ironforge_container.py", "description": "Initialize IRONFORGE lazy loading system", "parameters": []}}}, "validation": {"classes": {"validation_framework.ValidationLevel": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Validation thoroughness levels", "methods": []}, "validation_framework.TestResult": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Test result status", "methods": []}, "validation_framework.ValidationResult": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Result of a validation test", "methods": []}, "validation_framework.ValidationSuite": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Collection of validation results", "methods": ["add_result", "finalize", "get_summary"]}, "validation_framework.ValidationLayer": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Base class for validation layer implementations.\nProvides framework for mathematical accuracy and performance validation.", "methods": ["validate_mathematical_invariants", "performance_benchmark", "statistical_validation"]}, "validation_framework.MathematicalPropertyTest": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Property-based testing for mathematical models.\nTests mathematical properties using generated data.", "methods": ["run_property_tests"]}, "validation_framework.NumericalStabilityTest": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Tests for numerical stability of mathematical implementations.\nChecks behavior under extreme conditions and edge cases.", "methods": ["run_stability_tests"]}, "validation_framework.PerformanceBenchmarkTest": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Performance benchmarking for mathematical implementations.\nTests execution time, memory usage, and scalability.", "methods": ["run_performance_tests"]}, "validation_framework.MathematicalValidationFramework": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Comprehensive validation framework that combines all testing approaches.\nMain entry point for mathematical model validation.", "methods": ["validate_mathematical_invariants", "performance_benchmark", "statistical_validation", "comprehensive_validation", "generate_validation_report"]}, "validation_framework.MockHawkesModel": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": null, "methods": ["compute_core_function"]}, "validation_framework.st": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": null, "methods": ["floats", "integers", "lists"]}, "cards.MotifStep": {"file": "ironforge/motifs/cards.py", "description": "One step in a motif sequence.", "methods": []}, "cards.MotifCard": {"file": "ironforge/motifs/cards.py", "description": null, "methods": []}, "performance_monitor.PerformanceMonitor": {"file": "ironforge/validation/performance_monitor.py", "description": "Performance monitoring and benchmarking system for IRONFORGE.\n\nMonitors:\n- Execution time performance\n- Memory usage patterns\n- GPU utilization (if available)\n- Component initialization times\n- Session processing throughput\n- Pattern discovery performance", "methods": ["monitor_execution", "benchmark_system_performance", "get_performance_history", "clear_performance_history", "get_monitor_summary"]}, "splits.PurgedKFold": {"file": "ironforge/validation/splits.py", "description": "Time-ordered, leakage-safe K-fold splits with embargo period.\n\nParameters\n----------\nn_splits : int\n    Number of folds for cross-validation.\nembargo_mins : int\n    Embargo period in minutes to prevent look-ahead leakage.\n\nNotes\n-----\nThe embargo creates a buffer between train and test periods to ensure\nthat training data cannot leak into test evaluation through temporal\ndependencies.", "methods": ["split"]}, "statistical_validator.StatisticalValidator": {"file": "ironforge/validation/statistical_validator.py", "description": "Statistical validation framework for IRONFORGE pattern quality assessment.\n\nValidates patterns against multiple statistical criteria:\n- Distribution normality and outlier detection\n- Temporal consistency and stationarity\n- Archaeological significance testing\n- Pattern authenticity metrics\n- 87% baseline compliance validation", "methods": ["validate_pattern_quality", "get_validation_summary"]}}, "functions": {"validation_framework.create_validation_framework": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": "Create mathematical validation framework with specified level", "parameters": [{"name": "validation_level", "annotation": "ValidationLevel", "default": "ValidationLevel.STANDARD", "kind": "positional"}]}, "validation_framework.given": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": null, "parameters": [{"name": "args", "annotation": null, "kind": "var_positional"}, {"name": "kwargs", "annotation": null, "kind": "var_keyword"}]}, "validation_framework.decorator": {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "description": null, "parameters": [{"name": "func", "annotation": null, "default": null, "kind": "positional"}]}, "cards.default_cards": {"file": "ironforge/motifs/cards.py", "description": "Three thin, testable cards derived from research notes.", "parameters": []}, "controls.time_shuffle_edges": {"file": "ironforge/validation/controls.py", "description": "Shuffle edge times across edges to break temporal signal.\n\nThis negative control destroys temporal relationships while preserving\nthe graph structure and edge feature distributions.\n\nParameters\n----------\nedge_index : np.ndarray\n    Edge connectivity matrix of shape (2, num_edges).\nedge_times : np.ndarray\n    Temporal timestamps for each edge.\nseed : int\n    Random seed for reproducible shuffling.\n\nReturns\n-------\nTuple[np.ndarray, np.ndarray]\n    (edge_index, shuffled_edge_times)\n\nExamples\n--------\n>>> edge_index = np.array([[0, 1, 2], [1, 2, 0]])\n>>> edge_times = np.array([100, 200, 300])\n>>> new_edge_index, shuffled_times = time_shuffle_edges(edge_index, edge_times, seed=42)\n>>> # Times are shuffled but edge connectivity preserved", "parameters": [{"name": "edge_index", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "edge_times", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "seed", "annotation": "int", "default": "7", "kind": "positional"}]}, "controls.label_permutation": {"file": "ironforge/validation/controls.py", "description": "Permute labels as a negative control baseline.\n\nThis control destroys the relationship between features and labels\nwhile preserving label distribution.\n\nParameters\n----------\nlabels : np.n<PERSON>ray\n    Original labels to permute.\nseed : int\n    Random seed for reproducible permutation.\n\nReturns\n-------\nnp.ndarray\n    Permuted labels with same distribution but random assignment.\n\nExamples\n--------\n>>> labels = np.array([0, 0, 1, 1, 1])\n>>> permuted = label_permutation(labels, seed=42)\n>>> # Same number of 0s and 1s, but randomly assigned", "parameters": [{"name": "labels", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "seed", "annotation": "int", "default": "7", "kind": "positional"}]}, "controls.node_feature_shuffle": {"file": "ironforge/validation/controls.py", "description": "Shuffle node features within or across feature groups.\n\nParameters\n----------\nnode_features : np.ndarray\n    Node feature matrix of shape (num_nodes, num_features).\nfeature_groups : Dict[str, Sequence[int]], optional\n    Groups of feature indices to shuffle independently.\n    If None, shuffle all features together.\nseed : int\n    Random seed for reproducible shuffling.\n\nReturns\n-------\nnp.ndarray\n    Feature matrix with shuffled values.", "parameters": [{"name": "node_features", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "feature_groups", "annotation": "dict[str, Sequence[int]] | None", "default": "None", "kind": "positional"}, {"name": "seed", "annotation": "int", "default": "7", "kind": "positional"}]}, "controls.edge_direction_shuffle": {"file": "ironforge/validation/controls.py", "description": "Randomly flip edge directions to test directional sensitivity.\n\nParameters\n----------\nedge_index : np.ndarray\n    Edge connectivity matrix of shape (2, num_edges).\nseed : int\n    Random seed for reproducible shuffling.\n\nReturns\n-------\nnp.ndarray\n    Edge index with randomly flipped directions.", "parameters": [{"name": "edge_index", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "seed", "annotation": "int", "default": "7", "kind": "positional"}]}, "controls.temporal_block_shuffle": {"file": "ironforge/validation/controls.py", "description": "Shuffle temporal blocks to preserve local structure but break global patterns.\n\nParameters\n----------\nedge_times : np.ndarray\n    Temporal timestamps for edges.\nblock_size_mins : int\n    Size of temporal blocks in minutes.\nseed : int\n    Random seed for reproducible shuffling.\n\nReturns\n-------\nnp.ndarray\n    Edge times with shuffled temporal blocks.", "parameters": [{"name": "edge_times", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "block_size_mins", "annotation": "int", "default": "60", "kind": "positional"}, {"name": "seed", "annotation": "int", "default": "7", "kind": "positional"}]}, "controls.create_control_variants": {"file": "ironforge/validation/controls.py", "description": "Create multiple negative control variants for comprehensive testing.\n\nParameters\n----------\nedge_index : np.ndarray\n    Edge connectivity matrix.\nedge_times : np.ndarray\n    Edge timestamps.\nnode_features : np.ndarray\n    Node feature matrix.\nlabels : np.ndarray\n    Target labels.\ncontrols : Sequence[str]\n    List of control types to generate.\nseed : int\n    Base random seed.\n\nReturns\n-------\nDict[str, Dict[str, Any]]\n    Control variants with modified data components.", "parameters": [{"name": "edge_index", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "edge_times", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "node_features", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "labels", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "controls", "annotation": "Sequence[str]", "default": null, "kind": "positional"}, {"name": "seed", "annotation": "int", "default": "7", "kind": "positional"}]}, "splits.oos_split": {"file": "ironforge/validation/splits.py", "description": "Simple out-of-sample split based on timestamp cutoff.\n\nParameters\n----------\ntimestamps : Sequence[int]\n    Epoch minutes (or any sortable temporal values).\ncutoff_ts : int\n    Cutoff timestamp. Train = ts < cutoff, Test = ts >= cutoff.\n\nReturns\n-------\nTuple[np.ndarray, np.ndarray]\n    (train_indices, test_indices)\n\nExamples\n--------\n>>> timestamps = [100, 200, 300, 400, 500]\n>>> train_idx, test_idx = oos_split(timestamps, cutoff_ts=300)\n>>> print(f\"Train: {train_idx}, Test: {test_idx}\")\nTrain: [0 1], Test: [2 3 4]", "parameters": [{"name": "timestamps", "annotation": "Sequence[int]", "default": null, "kind": "positional"}, {"name": "cutoff_ts", "annotation": "int", "default": null, "kind": "positional"}]}, "splits.temporal_train_test_split": {"file": "ironforge/validation/splits.py", "description": "Train-test split with temporal ordering and embargo period.\n\nParameters\n----------\ntimestamps : Sequence[int]\n    Epoch minutes (or any sortable temporal values).\ntest_size : float\n    Proportion of data to use for testing (0.0 to 1.0).\nembargo_mins : int\n    Embargo period in minutes between train and test.\n\nReturns\n-------\nTuple[np.ndarray, np.ndarray]\n    (train_indices, test_indices)", "parameters": [{"name": "timestamps", "annotation": "Sequence[int]", "default": null, "kind": "positional"}, {"name": "test_size", "annotation": "float", "default": "0.2", "kind": "positional"}, {"name": "embargo_mins", "annotation": "int", "default": "30", "kind": "positional"}]}, "run_weekly_daily_sweep_cascade_step_3b.main": {"file": "run_weekly_daily_sweep_cascade_step_3b.py", "description": "Execute Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B)", "parameters": []}}}, "reporting": {"classes": {"confluence.ConfluenceStripSpec": {"file": "ironforge/reporting/confluence.py", "description": null, "methods": []}, "heatmap.TimelineHeatmapSpec": {"file": "ironforge/reporting/heatmap.py", "description": null, "methods": []}}, "functions": {"confluence.build_confluence_strip": {"file": "ironforge/reporting/confluence.py", "description": "Render a 0–100 confluence strip with optional event markers.", "parameters": [{"name": "minute_bins", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "scores_0_100", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "marker_minutes", "annotation": "np.n<PERSON><PERSON> | None", "default": "None", "kind": "positional"}, {"name": "spec", "annotation": "ConfluenceStripSpec", "default": "ConfluenceStripSpec()", "kind": "positional"}]}, "heatmap.build_session_heatmap": {"file": "ironforge/reporting/heatmap.py", "description": "Render a single-session timeline heatmap.\nminute_bins: shape (T,), minute offsets from session open.\ndensities:   shape (T,), nonnegative event density per minute.\nReturns: PIL.Image (RGBA).", "parameters": [{"name": "minute_bins", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "densities", "annotation": "np.n<PERSON><PERSON>", "default": null, "kind": "positional"}, {"name": "spec", "annotation": "TimelineHeatmapSpec", "default": "TimelineHeatmapSpec()", "kind": "positional"}]}, "html.build_report_html": {"file": "ironforge/reporting/html.py", "description": "images: List of (caption, PIL.Image)", "parameters": [{"name": "title", "annotation": "str", "default": null, "kind": "positional"}, {"name": "images", "annotation": "list[tuple[str, object]]", "default": null, "kind": "positional"}]}, "writer.write_png": {"file": "ironforge/reporting/writer.py", "description": null, "parameters": [{"name": "path", "annotation": "Path", "default": null, "kind": "positional"}, {"name": "img", "annotation": null, "default": null, "kind": "positional"}]}, "writer.write_html": {"file": "ironforge/reporting/writer.py", "description": null, "parameters": [{"name": "path", "annotation": "Path", "default": null, "kind": "positional"}, {"name": "html", "annotation": "str", "default": null, "kind": "positional"}]}}}, "utilities": {"classes": {"batch_migrate_graphs.BatchGraphMigrator": {"file": "data_migration/batch_migrate_graphs.py", "description": "Batch migration system for IRONFORGE graph files\n\nTechnical Debt Surgeon: Comprehensive batch processing with\ndetailed reporting and strict data integrity validation", "methods": ["discover_graph_files", "create_backup", "analyze_batch_requirements", "migrate_single_file", "migrate_batch", "print_batch_report"]}, "schema_normalizer.SchemaNormalizer": {"file": "data_migration/schema_normalizer.py", "description": "Technical Debt Surgeon implementation for schema normalization\nMigrates 34D legacy data to 37D temporal cycle schema", "methods": ["migrate_legacy_to_37d", "migrate_session_file", "batch_migrate_directory"]}, "performance_monitor.PerformanceMonitor": {"file": "scripts/utilities/performance_monitor.py", "description": "Monitor IRONFORGE performance with Sprint 2 enhancements\nTracks 37D + 4 edge types vs baseline performance", "methods": ["start_monitoring", "end_monitoring", "check_performance_regression", "analyze_sprint2_impact", "generate_performance_report", "save_baseline_metrics", "hook_into_orchestrator"]}, "phase2_validation_framework.FeatureAuthenticityValidator": {"file": "scripts/analysis/phase2_validation_framework.py", "description": "Validates feature authenticity and provides comprehensive contamination analysis\nbefore and after Phase 2 enhancement.", "methods": ["analyze_feature_distributions", "load_session_data", "compare_before_after_enhancement", "generate_decontamination_report"]}, "phase4d_profile_run.PerformanceProfiler": {"file": "scripts/analysis/phase4d_profile_run.py", "description": "Performance profiling harness for IRONFORGE workloads.", "methods": ["setup_profiling", "create_workload_sets", "profile_session", "run_workload", "check_acceptance_targets", "run_performance_validation"]}, "enhanced_session_relativity_processor.EnhancedSessionRelativityProcessor": {"file": "scripts/data_processing/enhanced_session_relativity_processor.py", "description": "Processes enhanced sessions to add price relativity features\nfor permanent structural pattern discovery", "methods": ["calculate_session_statistics", "add_relativity_to_price_movements", "process_session", "process_all_enhanced_sessions"]}, "enhanced_sessions_price_relativity_processor.EnhancedSessionsRelativityProcessor": {"file": "scripts/data_processing/enhanced_sessions_price_relativity_processor.py", "description": "Processes enhanced sessions to add price relativity features", "methods": ["calculate_session_statistics", "add_relativity_to_price_movements", "add_relativity_to_fpfvg", "add_relativity_to_liquidity_events", "process_enhanced_session", "process_all_enhanced_sessions"]}, "price_field_standardizer.PriceFieldStandardizer": {"file": "scripts/data_processing/price_field_standardizer.py", "description": "Standardizes price movement field formats for TGAT validation.", "methods": ["standardize_session_file", "validate_standardization", "run_standardization"]}, "price_relativity_generator.PriceRelativityGenerator": {"file": "scripts/data_processing/price_relativity_generator.py", "description": "Generates normalized and relational features for permanent pattern discovery", "methods": ["calculate_session_statistics", "add_relativity_to_price_movements", "add_relativity_to_pythonnodes", "process_session", "process_all_sessions"]}, "session_quality_assessor.SessionQualityAssessor": {"file": "scripts/data_processing/session_quality_assessor.py", "description": null, "methods": ["assess_session_quality", "analyze_all_sessions"]}, "daily_discovery_workflows.MarketAnalysis": {"file": "scripts/utilities/daily_discovery_workflows.py", "description": "Daily market analysis result", "methods": []}, "daily_discovery_workflows.SessionDiscoveryResult": {"file": "scripts/utilities/daily_discovery_workflows.py", "description": "Real-time session discovery result", "methods": []}, "daily_discovery_workflows.DailyDiscoveryWorkflows": {"file": "scripts/utilities/daily_discovery_workflows.py", "description": "Production workflows for daily pattern discovery and market analysis\n\nProvides systematic, actionable workflows for:\n- Pre-market preparation\n- Real-time session analysis\n- Cross-session pattern tracking\n- Performance monitoring", "methods": ["morning_market_analysis", "hunt_session_patterns", "track_pattern_performance"]}, "htf_builder.HTFBuilder": {"file": "scripts/utilities/htf_builder.py", "description": null, "methods": ["aggregate_session", "process_all_sessions"]}, "ironforge_discovery_sdk.PatternAnalysis": {"file": "scripts/utilities/ironforge_discovery_sdk.py", "description": "Structured pattern analysis result", "methods": []}, "ironforge_discovery_sdk.CrossSessionLink": {"file": "scripts/utilities/ironforge_discovery_sdk.py", "description": "Cross-session pattern relationship", "methods": []}, "ironforge_discovery_sdk.IRONFORGEDiscoverySDK": {"file": "scripts/utilities/ironforge_discovery_sdk.py", "description": "Production SDK for IRONFORGE archaeological pattern discovery\n\nProvides systematic workflows for discovering real cross-session patterns\nand temporal links using the validated TGAT architecture.", "methods": ["discover_session_patterns", "discover_all_sessions", "find_cross_session_links", "get_discovery_summary", "generate_discovery_report"]}, "pattern_correlation_visualizer.PatternCorrelationVisualizer": {"file": "scripts/utilities/pattern_correlation_visualizer.py", "description": "Advanced pattern correlation visualization system", "methods": ["generate_correlation_visualizations"]}, "pattern_intelligence.PatternTrend": {"file": "scripts/utilities/pattern_intelligence.py", "description": "Temporal trend in pattern occurrence", "methods": []}, "pattern_intelligence.MarketRegime": {"file": "scripts/utilities/pattern_intelligence.py", "description": "Identified market regime based on pattern clustering", "methods": []}, "pattern_intelligence.PatternAlert": {"file": "scripts/utilities/pattern_intelligence.py", "description": "Real-time pattern alert", "methods": []}, "pattern_intelligence.PatternIntelligenceEngine": {"file": "scripts/utilities/pattern_intelligence.py", "description": "Advanced pattern intelligence system for actionable trading insights\n\nProvides sophisticated analysis beyond basic pattern discovery:\n- Market regime identification\n- Pattern trend analysis\n- Real-time pattern matching\n- Performance tracking", "methods": ["analyze_pattern_trends", "identify_market_regimes", "find_pattern_matches", "generate_pattern_alerts", "get_intelligence_summary", "generate_intelligence_report"]}, "pattern_monitor.PatternMonitor": {"file": "scripts/utilities/pattern_monitor.py", "description": "Real-time pattern monitoring system", "methods": ["monitor_patterns"]}, "performance_monitor.PerformanceMetrics": {"file": "scripts/utilities/performance_monitor.py", "description": "Container for performance measurement results", "methods": []}}, "functions": {"batch_migrate_graphs.main": {"file": "data_migration/batch_migrate_graphs.py", "description": "Main execution with command line argument parsing", "parameters": []}, "schema_normalizer.main": {"file": "data_migration/schema_normalizer.py", "description": "Command-line interface for schema migration", "parameters": []}, "prepare_motifs_input.build_motifs_input": {"file": "ironforge/scripts/prepare_motifs_input.py", "description": null, "parameters": [{"name": "discovery_json", "annotation": "Path", "default": null, "kind": "positional"}, {"name": "validation_json", "annotation": "Path | None", "default": "None", "kind": "positional"}]}, "prepare_motifs_input.main": {"file": "ironforge/scripts/prepare_motifs_input.py", "description": null, "parameters": [{"name": "argv", "annotation": "list[str] | None", "default": "None", "kind": "positional"}]}, "complete_phase2_enhancement.generate_authentic_htf_carryover": {"file": "scripts/analysis/complete_phase2_enhancement.py", "description": "Generate authentic HTF carryover strength based on market conditions.", "parameters": [{"name": "price_volatility", "annotation": "float", "default": null, "kind": "positional"}, {"name": "session_type", "annotation": "str", "default": null, "kind": "positional"}]}, "complete_phase2_enhancement.generate_authentic_energy_density": {"file": "scripts/analysis/complete_phase2_enhancement.py", "description": "Calculate authentic energy density based on actual market activity.", "parameters": [{"name": "price_movements", "annotation": "List[Dict]", "default": null, "kind": "positional"}, {"name": "session_duration", "annotation": "int", "default": null, "kind": "positional"}]}, "complete_phase2_enhancement.generate_liquidity_events": {"file": "scripts/analysis/complete_phase2_enhancement.py", "description": "Generate realistic liquidity events based on session characteristics.", "parameters": [{"name": "price_movements", "annotation": "List[Dict]", "default": null, "kind": "positional"}, {"name": "session_type", "annotation": "str", "default": null, "kind": "positional"}]}, "complete_phase2_enhancement.enhance_session_features": {"file": "scripts/analysis/complete_phase2_enhancement.py", "description": "Enhance a single session with authentic feature calculations.", "parameters": [{"name": "session_data", "annotation": "Dict[str, Any]", "default": null, "kind": "positional"}]}, "complete_phase2_enhancement.process_remaining_sessions": {"file": "scripts/analysis/complete_phase2_enhancement.py", "description": "Process all remaining TGAT-ready sessions for complete Phase 2 coverage.", "parameters": []}, "investigate_causal_event_chains.extract_event_sequences": {"file": "scripts/analysis/investigate_causal_event_chains.py", "description": "Extract chronological event sequences from each session", "parameters": []}, "investigate_causal_event_chains.build_transition_matrices": {"file": "scripts/analysis/investigate_causal_event_chains.py", "description": "Build event transition matrices within each session", "parameters": [{"name": "session_sequences", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_causal_event_chains.analyze_lag_profiles": {"file": "scripts/analysis/investigate_causal_event_chains.py", "description": "Calculate lag histograms and consistency metrics for each transition pair", "parameters": [{"name": "transition_lags", "annotation": null, "default": null, "kind": "positional"}, {"name": "min_occurrences", "annotation": null, "default": "3", "kind": "positional"}]}, "investigate_causal_event_chains.identify_causal_chains": {"file": "scripts/analysis/investigate_causal_event_chains.py", "description": "Identify causal chains with lag consistency >80%", "parameters": [{"name": "lag_profiles", "annotation": null, "default": null, "kind": "positional"}, {"name": "consistency_threshold", "annotation": null, "default": "80", "kind": "positional"}]}, "investigate_causal_event_chains.test_specific_hypothesis": {"file": "scripts/analysis/investigate_causal_event_chains.py", "description": "Test specific hypothesis: expansion_phase → consolidation → liq_sweep", "parameters": [{"name": "session_sequences", "annotation": null, "default": null, "kind": "positional"}, {"name": "lag_profiles", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_causal_event_chains.create_causal_chain_visualization": {"file": "scripts/analysis/investigate_causal_event_chains.py", "description": "Create visualization of causal chain discoveries", "parameters": [{"name": "lag_profiles", "annotation": null, "default": null, "kind": "positional"}, {"name": "high_consistency_chains", "annotation": null, "default": null, "kind": "positional"}, {"name": "complete_sequences", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_causal_event_chains.main": {"file": "scripts/analysis/investigate_causal_event_chains.py", "description": "Main causal chain investigation", "parameters": []}, "investigate_liquidity_sweep_catalyst.extract_liquidity_sweep_sequences": {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "description": "Extract all sequences that START with liq_sweep events", "parameters": []}, "investigate_liquidity_sweep_catalyst.analyze_immediate_responses": {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "description": "Analyze what happens immediately after liq_sweep events", "parameters": [{"name": "liq_sweep_sequences", "annotation": null, "default": null, "kind": "positional"}, {"name": "time_windows", "annotation": null, "default": "[1, 3, 5, 10, 15, 30]", "kind": "positional"}]}, "investigate_liquidity_sweep_catalyst.discover_catalyst_chains": {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "description": "Discover the most common event chains triggered by liq_sweep", "parameters": [{"name": "liq_sweep_sequences", "annotation": null, "default": null, "kind": "positional"}, {"name": "max_chain_length", "annotation": null, "default": "5", "kind": "positional"}]}, "investigate_liquidity_sweep_catalyst.analyze_catalyst_timing_patterns": {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "description": "Analyze when liq_sweeps occur and their effectiveness as catalysts", "parameters": [{"name": "liq_sweep_sequences", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_liquidity_sweep_catalyst.test_specific_catalyst_hypotheses": {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "description": "Test specific hypotheses about liq_sweep catalyst behavior", "parameters": [{"name": "liq_sweep_sequences", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_liquidity_sweep_catalyst.create_catalyst_visualization": {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "description": "Create comprehensive visualization of liq_sweep catalyst behavior", "parameters": [{"name": "liq_sweep_sequences", "annotation": null, "default": null, "kind": "positional"}, {"name": "window_responses", "annotation": null, "default": null, "kind": "positional"}, {"name": "chain_patterns", "annotation": null, "default": null, "kind": "positional"}, {"name": "hypotheses_results", "annotation": null, "default": null, "kind": "positional"}]}, "investigate_liquidity_sweep_catalyst.main": {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "description": "Main liquidity sweep catalyst investigation", "parameters": []}, "phase2_validation_framework.main": {"file": "scripts/analysis/phase2_validation_framework.py", "description": "Main validation execution.", "parameters": []}, "phase2_validation_summary.validate_decontamination": {"file": "scripts/analysis/phase2_validation_summary.py", "description": "Show before/after decontamination results.", "parameters": []}, "phase4d_profile_run.run_phase4d_performance_validation": {"file": "scripts/analysis/phase4d_profile_run.py", "description": "Run Phase 4d performance validation.", "parameters": []}, "phase4d_profile_run.save_profile_csv": {"file": "scripts/analysis/phase4d_profile_run.py", "description": "Save profiling results to CSV.", "parameters": [{"name": "results", "annotation": "Dict", "default": null, "kind": "positional"}]}, "run_direct_discovery.run_direct_discovery": {"file": "scripts/analysis/run_direct_discovery.py", "description": null, "parameters": []}, "run_manual_discovery.run_full_discovery": {"file": "scripts/analysis/run_manual_discovery.py", "description": null, "parameters": []}, "enhanced_session_relativity_processor.main": {"file": "scripts/data_processing/enhanced_session_relativity_processor.py", "description": "Main execution: Transform all enhanced sessions to use price relativity", "parameters": []}, "enhanced_sessions_price_relativity_processor.main": {"file": "scripts/data_processing/enhanced_sessions_price_relativity_processor.py", "description": "Main execution: Transform all enhanced sessions to use price relativity", "parameters": []}, "price_field_standardizer.main": {"file": "scripts/data_processing/price_field_standardizer.py", "description": "Run price field standardization.", "parameters": []}, "price_relativity_generator.main": {"file": "scripts/data_processing/price_relativity_generator.py", "description": "Main execution: Transform all HTF sessions to use price relativity\n\nTechnical Debt Surgeon: Enhanced with comprehensive error handling", "parameters": []}, "session_quality_assessor.main": {"file": "scripts/data_processing/session_quality_assessor.py", "description": "Run complete quality assessment on all Level 1 sessions", "parameters": []}, "unicode_fix.sanitize_session_data": {"file": "scripts/data_processing/unicode_fix.py", "description": "Recursively sanitize all strings in data structure", "parameters": [{"name": "data", "annotation": null, "default": null, "kind": "positional"}]}, "unicode_fix.load_clean_sessions": {"file": "scripts/data_processing/unicode_fix.py", "description": "Load all sessions with sanitization", "parameters": []}, "benchmark_performance.measure_import_time": {"file": "scripts/utilities/benchmark_performance.py", "description": "Measure time to import a module and optionally get a class", "parameters": [{"name": "module_name", "annotation": null, "default": null, "kind": "positional"}, {"name": "class_name", "annotation": null, "default": "None", "kind": "positional"}]}, "benchmark_performance.benchmark_core_imports": {"file": "scripts/utilities/benchmark_performance.py", "description": "Benchmark core IRONFORGE imports", "parameters": []}, "benchmark_performance.benchmark_container_loading": {"file": "scripts/utilities/benchmark_performance.py", "description": "Benchmark lazy loading container performance", "parameters": []}, "benchmark_performance.main": {"file": "scripts/utilities/benchmark_performance.py", "description": "Run complete performance benchmark", "parameters": []}, "daily_discovery_workflows.morning_prep": {"file": "scripts/utilities/daily_discovery_workflows.py", "description": "Quick morning market preparation", "parameters": [{"name": "days_back", "annotation": "int", "default": "7", "kind": "positional"}]}, "daily_discovery_workflows.hunt_patterns": {"file": "scripts/utilities/daily_discovery_workflows.py", "description": "Quick pattern hunting for specific session", "parameters": [{"name": "session", "annotation": "str", "default": "'NY_PM'", "kind": "positional"}]}, "daily_discovery_workflows.full_market_intel": {"file": "scripts/utilities/daily_discovery_workflows.py", "description": "Complete daily market intelligence workflow", "parameters": []}, "debug_features.debug_feature_dimensions": {"file": "scripts/utilities/debug_features.py", "description": null, "parameters": []}, "debug_graph_structure.debug_graph_structure": {"file": "scripts/utilities/debug_graph_structure.py", "description": "Debug the actual graph structure.", "parameters": []}, "debug_tgat_init.debug_tgat_initialization": {"file": "scripts/utilities/debug_tgat_init.py", "description": null, "parameters": []}, "example_htf_output.demonstrate_htf_integration": {"file": "scripts/utilities/example_htf_output.py", "description": "Create example HTF output showing all features", "parameters": []}, "graph_builder_diagnostic.diagnose_graph_building": {"file": "scripts/utilities/graph_builder_diagnostic.py", "description": "Diagnose what's happening in graph building process.", "parameters": []}, "ironforge_discovery_sdk.quick_discover_all_sessions": {"file": "scripts/utilities/ironforge_discovery_sdk.py", "description": "Quick function to run discovery on all sessions", "parameters": []}, "ironforge_discovery_sdk.analyze_session_patterns": {"file": "scripts/utilities/ironforge_discovery_sdk.py", "description": "Quick function to analyze patterns in a specific session", "parameters": [{"name": "session_name", "annotation": "str", "default": null, "kind": "positional"}]}, "pattern_intelligence.analyze_market_intelligence": {"file": "scripts/utilities/pattern_intelligence.py", "description": "Complete market intelligence analysis workflow", "parameters": []}, "pattern_intelligence.find_similar_patterns": {"file": "scripts/utilities/pattern_intelligence.py", "description": "Find patterns similar to a specific pattern from a session", "parameters": [{"name": "session_name", "annotation": "str", "default": null, "kind": "positional"}, {"name": "pattern_index", "annotation": "int", "default": "0", "kind": "positional"}]}, "pattern_monitor.main": {"file": "scripts/utilities/pattern_monitor.py", "description": null, "parameters": []}, "performance_monitor.monitor_ironforge_session": {"file": "scripts/utilities/performance_monitor.py", "description": "Convenience function to monitor a complete IRONFORGE session\n\nArgs:\n    session_data: Input session data\n    orchestrator_func: Function to call for processing (e.g., orchestrator.process_session)\n    baseline_file: Optional baseline metrics file\n\nReturns:\n    Combined processing results and performance report", "parameters": [{"name": "session_data", "annotation": "Dict", "default": null, "kind": "positional"}, {"name": "orchestrator_func", "annotation": null, "default": null, "kind": "positional"}, {"name": "baseline_file", "annotation": "str", "default": "None", "kind": "positional"}]}, "performance_monitor.main": {"file": "scripts/utilities/performance_monitor.py", "description": "Command-line interface for performance monitoring", "parameters": []}, "performance_monitor.create_graph_analysis": {"file": "scripts/utilities/performance_monitor.py", "description": "Create graph analysis for performance monitoring", "parameters": [{"name": "graph_builder", "annotation": null, "default": null, "kind": "positional"}, {"name": "processed_graphs", "annotation": null, "default": null, "kind": "positional"}]}, "performance_monitor.synthetic_processing": {"file": "scripts/utilities/performance_monitor.py", "description": "Synthetic processing function for testing", "parameters": [{"name": "data", "annotation": null, "default": null, "kind": "positional"}]}}}, "data": {"classes": {}, "functions": {"parquet_writer.write_nodes": {"file": "ironforge/data_engine/parquet_writer.py", "description": "Write nodes DataFrame to Parquet with validation.", "parameters": [{"name": "df", "annotation": "pd.DataFrame", "default": null, "kind": "positional"}, {"name": "path", "annotation": "str", "default": null, "kind": "positional"}]}, "parquet_writer.write_edges": {"file": "ironforge/data_engine/parquet_writer.py", "description": "Write edges DataFrame to Parquet with validation.", "parameters": [{"name": "df", "annotation": "pd.DataFrame", "default": null, "kind": "positional"}, {"name": "path", "annotation": "str", "default": null, "kind": "positional"}]}}}}, "architecture_insights": {"design_patterns": [{"pattern": "Factory", "usage_count": 37}, {"pattern": "Builder", "usage_count": 5}, {"pattern": "Container/DI", "usage_count": 2}], "architectural_smells": [], "strengths": [{"strength": "well_organized_architecture", "description": "Clear separation into 8 distinct engines"}], "suggested_improvements": []}, "ai_assistant_guidance": {"primary_entry_points": [{"file": "data_migration/batch_migrate_graphs.py", "function": "main", "description": "Main execution with command line argument parsing"}, {"file": "data_migration/schema_normalizer.py", "function": "main", "description": "Command-line interface for schema migration"}, {"file": "ironforge/scripts/prepare_motifs_input.py", "function": "main", "description": null}, {"file": "ironforge/sdk/cli.py", "function": "main", "description": "Entry point for the `ironforge` CLI."}, {"file": "run_fpfvg_network_analysis.py", "function": "main", "description": "Execute FPFVG Network Analysis (Step 3A)"}, {"file": "run_fpfvg_network_analysis_simple.py", "function": "main", "description": "Execute simplified FPFVG network analysis"}, {"file": "run_fpfvg_redelivery_lattice.py", "function": "main", "description": "Execute FPFVG Redelivery Network Lattice analysis"}, {"file": "run_global_lattice.py", "function": "main", "description": "Execute global lattice build"}, {"file": "run_specialized_lattice.py", "function": "main", "description": "Execute specialized lattice building for archaeological deep dive"}, {"file": "run_terrain_analysis.py", "function": "main", "description": "Execute terrain analysis for hot zones and cascades"}, {"file": "run_weekly_daily_cascade_lattice.py", "function": "main", "description": "Execute Weekly→Daily Liquidity Sweep Cascade Lattice analysis"}, {"file": "run_weekly_daily_sweep_cascade_step_3b.py", "function": "main", "description": "Execute Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B)"}, {"file": "run_weekly_daily_sweep_cascade_step_3b_refined.py", "function": "main", "description": "Execute REFINED Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B)"}, {"file": "run_working_cascade_analysis.py", "function": "main", "description": "Execute working cascade analysis"}, {"file": "scripts/analysis/analyze_concrete_patterns.py", "function": "main", "description": "Main concrete analysis"}, {"file": "scripts/analysis/bridge_node_mapper.py", "function": "main", "description": "Run bridge node analysis"}, {"file": "scripts/analysis/comprehensive_discovery_report.py", "function": "main", "description": "Main analysis function"}, {"file": "scripts/analysis/decode_subpattern_findings.py", "function": "main", "description": "Main analysis function"}, {"file": "scripts/analysis/enrichment_analyzer.py", "function": "main", "description": "Run enrichment analysis"}, {"file": "scripts/analysis/explore_discoveries.py", "function": "main", "description": "Main exploration function"}, {"file": "scripts/analysis/investigate_causal_event_chains.py", "function": "main", "description": "Main causal chain investigation"}, {"file": "scripts/analysis/investigate_cross_session_synchronization.py", "function": "main", "description": "Main cross-session synchronization investigation"}, {"file": "scripts/analysis/investigate_htf_structural_inheritance.py", "function": "main", "description": "Main HTF structural inheritance investigation"}, {"file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "function": "main", "description": "Main liquidity sweep catalyst investigation"}, {"file": "scripts/analysis/investigate_pattern_subarchitecture.py", "function": "main", "description": "Main sub-pattern discovery analysis"}, {"file": "scripts/analysis/phase2_feature_pipeline_enhancement.py", "function": "main", "description": "Main execution for Phase 2 Feature Pipeline Enhancement."}, {"file": "scripts/analysis/phase2_validation_framework.py", "function": "main", "description": "Main validation execution."}, {"file": "scripts/analysis/phase5_archaeological_discovery_validation.py", "function": "main", "description": "Execute Phase 5 Archaeological Discovery Validation"}, {"file": "scripts/analysis/phase5_direct_tgat_validation.py", "function": "main", "description": "Execute Phase 5 Direct TGAT Validation"}, {"file": "scripts/analysis/phase5_enhanced_session_validation.py", "function": "main", "description": "Run enhanced session validation"}, {"file": "scripts/analysis/quick_pattern_discovery.py", "function": "main", "description": null}, {"file": "scripts/analysis/run_archaeology_demonstration.py", "function": "main", "description": "Main demonstration workflow"}, {"file": "scripts/analysis/run_contaminated_session_enhancement.py", "function": "main", "description": "Run enhancement on contaminated sessions."}, {"file": "scripts/analysis/run_full_archaeology_discovery.py", "function": "main", "description": "Main production archaeological discovery workflow"}, {"file": "scripts/analysis/run_htf_orchestrator.py", "function": "main", "description": null}, {"file": "scripts/data_processing/enhanced_session_relativity_processor.py", "function": "main", "description": "Main execution: Transform all enhanced sessions to use price relativity"}, {"file": "scripts/data_processing/enhanced_sessions_price_relativity_processor.py", "function": "main", "description": "Main execution: Transform all enhanced sessions to use price relativity"}, {"file": "scripts/data_processing/price_field_standardizer.py", "function": "main", "description": "Run price field standardization."}, {"file": "scripts/data_processing/price_relativity_generator.py", "function": "main", "description": "Main execution: Transform all HTF sessions to use price relativity\n\nTechnical Debt Surgeon: Enhanced with comprehensive error handling"}, {"file": "scripts/data_processing/session_quality_assessor.py", "function": "main", "description": "Run complete quality assessment on all Level 1 sessions"}, {"file": "scripts/utilities/benchmark_performance.py", "function": "main", "description": "Run complete performance benchmark"}, {"file": "scripts/utilities/lattice_population_runner.py", "function": "main", "description": "Run the complete lattice population"}, {"file": "scripts/utilities/pattern_monitor.py", "function": "main", "description": null}, {"file": "scripts/utilities/performance_monitor.py", "function": "main", "description": "Command-line interface for performance monitoring"}], "core_abstractions": [{"file": "iron_core/mathematical/mathematical_hooks.py", "class": "MathematicalHook", "description": "Base class for mathematical model hooks"}, {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "class": "APIInterfaceLayer", "description": "Base class for API interface implementations.\nProvides framework for exposing mathematical models via APIs."}, {"file": "iron_core/mathematical/mathematical_layers/api_interface.py", "class": "BaseModel", "description": null}, {"file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "class": "CoreAlgorithmLayer", "description": "Base class for high-performance algorithm implementations.\nFocuses on computational efficiency and numerical stability."}, {"file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "class": "Integration<PERSON>ayer", "description": "Base class for mathematical model integration with business systems.\nProvides framework for model registration, execution, and monitoring."}, {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "class": "MathematicalModel", "description": "Protocol/Interface for mathematical models.\nDefines the contract that all mathematical models must satisfy."}, {"file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "class": "TheoryAbstractionLayer", "description": "Base class for theoretical mathematical model abstractions.\nFocuses purely on mathematical theory without implementation concerns."}, {"file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "class": "ValidationLayer", "description": "Base class for validation layer implementations.\nProvides framework for mathematical accuracy and performance validation."}, {"file": "iron_core/mathematical/scaling_patterns.py", "class": "ScalingPattern", "description": "Abstract base class for scaling patterns"}], "data_flow_patterns": [], "testing_patterns": [], "configuration_patterns": [{"file": "config.py", "description": "IRONFORGE Configuration Management\n=================================\n\nCentralized configuration system to eliminate hardcoded paths and make\nIRONFORGE deployable across different environments.\n\nSupports:\n- Environment variables\n- Configuration files\n- Default fallbacks\n- Path validation", "classes": ["IRONFORGEConfig"]}]}}