[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ironforge"
dynamic = ["version"]
description = "Archaeological discovery system for market pattern analysis"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "IRON Ecosystem", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.10"
dependencies = [
    "numpy>=1.20.0",
    "pandas>=1.3.0", 
    "torch>=1.9.0",
    "matplotlib>=3.3.0",
    "seaborn>=0.11.0",
    "scikit-learn>=1.0.0",
    "networkx>=2.5",
    "tqdm>=4.60.0",
    "PyYAML>=6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=21.0",
    "ruff>=0.6.8", 
    "mypy>=0.800",
    "bandit>=1.7.9",
    "coverage>=7.3.2",
    "pytest-benchmark>=4.0.0",
]

[project.scripts]
ironforge = "ironforge.sdk.cli:main"
ifg = "ironforge.sdk.cli:main"

[project.urls]
Homepage = "https://github.com/iron-ecosystem/ironforge"
Repository = "https://github.com/iron-ecosystem/ironforge"

[tool.setuptools.packages.find]
include = ["ironforge*"]

[tool.setuptools.dynamic]
version = {attr = "ironforge.__version__.__version__"}

[tool.python]
version = ">=3.10"

[tool.black]
line-length = 100
target-version = ["py310"]

[tool.ruff]
line-length = 100
target-version = "py310"
extend-exclude = ["tests/_golden/**"]

[tool.ruff.lint]
select = ["E","F","I","UP","B","SIM","C4","ARG"]
ignore = ["E501"]  # handled by black

[tool.mypy]
python_version = "3.10"
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_return_any = true
disallow_untyped_defs = true
no_implicit_optional = true
namespace_packages = true
files = ["ironforge"]
exclude = "(^tests/_golden/|^scripts/|^tests/integration/|^ironforge/analysis/|^ironforge/learning/|^ironforge/synthesis/|^ironforge/validation/|^ironforge/utilities/|^config.py)"

[tool.bandit]
skips = ["B101", "B110"] # assert allowed in tests, ignore existing try/except/pass patterns
exclude_dirs = ["tests/integration", "ironforge/analysis", "ironforge/learning", "ironforge/synthesis", "ironforge/validation", "ironforge/utilities"]

[tool.pytest.ini_options]
testpaths = ["tests"]
markers = [
    "performance: performance regression tests",
]
