#!/usr/bin/env python3
"""
IRONFORGE Remote Agent Setup Example
===================================

Complete example demonstrating how to set up and use IRONFORGE remote agents.
Shows configuration, deployment, and usage patterns.
"""

import asyncio
import logging
import yaml
from pathlib import Path

# IRONFORGE remote agent imports
from ironforge.services.agent_gateway import AgentGateway
from ironforge.services.agent_orchestrator import AgentOrchestrator
from ironforge.services.remote_agent import RemoteAgent
from ironforge.services.service_discovery import ServiceDiscovery
from ironforge.services.auth import setup_default_auth
from ironforge.services.monitoring import create_remote_agent_monitor
from ironforge.services.failover import create_failover_manager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def setup_infrastructure():
    """Set up the complete remote agent infrastructure"""
    logger.info("Setting up IRONFORGE remote agent infrastructure...")
    
    # 1. Setup Authentication
    logger.info("Setting up authentication...")
    auth_manager = setup_default_auth()
    
    # 2. Setup Service Discovery
    logger.info("Setting up service discovery...")
    service_discovery = ServiceDiscovery("configs/services.json")
    await service_discovery.start()
    
    # Register core services
    service_discovery.register_service(
        service_id="ironforge-gateway",
        service_type="gateway",
        host="localhost",
        port=8001,
        protocol="ws",
        path="/ws/agent",
        health_check_url="http://localhost:8001/api/health"
    )
    
    # 3. Setup Monitoring
    logger.info("Setting up monitoring...")
    monitor = create_remote_agent_monitor({
        "retention_hours": 24
    })
    
    # 4. Setup Agent Gateway
    logger.info("Setting up agent gateway...")
    gateway = AgentGateway(host="localhost", port=8001)
    
    # 5. Setup Agent Orchestrator
    logger.info("Setting up agent orchestrator...")
    orchestrator = AgentOrchestrator()
    await orchestrator.start()
    
    # 6. Setup Failover Manager
    logger.info("Setting up failover manager...")
    failover_manager = create_failover_manager([
        "http://localhost:8001",
        "http://localhost:8002"
    ])
    
    return {
        "auth_manager": auth_manager,
        "service_discovery": service_discovery,
        "monitor": monitor,
        "gateway": gateway,
        "orchestrator": orchestrator,
        "failover_manager": failover_manager
    }


async def deploy_remote_agents(gateway_url: str, num_agents: int = 3):
    """Deploy multiple remote agents"""
    logger.info(f"Deploying {num_agents} remote agents...")
    
    agents = []
    
    for i in range(num_agents):
        agent_id = f"agent-{i+1}"
        capabilities = ["discovery", "confluence", "validation", "analysis"]
        
        # Create and start agent
        agent = RemoteAgent(
            gateway_url=gateway_url,
            agent_id=agent_id,
            capabilities=capabilities
        )
        
        # Start agent in background
        asyncio.create_task(agent.start())
        agents.append(agent)
        
        logger.info(f"Agent {agent_id} deployed")
    
    return agents


async def submit_sample_tasks(orchestrator):
    """Submit sample tasks to demonstrate functionality"""
    logger.info("Submitting sample tasks...")
    
    # Sample discovery task
    discovery_task_id = orchestrator.submit_task(
        task_type="discovery",
        payload={
            "shard_paths": ["data/shards/sample1.parquet", "data/shards/sample2.parquet"],
            "config": {
                "fanouts": [10, 10],
                "batch_size": 2048
            }
        },
        priority=1
    )
    
    # Sample confluence task
    confluence_task_id = orchestrator.submit_task(
        task_type="confluence",
        payload={
            "session_count": 50,
            "weights": {
                "cluster": 0.25,
                "htf_prox": 0.25,
                "structure": 0.20,
                "cycle": 0.15,
                "precursor": 0.15
            }
        },
        priority=2
    )
    
    # Sample validation task
    validation_task_id = orchestrator.submit_task(
        task_type="validation",
        payload={
            "validation_type": "temporal",
            "folds": 5
        },
        priority=3
    )
    
    logger.info(f"Submitted tasks: {discovery_task_id}, {confluence_task_id}, {validation_task_id}")
    
    return [discovery_task_id, confluence_task_id, validation_task_id]


async def monitor_task_progress(orchestrator, task_ids):
    """Monitor task progress"""
    logger.info("Monitoring task progress...")
    
    completed_tasks = set()
    
    while len(completed_tasks) < len(task_ids):
        for task_id in task_ids:
            if task_id in completed_tasks:
                continue
            
            status = orchestrator.get_task_status(task_id)
            if status:
                logger.info(f"Task {task_id}: {status['status']}")
                
                if status['status'] in ['completed', 'failed', 'cancelled']:
                    completed_tasks.add(task_id)
        
        await asyncio.sleep(5)  # Check every 5 seconds
    
    logger.info("All tasks completed")


async def demonstrate_failover(failover_manager):
    """Demonstrate failover functionality"""
    logger.info("Demonstrating failover functionality...")
    
    async def sample_operation(service_url):
        """Sample operation that might fail"""
        logger.info(f"Executing operation on {service_url}")
        # Simulate operation
        await asyncio.sleep(1)
        return {"result": "success", "service": service_url}
    
    try:
        result = await failover_manager.execute_with_failover(sample_operation)
        logger.info(f"Failover operation result: {result}")
    except Exception as e:
        logger.error(f"Failover operation failed: {e}")


async def show_monitoring_dashboard(monitor):
    """Display monitoring dashboard"""
    logger.info("Monitoring Dashboard:")
    logger.info("=" * 50)
    
    dashboard = monitor.get_monitoring_dashboard()
    
    # System health
    health = dashboard["system_health"]
    logger.info(f"Overall Health: {health['overall_status']}")
    
    # Metrics summary
    metrics = dashboard["metrics"]
    logger.info(f"Active Counters: {len(metrics['counters'])}")
    logger.info(f"Active Gauges: {len(metrics['gauges'])}")
    
    # Agent summary
    agent_summary = dashboard["agent_summary"]
    logger.info(f"Total Agents: {agent_summary['total_agents']}")
    logger.info(f"Active Agents: {agent_summary['active_agents']}")


async def cleanup_infrastructure(infrastructure):
    """Clean up infrastructure components"""
    logger.info("Cleaning up infrastructure...")
    
    # Stop orchestrator
    await infrastructure["orchestrator"].stop()
    
    # Stop service discovery
    await infrastructure["service_discovery"].stop()
    
    # Stop monitoring
    await infrastructure["monitor"].stop()
    
    logger.info("Infrastructure cleanup completed")


async def main():
    """Main demonstration function"""
    logger.info("Starting IRONFORGE Remote Agent Demonstration")
    logger.info("=" * 60)
    
    try:
        # 1. Setup infrastructure
        infrastructure = await setup_infrastructure()
        
        # 2. Start gateway in background
        gateway_task = asyncio.create_task(
            infrastructure["gateway"].start()
        )
        
        # Give gateway time to start
        await asyncio.sleep(2)
        
        # 3. Deploy remote agents
        agents = await deploy_remote_agents("ws://localhost:8001", num_agents=3)
        
        # Give agents time to connect
        await asyncio.sleep(5)
        
        # 4. Submit sample tasks
        task_ids = await submit_sample_tasks(infrastructure["orchestrator"])
        
        # 5. Monitor task progress
        await monitor_task_progress(infrastructure["orchestrator"], task_ids)
        
        # 6. Demonstrate failover
        await demonstrate_failover(infrastructure["failover_manager"])
        
        # 7. Show monitoring dashboard
        await show_monitoring_dashboard(infrastructure["monitor"])
        
        # 8. Show orchestrator stats
        stats = infrastructure["orchestrator"].get_orchestrator_stats()
        logger.info(f"Orchestrator Stats: {stats}")
        
        # 9. Show service discovery stats
        discovery_stats = infrastructure["service_discovery"].get_discovery_stats()
        logger.info(f"Service Discovery Stats: {discovery_stats}")
        
        logger.info("Demonstration completed successfully!")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        raise
    
    finally:
        # Cleanup
        if 'infrastructure' in locals():
            await cleanup_infrastructure(infrastructure)


def create_sample_config():
    """Create a sample configuration file"""
    config = {
        "gateway": {
            "host": "localhost",
            "port": 8001,
            "max_connections": 100
        },
        "orchestrator": {
            "max_concurrent_tasks_per_agent": 1,
            "task_timeout": 300
        },
        "authentication": {
            "jwt": {
                "algorithm": "HS256",
                "expiration_hours": 24
            }
        },
        "monitoring": {
            "metrics": {
                "enabled": True,
                "collection_interval": 30
            }
        }
    }
    
    # Save to file
    config_path = Path("configs/remote_agents_example.yml")
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    logger.info(f"Sample configuration saved to {config_path}")


if __name__ == "__main__":
    # Create sample configuration
    create_sample_config()
    
    # Run demonstration
    asyncio.run(main())
