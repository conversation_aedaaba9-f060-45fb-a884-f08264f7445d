# IRONFORGE v0.9.0

Release date: 2025-08-19

## Highlights
- HTF Context Features v1.1: 45D → 51D node features via higher-timeframe context (M15/H1) with leakage-safe, closed-bar rules.
- Macro–Micro Analytics Suite: cross-session analytics that quantify HTF → trade-horizon influence and session prototypes.
- Creative Adjacent Possibilities Report: structured exploration across 12 innovation domains.
- Context Saver Kit: documentation and artifacts generation to preserve discovery context.
- Enhanced Discovery Analysis: improved archaeological tools and reporting pipeline.
- Lint and Code Quality: 1,500+ lint issues resolved (PR #28) without functional changes.

## Changes
- Converters
  - Added enhanced JSON → Parquet shard converter (`ironforge.converters.json_to_parquet`).
  - Generates `nodes.parquet`, `edges.parquet`, `node_map.parquet`, and `meta.json` per shard.
  - Deterministic `NodeIDManager` with persisted id counter; manifest writer for shard sets.
  - 45D base node features + optional 6D HTF context (f45–f50) and 20D edge features.
- HTF Context
  - New `HTFContextProcessor` to compute:
    - `f45_sv_m15_z`, `f46_sv_h1_z` (synthetic volume z-scores)
    - `f47_barpos_m15`, `f48_barpos_h1` (bar position within timeframe)
    - `f49_dist_daily_mid` (normalized distance to prior-day midpoint)
    - `f50_htf_regime` (0/1/2 consolidation/transition/expansion)
  - Leakage prevention: only last closed HTF bar contributes features.
- CLI
  - `ironforge prep-shards --source-glob ... --symbol NQ --tf M5 --tz ET --pack [single|pack] --dry-run --overwrite --htf-context`
  - Minimal reporting flow with `report-minimal` uses generated parquet inputs.
- Reporting
  - `minidash` outputs default charts when parquet inputs are absent to keep flows demo-friendly.
- Quality & Style
  - Lint fixes across the codebase (PR #28), import ordering, typing cleanups.

## Upgrade Notes
- The converter may create and update `data/shards/_id_counter.json`; do not commit generated data to VCS.
- Prefer rel_ enhanced sessions over base duplicates (`enhanced_rel_*` are deduped automatically).
- If you maintain external consumers of node features, account for the optional 6 new HTF fields (51D total when enabled).

## Compatibility
- Built atop the latest `main` after lint-only PR #28; replaces the conflicted PR #24.
- No breaking API changes expected; CLI adds optional flags.

## Thanks
- PRs: #28 (lint), #29 (features). #24 closed as superseded.

