# Archived Documentation

This folder contains legacy and historical documents kept for reference.

- Wave 3/4/5 docs and refactoring plans are preserved as read-only context.
- Assistant and process notes (e.g., CLAUDE*.md) are not part of the product docs.
- Prefer the Wave 7 SDK/CLI flow documented in README and docs/GETTING_STARTED.md.

Canonical entry points:
- Install: `pip install -e .[dev]`
- Run: `python -m ironforge.sdk.cli report-minimal --config configs/dev.yml`
- Full pipeline: `discover-temporal`, `score-session`, `validate-run`, `report-minimal`

These archived files may be inconsistent with the current codebase and are not maintained.

