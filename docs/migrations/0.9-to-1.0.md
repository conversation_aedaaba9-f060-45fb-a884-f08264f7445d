# IRONFORGE 0.9 → 1.0 Migration Guide

## Summary
- No data contract breaking changes: taxonomy v1 (6 events), 4 intents, and 51D/20D invariant remain.
- HTF context default is OFF in 1.0 (45D nodes). Opt-in yields 51D (v1.1 features). Planned default ON in 1.1 with `--no-htf-context` escape.
- Canonical entrypoints finalized; legacy imports emit DeprecationWarning in 1.0 and will be removed in 2.0.
- Version is single-sourced from `ironforge.__version__`.

## What Changed
1) HTF Context Default
- Before: some docs/features implied ON by default.
- Now: OFF by default in 1.0. Enable explicitly to add 6 features (45D → 51D).
  - CLI: `python -m ironforge.sdk.cli prep-shards --htf-context`
  - Config: set `htf_context.enabled: true` in your conversion settings.

2) Canonical Public API
- Discovery: `ironforge.learning.discovery_pipeline:run_discovery`
- Confluence: `ironforge.confluence.scoring:score_confluence`
- Validation: `ironforge.validation.runner:validate_run`
- Reporting: `ironforge.reporting.minidash:build_minidash`
- Helpers: `ironforge.sdk.config`, `ironforge.sdk.app_config`, `ironforge.sdk.io`

3) Deprecations (Soft)
- Legacy imports still work but warn in 1.0; removal in 2.0:
  - `ironforge.learning.tgat_discovery:run_discovery` → use `learning.discovery_pipeline:run_discovery`
  - `ironforge.discovery.runner:run_discovery` → use `learning.discovery_pipeline:run_discovery`
  - `ironforge.confluence.scorer:score_session` → use `confluence.scoring:score_confluence`
  - `ironforge.metrics.confluence:score_session` → use `confluence.scoring:score_confluence`

4) Version Single-Source
- All modules import version from `ironforge/__version__.py`.
- Access as: `import ironforge; print(ironforge.__version__)`.

## Actions You May Need
- If you relied on 51D features implicitly, add `--htf-context` (CLI) or enable in your conversion config.
- Update imports to canonical paths to avoid deprecation warnings.
- If you read version from submodules, switch to `ironforge.__version__`.

## Backward Compatibility
- Data and run layout unchanged.
- Optional adapters remain opt-in and backward compatible.
- Legacy entrypoints work in 1.0 with warnings; will be removed in 2.0.

## Verification Checklist
- Node feature count is 45 (default) or 51 (HTF enabled).
- CLI runs: discover → score → validate → report complete successfully.
- No DeprecationWarnings after import path updates.

---
For questions or report issues, open a discussion or file an issue with your config snippet.

