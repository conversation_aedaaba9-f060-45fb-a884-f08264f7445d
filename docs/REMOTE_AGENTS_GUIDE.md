# IRONFORGE Remote Agents Guide

**Comprehensive guide for setting up and managing IRONFORGE remote agent functionality**

---

## 🎯 Overview

IRONFORGE Remote Agents provide distributed processing capabilities for archaeological discovery tasks. The system enables horizontal scaling, fault tolerance, and efficient resource utilization across multiple nodes.

### Key Features

- **Distributed Processing**: Scale IRONFORGE discovery across multiple agents
- **Fault Tolerance**: Automatic failover and recovery mechanisms
- **Real-time Communication**: WebSocket-based agent communication
- **Authentication & Security**: JWT tokens and API key authentication
- **Monitoring & Observability**: Comprehensive metrics and health monitoring
- **Flexible Deployment**: Support for standalone, Docker, and Kubernetes deployments

---

## 🏗️ Architecture

### Core Components

1. **Agent Gateway**: Central communication hub for remote agents
2. **Agent Orchestrator**: Manages agent lifecycle and task distribution
3. **Remote Agents**: Worker processes that execute IRONFORGE tasks
4. **Service Discovery**: Manages service registration and health monitoring
5. **Authentication Manager**: Handles security and access control
6. **Monitoring System**: Collects metrics and monitors system health

### Communication Flow

```
Client → Agent Gateway → Agent Orchestrator → Remote Agents
                    ↓
              Service Discovery ← Authentication Manager
                    ↓
              Monitoring System
```

---

## 🚀 Quick Start

### 1. Installation

Ensure you have the required dependencies:

```bash
pip install fastapi uvicorn websockets pyjwt cryptography psutil aiohttp
```

### 2. Basic Configuration

Create a configuration file `configs/remote_agents.yml`:

```yaml
gateway:
  host: "localhost"
  port: 8001
  max_connections: 100

orchestrator:
  max_concurrent_tasks_per_agent: 1
  task_timeout: 300

authentication:
  jwt:
    algorithm: "HS256"
    expiration_hours: 24

monitoring:
  metrics:
    enabled: true
    collection_interval: 30
```

### 3. Start the Infrastructure

```python
import asyncio
from ironforge.services.agent_gateway import AgentGateway
from ironforge.services.agent_orchestrator import AgentOrchestrator

async def start_infrastructure():
    # Start orchestrator
    orchestrator = AgentOrchestrator()
    await orchestrator.start()
    
    # Start gateway
    gateway = AgentGateway()
    await gateway.start()

asyncio.run(start_infrastructure())
```

### 4. Deploy Remote Agents

```python
from ironforge.services.remote_agent import RemoteAgent

async def deploy_agent():
    agent = RemoteAgent(
        gateway_url="ws://localhost:8001",
        agent_id="agent-1",
        capabilities=["discovery", "confluence", "validation"]
    )
    await agent.start()

asyncio.run(deploy_agent())
```

---

## 📋 Configuration Reference

### Gateway Configuration

```yaml
gateway:
  host: "0.0.0.0"              # Bind address
  port: 8001                   # Port number
  max_connections: 1000        # Maximum concurrent connections
  heartbeat_timeout: 60        # Agent heartbeat timeout (seconds)
  message_queue_size: 1000     # Message queue size per agent
```

### Orchestrator Configuration

```yaml
orchestrator:
  max_concurrent_tasks_per_agent: 1    # Tasks per agent
  task_timeout: 300                    # Task timeout (seconds)
  heartbeat_timeout: 60                # Agent heartbeat timeout
  cleanup_interval: 3600               # Cleanup interval (seconds)
  
  task_queue:
    max_size: 10000                    # Maximum queue size
    priority_levels: 5                 # Number of priority levels
    retry_policy:
      max_retries: 3                   # Maximum retry attempts
      retry_delay: 30                  # Retry delay (seconds)
      exponential_backoff: true        # Use exponential backoff
```

### Authentication Configuration

```yaml
authentication:
  jwt:
    algorithm: "HS256"                 # JWT algorithm (HS256 or RS256)
    expiration_hours: 24               # Token expiration time
    secret_key: null                   # Secret key (auto-generated if null)
  
  api_keys:
    default_expiration_days: 365       # Default API key expiration
    max_keys_per_user: 10              # Maximum keys per user
  
  roles:
    admin:
      permissions: ["read", "write", "execute", "admin"]
    agent:
      permissions: ["read", "write", "execute"]
    worker:
      permissions: ["read", "execute"]
    viewer:
      permissions: ["read"]
```

---

## 🔧 Deployment Options

### Standalone Deployment

Deploy as systemd services:

```bash
python scripts/deploy_remote_agents.py --type standalone
```

This creates systemd services for each component:
- `ironforge-gateway.service`
- `ironforge-orchestrator.service`
- `ironforge-service-discovery.service`

### Docker Deployment

Deploy using Docker containers:

```bash
python scripts/deploy_remote_agents.py --type docker
```

This builds Docker images and runs containers for each component.

### Kubernetes Deployment

Deploy to Kubernetes cluster:

```bash
python scripts/deploy_remote_agents.py --type kubernetes --namespace ironforge
```

This creates Kubernetes deployments, services, and ConfigMaps.

---

## 📊 Monitoring and Observability

### Health Checks

The system provides comprehensive health monitoring:

```python
from ironforge.services.monitoring import create_remote_agent_monitor

# Create monitor
monitor = create_remote_agent_monitor({
    "retention_hours": 24
})

# Get health status
health = monitor.get_monitoring_dashboard()
print(f"System Health: {health['system_health']['overall_status']}")
```

### Metrics Collection

Available metrics include:

- **Agent Metrics**: Connection status, task completion rates, error rates
- **Task Metrics**: Execution times, success/failure rates, queue depths
- **System Metrics**: CPU usage, memory usage, network statistics

### Monitoring Dashboard

Access the monitoring dashboard at:
- Gateway: `http://localhost:8001/api/health`
- Orchestrator stats via API calls
- Service discovery stats via API calls

---

## 🛡️ Security Best Practices

### Authentication

1. **Use RSA Keys for Production**:
   ```yaml
   authentication:
     jwt:
       algorithm: "RS256"
       private_key_path: "certs/jwt_private.pem"
       public_key_path: "certs/jwt_public.pem"
   ```

2. **Rotate API Keys Regularly**:
   ```python
   # Create API key with expiration
   key_id, api_key = auth_manager.create_api_key(
       name="agent-key",
       roles=["agent"],
       expires_in_days=30
   )
   ```

### Network Security

1. **Use TLS for Production**:
   ```yaml
   gateway:
     tls:
       enabled: true
       cert_file: "certs/gateway.crt"
       key_file: "certs/gateway.key"
   ```

2. **Configure Firewalls**:
   - Allow only necessary ports (8001, 8002, 8003)
   - Restrict access to management interfaces

### Access Control

1. **Implement Role-Based Access**:
   ```python
   # Create user with specific roles
   user_id = auth_manager.create_user(
       username="agent_user",
       email="<EMAIL>",
       roles=["agent"]
   )
   ```

2. **Use Least Privilege Principle**:
   - Assign minimal required permissions
   - Regular access reviews

---

## 🔄 Error Handling and Failover

### Circuit Breaker Pattern

```python
from ironforge.services.failover import CircuitBreaker, CircuitBreakerConfig

# Configure circuit breaker
config = CircuitBreakerConfig(
    failure_threshold=5,
    recovery_timeout=60.0,
    success_threshold=3
)

circuit_breaker = CircuitBreaker(config)

# Use circuit breaker
result = await circuit_breaker.call(risky_operation)
```

### Retry Policies

```python
from ironforge.services.failover import RetryPolicy, RetryHandler

# Configure retry policy
policy = RetryPolicy(
    max_attempts=3,
    base_delay=1.0,
    strategy=RetryStrategy.EXPONENTIAL
)

retry_handler = RetryHandler(policy)
result = await retry_handler.execute_with_retry(operation)
```

### Failover Management

```python
from ironforge.services.failover import create_failover_manager

# Create failover manager
failover_manager = create_failover_manager([
    "http://primary-service:8001",
    "http://backup-service:8001"
])

# Execute with automatic failover
result = await failover_manager.execute_with_failover(operation)
```

---

## 📈 Scaling Strategies

### Horizontal Scaling

1. **Add More Agents**:
   ```bash
   # Deploy additional agents
   for i in {1..5}; do
       python -m ironforge.services.remote_agent ws://gateway:8001 agent-$i &
   done
   ```

2. **Load Balancing**:
   ```yaml
   scaling:
     load_balancing:
       strategy: "round_robin"
       health_check_required: true
   ```

### Auto-scaling

```yaml
scaling:
  auto_scaling:
    enabled: true
    min_agents: 2
    max_agents: 20
    triggers:
      cpu_threshold: 80
      memory_threshold: 80
      queue_depth_threshold: 100
```

---

## 🧪 Testing

### Unit Tests

Run the test suite:

```bash
python -m pytest tests/services/
```

### Integration Tests

Test the complete system:

```bash
python examples/remote_agent_setup.py
```

### Load Testing

Test system under load:

```bash
python scripts/load_test_agents.py --agents 10 --tasks 100
```

---

## 🔍 Troubleshooting

### Common Issues

1. **Agent Connection Failures**:
   - Check gateway is running: `curl http://localhost:8001/api/health`
   - Verify network connectivity
   - Check authentication credentials

2. **Task Execution Failures**:
   - Review agent logs
   - Check task payload format
   - Verify agent capabilities

3. **Performance Issues**:
   - Monitor system resources
   - Check task queue depth
   - Review circuit breaker states

### Debugging

Enable debug logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

Check component status:

```bash
python scripts/deploy_remote_agents.py --status
```

---

## 📚 API Reference

### Agent Gateway API

- `POST /api/agents/register` - Register new agent
- `GET /api/agents` - List all agents
- `POST /api/agents/{id}/tasks` - Assign task to agent
- `GET /api/health` - Health check

### WebSocket Endpoints

- `ws://gateway:8001/ws/agent/{agent_id}` - Agent connection

### Authentication

- Bearer tokens: `Authorization: Bearer <jwt_token>`
- API keys: `Authorization: ApiKey <api_key>`

---

This guide provides comprehensive coverage of IRONFORGE remote agent functionality. For additional examples and advanced configurations, see the `examples/` directory.
