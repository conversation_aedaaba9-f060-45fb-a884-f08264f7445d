# IRONFORGE Notes & Tasks

## TODO
- [ ] Add TODOs to
- [ ] Add tasks to this file
- [ ] Add notes to this file

### Notes
1. Use context7 properly, to find documentation to clean up IRONFORGE.
2. All documentation should be in the docs/ directory or it should be a README.md in sub directories areas
3. All documentation should be in markdown format.
4. CLAUDE.md files will need updating.
5. Consider building a proper Opencode to experiment with building.


