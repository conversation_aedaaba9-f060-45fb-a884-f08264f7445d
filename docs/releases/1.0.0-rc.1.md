# IRONFORGE 1.0.0-rc.1 (Release Candidate)

This RC validates the 1.0 public surface and pipeline invariants ahead of GA.

## Highlights

- Public API stabilized:
  - CLI: `discover-temporal`, `score-session`, `validate-run`, `report-minimal`, `status`, `prep-shards` (with `--htf-context`).
  - Python: `ironforge.learning.discovery_pipeline.run_discovery`, `ironforge.confluence.scoring.score_confluence`, `ironforge.validation.runner.validate_run`, `ironforge.reporting.minidash.build_minidash`.
- HTF context features (v1.1) supported via data prep toggle; OFF by default in 1.0 (45D → 51D when enabled).
- Invariants enforced: 6 events, 4 intents, nodes 45D/51D, edges 20D, HTF last-closed, strict session isolation.
- CI/tooling: pre-commit, Ruff, Black, mypy, Bandit; release-please configured.

## Behavior & Defaults

- HTF context is OFF by default in 1.0.
  - Enable with: `ironforge prep-shards --htf-context`.
  - 1.1 will flip default ON; use `--no-htf-context` to disable in 1.1.
- Legacy imports continue to work in 1.0 with deprecation warnings; removal planned for 2.0.

## Breaking Changes

- None expected. The RC does not remove legacy imports and preserves v0.9 behavior where applicable.

## Migration Notes

- No code changes required for base 45D operation.
- To evaluate HTF (51D):
  - Convert enhanced JSON sessions to Parquet shards with HTF context enabled:
    - `ironforge prep-shards --htf-context`
  - A/B compare runs between 45D and 51D datasets.

## Quickstart

```bash
# Discovery → Confluence → Report
python -m ironforge.sdk.cli discover-temporal --config configs/dev.yml
python -m ironforge.sdk.cli score-session     --config configs/dev.yml
python -m ironforge.sdk.cli report-minimal    --config configs/dev.yml

# Status
python -m ironforge.sdk.cli status --runs runs

# Data prep (HTF OFF by default → 45D)
python -m ironforge.sdk.cli prep-shards

# Enable HTF (adds 6 features → 51D)
python -m ironforge.sdk.cli prep-shards --htf-context
```

## Known Decisions

- Session isolation maintained; no cross-session edges.
- HTF features computed on last closed bar only (no leakage).

## Verification Checklist (RC)

- Unit and integration tests green on tag.
- `make precommit` clean (Black, Ruff, mypy, Bandit).
- Smoke CLI runs complete on sample data.
- Minidash assets render (`minidash.html`, `minidash.png`).

## Acknowledgements

- M1–M3 delivered; this RC focuses on M4 hardening ahead of M5 GA.

## Run Artifacts (NQ 5m RC Smoke)

- 45D report: `runs/<DATE>/report_45d/`
- 51D report: `runs/<DATE>/report_51d/`
- Manifests:
  - `runs/<DATE>/NQ_5m/manifest.json`
  - `runs/<DATE>/NQ_5m_htf/manifest.json`

Replace `<DATE>` with the smoke date (e.g., `$(date +%F)`). If you enable automatic manifest writing during reporting, run with:

`IRONFORGE_WRITE_MANIFEST=1 python -m ironforge.sdk.cli report-minimal --config configs/dev.yml`
