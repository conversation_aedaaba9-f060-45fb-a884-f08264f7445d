# IRONFORGE 1.0.0 — GA (2025-08-19)

## Highlights
- Public API frozen: CLI (`discover-temporal`, `score-session`, `validate-run`, `report-minimal`, `status`, `prep-shards`), Python (`learning.discovery_pipeline.run_discovery`, `confluence.scoring.score_confluence`, `validation.runner.validate_run`, `reporting.minidash.build_minidash`).
- Contracts locked: nodes 45D (HTF OFF) / 51D (HTF ON), edges=20, taxonomy v1 (6 events), edge intents (4).
- Repro rails: CI (lint/type/tests/build/smoke), manifests per run (env-gated), RC smoke scripts.

## Invariants
- Events: 6 | Intents: 4 | Node features: 45D/51D (HTF ON adds 6 HTF cols) | Edge features: 20
- HTF sampling: last-closed only | Session isolation: enforced

## Deprecations
- Legacy CLI/import fallbacks: warn in 1.0, removal planned for 2.0.

## HTF Policy
- Default OFF in 1.0; planned default ON in 1.1 with `--no-htf-context` escape.

## Data Contracts
- Shards: `data/shards/<SYMBOL_TF>/shard_*/` (51D nodes / 20D edges)
- Runs: `runs/YYYY-MM-DD/<SYMBOL_TF>m[_htf]/` + `manifest.json` (when enabled)

## RC Smoke (NQ 5m) Artifacts
- `runs/<DATE>/NQ_5m/` (45D) — manifest ✅
- `runs/<DATE>/NQ_5m_htf/` (51D) — manifest ✅
- Reports: `runs/<DATE>/report_45d/`, `runs/<DATE>/report_51d/`

## Upgrade Guide
- See: `docs/migrations/0.9-to-1.0.md` (HTF default, canonical entrypoints, version single-source).

## Known Issues / Limits
- Cross-session edges (Wave 8) excluded from 1.0.
- Multi-model orchestration out of scope for 1.0.

## Verify Installation
```bash
python -m pip install ironforge-*.whl
python - <<'PY'
import ironforge, ironforge.reporting as r
print("ironforge", ironforge.__version__, "| reporting", r.__version__)
PY
```

## Commands to Cut/Publish
Preferred: merge release-please PR to create tag `v1.0.0`.

Manual (if needed):
```bash
python -m build && twine check dist/*
python -m pip install dist/*.whl && python - <<'PY'
import ironforge, ironforge.reporting as r; print(ironforge.__version__, r.__version__)
PY
```

## RC Smoke (for completeness)
```bash
IRONFORGE_WRITE_MANIFEST=1 bash scripts/smoke_rc_nq5m.sh
python scripts/validate_contracts.py runs/$(date +%F)/NQ_5m/     --expect-node-dims 45 --name NQ_5m_45d
python scripts/validate_contracts.py runs/$(date +%F)/NQ_5m_htf/ --expect-node-dims 51 --name NQ_5m_51d
```

## KPI Spot-Checks (tick-box)
- 3/12/24-bar outcomes present (hit_+X, time-to-X).
- Confluence distribution within documented thresholds (no drift vs RC).
- Runtime meets validation budgets.

