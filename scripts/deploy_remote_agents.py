#!/usr/bin/env python3
"""
IRONFORGE Remote Agent Deployment Script
========================================

Automated deployment and configuration management for IRONFORGE remote agents.
Supports Docker, Kubernetes, and standalone deployments.
"""

import argparse
import json
import logging
import os
import subprocess
import sys
import yaml
from pathlib import Path
from typing import Any, Dict, List, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DeploymentManager:
    """Manages deployment of IRONFORGE remote agents"""
    
    def __init__(self, config_file: str = "configs/remote_agents.yml"):
        self.config_file = Path(config_file)
        self.config = self._load_config()
        self.deployment_type = None
    
    def _load_config(self) -> Dict[str, Any]:
        """Load deployment configuration"""
        try:
            with open(self.config_file, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {self.config_file}")
            return config
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            sys.exit(1)
    
    def deploy_standalone(self, components: List[str] = None):
        """Deploy components as standalone services"""
        logger.info("Starting standalone deployment...")
        
        components = components or ["gateway", "orchestrator", "service_discovery"]
        
        for component in components:
            self._deploy_standalone_component(component)
        
        logger.info("Standalone deployment completed")
    
    def _deploy_standalone_component(self, component: str):
        """Deploy a single component as standalone service"""
        logger.info(f"Deploying {component} as standalone service...")
        
        # Create systemd service file
        service_content = self._generate_systemd_service(component)
        service_file = f"/etc/systemd/system/ironforge-{component}.service"
        
        try:
            # Write service file (requires sudo)
            subprocess.run([
                "sudo", "tee", service_file
            ], input=service_content, text=True, check=True)
            
            # Reload systemd and start service
            subprocess.run(["sudo", "systemctl", "daemon-reload"], check=True)
            subprocess.run(["sudo", "systemctl", "enable", f"ironforge-{component}"], check=True)
            subprocess.run(["sudo", "systemctl", "start", f"ironforge-{component}"], check=True)
            
            logger.info(f"{component} service deployed and started")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to deploy {component}: {e}")
    
    def _generate_systemd_service(self, component: str) -> str:
        """Generate systemd service file content"""
        python_path = sys.executable
        working_dir = os.getcwd()
        
        service_commands = {
            "gateway": f"{python_path} -m ironforge.services.agent_gateway",
            "orchestrator": f"{python_path} -m ironforge.services.agent_orchestrator",
            "service_discovery": f"{python_path} -m ironforge.services.service_discovery"
        }
        
        return f"""[Unit]
Description=IRONFORGE {component.title()} Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=ironforge
Group=ironforge
WorkingDirectory={working_dir}
Environment=PYTHONPATH={working_dir}
Environment=IRONFORGE_ENV=production
ExecStart={service_commands.get(component, f"{python_path} -m ironforge.services.{component}")}
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ironforge-{component}

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
"""
    
    def deploy_docker(self, components: List[str] = None):
        """Deploy components using Docker"""
        logger.info("Starting Docker deployment...")
        
        components = components or ["gateway", "orchestrator", "service_discovery"]
        
        # Build Docker images
        self._build_docker_images()
        
        # Deploy components
        for component in components:
            self._deploy_docker_component(component)
        
        logger.info("Docker deployment completed")
    
    def _build_docker_images(self):
        """Build Docker images for IRONFORGE components"""
        logger.info("Building Docker images...")
        
        # Create Dockerfile if it doesn't exist
        dockerfile_content = self._generate_dockerfile()
        dockerfile_path = Path("Dockerfile.remote-agents")
        
        with open(dockerfile_path, 'w') as f:
            f.write(dockerfile_content)
        
        # Build image
        try:
            subprocess.run([
                "docker", "build", 
                "-f", str(dockerfile_path),
                "-t", "ironforge/remote-agents:latest",
                "."
            ], check=True)
            
            logger.info("Docker image built successfully")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to build Docker image: {e}")
            raise
    
    def _generate_dockerfile(self) -> str:
        """Generate Dockerfile content"""
        return """FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for remote agents
RUN pip install --no-cache-dir \\
    fastapi \\
    uvicorn \\
    websockets \\
    pyjwt \\
    cryptography \\
    psutil \\
    aiohttp

# Copy application code
COPY . .

# Install IRONFORGE in development mode
RUN pip install -e .

# Create non-root user
RUN useradd -m -u 1000 ironforge
USER ironforge

# Expose ports
EXPOSE 8000 8001 8002

# Default command
CMD ["python", "-m", "ironforge.services.agent_gateway"]
"""
    
    def _deploy_docker_component(self, component: str):
        """Deploy a component using Docker"""
        logger.info(f"Deploying {component} with Docker...")
        
        port_mapping = {
            "gateway": "8001:8001",
            "orchestrator": "8002:8002",
            "service_discovery": "8003:8003"
        }
        
        container_name = f"ironforge-{component}"
        
        try:
            # Stop and remove existing container
            subprocess.run(["docker", "stop", container_name], 
                         capture_output=True, check=False)
            subprocess.run(["docker", "rm", container_name], 
                         capture_output=True, check=False)
            
            # Run new container
            cmd = [
                "docker", "run", "-d",
                "--name", container_name,
                "-p", port_mapping.get(component, "8000:8000"),
                "-v", f"{os.getcwd()}/configs:/app/configs",
                "-v", f"{os.getcwd()}/logs:/app/logs",
                "--restart", "unless-stopped",
                "ironforge/remote-agents:latest",
                "python", "-m", f"ironforge.services.{component}"
            ]
            
            subprocess.run(cmd, check=True)
            logger.info(f"{component} container deployed successfully")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to deploy {component} container: {e}")
    
    def deploy_kubernetes(self, namespace: str = "ironforge"):
        """Deploy components to Kubernetes"""
        logger.info("Starting Kubernetes deployment...")
        
        # Create namespace
        self._create_k8s_namespace(namespace)
        
        # Generate and apply Kubernetes manifests
        manifests = self._generate_k8s_manifests(namespace)
        
        for manifest_name, manifest_content in manifests.items():
            self._apply_k8s_manifest(manifest_name, manifest_content)
        
        logger.info("Kubernetes deployment completed")
    
    def _create_k8s_namespace(self, namespace: str):
        """Create Kubernetes namespace"""
        namespace_manifest = f"""
apiVersion: v1
kind: Namespace
metadata:
  name: {namespace}
"""
        
        try:
            subprocess.run([
                "kubectl", "apply", "-f", "-"
            ], input=namespace_manifest, text=True, check=True)
            
            logger.info(f"Namespace {namespace} created")
            
        except subprocess.CalledProcessError as e:
            logger.warning(f"Failed to create namespace (may already exist): {e}")
    
    def _generate_k8s_manifests(self, namespace: str) -> Dict[str, str]:
        """Generate Kubernetes deployment manifests"""
        manifests = {}
        
        components = ["gateway", "orchestrator", "service_discovery"]
        
        for component in components:
            manifests[f"{component}-deployment"] = self._generate_k8s_deployment(component, namespace)
            manifests[f"{component}-service"] = self._generate_k8s_service(component, namespace)
        
        # Add ConfigMap
        manifests["config"] = self._generate_k8s_configmap(namespace)
        
        return manifests
    
    def _generate_k8s_deployment(self, component: str, namespace: str) -> str:
        """Generate Kubernetes deployment manifest"""
        port_mapping = {
            "gateway": 8001,
            "orchestrator": 8002,
            "service_discovery": 8003
        }
        
        return f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ironforge-{component}
  namespace: {namespace}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ironforge-{component}
  template:
    metadata:
      labels:
        app: ironforge-{component}
    spec:
      containers:
      - name: {component}
        image: ironforge/remote-agents:latest
        command: ["python", "-m", "ironforge.services.{component}"]
        ports:
        - containerPort: {port_mapping.get(component, 8000)}
        env:
        - name: IRONFORGE_ENV
          value: "production"
        volumeMounts:
        - name: config
          mountPath: /app/configs
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: config
        configMap:
          name: ironforge-config
"""
    
    def _generate_k8s_service(self, component: str, namespace: str) -> str:
        """Generate Kubernetes service manifest"""
        port_mapping = {
            "gateway": 8001,
            "orchestrator": 8002,
            "service_discovery": 8003
        }
        
        return f"""
apiVersion: v1
kind: Service
metadata:
  name: ironforge-{component}
  namespace: {namespace}
spec:
  selector:
    app: ironforge-{component}
  ports:
  - port: {port_mapping.get(component, 8000)}
    targetPort: {port_mapping.get(component, 8000)}
  type: ClusterIP
"""
    
    def _generate_k8s_configmap(self, namespace: str) -> str:
        """Generate Kubernetes ConfigMap manifest"""
        config_yaml = yaml.dump(self.config, default_flow_style=False)
        
        return f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: ironforge-config
  namespace: {namespace}
data:
  remote_agents.yml: |
{config_yaml}
"""
    
    def _apply_k8s_manifest(self, name: str, manifest: str):
        """Apply Kubernetes manifest"""
        try:
            subprocess.run([
                "kubectl", "apply", "-f", "-"
            ], input=manifest, text=True, check=True)
            
            logger.info(f"Applied {name} manifest")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to apply {name} manifest: {e}")
    
    def status(self):
        """Check deployment status"""
        logger.info("Checking deployment status...")
        
        # Check systemd services
        self._check_systemd_status()
        
        # Check Docker containers
        self._check_docker_status()
        
        # Check Kubernetes deployments
        self._check_k8s_status()
    
    def _check_systemd_status(self):
        """Check systemd service status"""
        services = ["ironforge-gateway", "ironforge-orchestrator", "ironforge-service-discovery"]
        
        for service in services:
            try:
                result = subprocess.run([
                    "systemctl", "is-active", service
                ], capture_output=True, text=True)
                
                status = result.stdout.strip()
                logger.info(f"Systemd {service}: {status}")
                
            except subprocess.CalledProcessError:
                logger.info(f"Systemd {service}: not found")
    
    def _check_docker_status(self):
        """Check Docker container status"""
        containers = ["ironforge-gateway", "ironforge-orchestrator", "ironforge-service-discovery"]
        
        for container in containers:
            try:
                result = subprocess.run([
                    "docker", "ps", "--filter", f"name={container}", "--format", "table {{.Names}}\\t{{.Status}}"
                ], capture_output=True, text=True)
                
                if result.stdout.strip():
                    logger.info(f"Docker {container}: running")
                else:
                    logger.info(f"Docker {container}: not running")
                    
            except subprocess.CalledProcessError:
                logger.info(f"Docker {container}: error checking status")
    
    def _check_k8s_status(self):
        """Check Kubernetes deployment status"""
        try:
            result = subprocess.run([
                "kubectl", "get", "deployments", "-n", "ironforge"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Kubernetes deployments:")
                logger.info(result.stdout)
            else:
                logger.info("Kubernetes: namespace not found or no access")
                
        except subprocess.CalledProcessError:
            logger.info("Kubernetes: not available")


def main():
    """Main deployment script"""
    parser = argparse.ArgumentParser(description="Deploy IRONFORGE Remote Agents")
    parser.add_argument("--type", choices=["standalone", "docker", "kubernetes"], 
                       default="standalone", help="Deployment type")
    parser.add_argument("--components", nargs="+", 
                       choices=["gateway", "orchestrator", "service_discovery"],
                       help="Components to deploy")
    parser.add_argument("--config", default="configs/remote_agents.yml",
                       help="Configuration file path")
    parser.add_argument("--namespace", default="ironforge",
                       help="Kubernetes namespace")
    parser.add_argument("--status", action="store_true",
                       help="Check deployment status")
    
    args = parser.parse_args()
    
    # Create deployment manager
    deployment_manager = DeploymentManager(args.config)
    
    if args.status:
        deployment_manager.status()
        return
    
    # Deploy based on type
    if args.type == "standalone":
        deployment_manager.deploy_standalone(args.components)
    elif args.type == "docker":
        deployment_manager.deploy_docker(args.components)
    elif args.type == "kubernetes":
        deployment_manager.deploy_kubernetes(args.namespace)


if __name__ == "__main__":
    main()
