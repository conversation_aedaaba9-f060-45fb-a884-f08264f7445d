{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎯 IRONFORGE Predictive Condition Explorer\n", "\n", "## Interactive Discovery of High-Probability Market Patterns\n", "\n", "**Goal:** Find conditions with 70%+ probability and actionable lead times (2-15 minutes)\n", "\n", "**Key Discovery:** f8 liquidity intensity spikes → FPFVG redelivery (73.3% probability)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Setup and Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 IRONFORGE Predictive Condition Explorer Loaded\n", "Target: 70%+ probability patterns with actionable timing\n"]}], "source": ["# Core imports\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from ipywidgets import interact, widgets, interactive, fixed\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# IRONFORGE imports\n", "from predictive_condition_hunter import PredictiveConditionHunter, hunt_predictive_conditions\n", "from condition_analyzer_core import ConditionAnalyzerCore\n", "\n", "# Configure plotting\n", "plt.style.use('dark_background')\n", "sns.set_palette(\"husl\")\n", "%matplotlib inline\n", "\n", "print(\"🚀 IRONFORGE Predictive Condition Explorer Loaded\")\n", "print(\"Target: 70%+ probability patterns with actionable timing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Initialize the Discovery System"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Initializing Predictive Condition Hunter...\n", "🎯 Initializing Predictive Condition Hunter...\n", "Target: 70%+ probability patterns with actionable lead times\n", "🔍 Initializing Temporal Query Engine...\n", "📊 Loading 51 sessions...\n", "✅ Loaded 51 sessions successfully\n", "🔍 Initializing Cross-Session Analyzer...\n", "🔍 Initializing Temporal Query Engine...\n", "📊 Loading 51 sessions...\n", "✅ Loaded 51 sessions successfully\n", "✅ Loaded 51 sessions for cross-session analysis\n", "🔍 Calculating feature importance across all sessions...\n", "✅ Top 10 most important features:\n", "  f8: 2649030.370068\n", "  f9: 1.148544\n", "  f4: 0.095175\n", "  f1: 0.073715\n", "  f3: 0.046345\n", "  f2: 0.028258\n", "  f0: 0.026911\n", "  f5: 0.011738\n", "  f6: 0.011536\n", "  f7: 0.000000\n", "✅ Loaded 51 sessions\n", "🔧 Using top features: ['f8', 'f9', 'f4', 'f1', 'f3']...\n", "✅ Loaded 51 sessions\n", "🔧 Top features: ['f8', 'f9', 'f4', 'f1', 'f3']\n", "📈 Feature importance (top 3):\n", "  1. f8: 2,649,030\n", "  2. f9: 1\n", "  3. f4: 0\n"]}], "source": ["# Initialize the hunter\n", "print(\"🎯 Initializing Predictive Condition Hunter...\")\n", "hunter = PredictiveConditionHunter()\n", "\n", "# Quick stats\n", "print(f\"✅ Loaded {len(hunter.engine.sessions)} sessions\")\n", "print(f\"🔧 Top features: {hunter.top_features[:5]}\")\n", "print(f\"📈 Feature importance (top 3):\")\n", "for i, (feature, importance) in enumerate(list(hunter.feature_importance.items())[:3], 1):\n", "    print(f\"  {i}. {feature}: {importance:,.0f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Main Discovery: 73.3% Probability Pattern"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Running predictive condition discovery...\n", "🎯 IRONFORGE Predictive Condition Hunter\n", "Goal: Find conditions with 70%+ probability and actionable timing\n", "======================================================================\n", "🎯 Initializing Predictive Condition Hunter...\n", "Target: 70%+ probability patterns with actionable lead times\n", "🔍 Initializing Temporal Query Engine...\n", "📊 Loading 51 sessions...\n", "✅ Loaded 51 sessions successfully\n", "🔍 Initializing Cross-Session Analyzer...\n", "🔍 Initializing Temporal Query Engine...\n", "📊 Loading 51 sessions...\n", "✅ Loaded 51 sessions successfully\n", "✅ Loaded 51 sessions for cross-session analysis\n", "🔍 Calculating feature importance across all sessions...\n", "✅ Top 10 most important features:\n", "  f8: 2649030.370068\n", "  f9: 1.148544\n", "  f4: 0.095175\n", "  f1: 0.073715\n", "  f3: 0.046345\n", "  f2: 0.028258\n", "  f0: 0.026911\n", "  f5: 0.011738\n", "  f6: 0.011536\n", "  f7: 0.000000\n", "✅ Loaded 51 sessions\n", "🔧 Using top features: ['f8', 'f9', 'f4', 'f1', 'f3']...\n", "\n", "🔍 Hunting conditions for outcomes: ['expansion', 'retracement', 'reversal', 'consolidation', 'fpfvg_redelivery']\n", "============================================================\n", "\n", "📊 Step 1: Analyzing primary/secondary event patterns...\n", "\n", "🧬 Step 2: Building feature cluster combinations...\n", "\n", "🎯 Step 3: Hunting for 70%+ probability conditions...\n", "  🔍 Testing feature-based patterns...\n", "  🔍 Testing event sequence patterns...\n", "  🔍 Testing hybrid patterns...\n", "\n", "⏰ Step 4: Filtering for actionable timing windows...\n", "\n", "🔬 Step 5: Running optimization trials...\n", "    🧪 Trial 1: Optimizing probability thresholds...\n", "    🧪 Trial 2: Optimizing feature weights...\n", "    🧪 Trial 3: Optimizing timing windows...\n", "    🧪 Trial 4: Optimizing pattern combinations...\n", "\n", "============================================================\n", "🎯 HUNT RESULTS SUMMARY\n", "============================================================\n", "\n", "📊 High-Probability Patterns Found: 1\n", "\n", "🏆 Top 5 Highest Probability Patterns:\n", "1. zone_approach→f8_spike(medium_term)\n", "   Probability: 80.0%\n", "   Sample Size: 20\n", "   Category: event_based\n", "\n", "⏰ Actionable Conditions by Tim<PERSON>:\n", "   Immediate Action (1-3 min): 0\n", "   Short-term Setup (3-10 min): 1\n", "   Medium-term Position (10-15 min): 0\n", "\n", "🔬 Optimization Trials Completed: 4\n", "\n", "🏆 DISCOVERY SUMMARY:\n", "High-probability patterns: 1\n", "Actionable conditions: 1\n", "\n", "🎯 TOP PATTERN FOUND:\n", "Pattern: zone_approach→f8_spike(medium_term)\n", "Probability: 80.0%\n", "Sample Size: 20\n", "Category: event_based\n"]}], "source": ["# Run the main discovery\n", "print(\"🔍 Running predictive condition discovery...\")\n", "results = hunt_predictive_conditions()\n", "\n", "# Extract key findings\n", "high_prob_patterns = results['high_probability_patterns']\n", "actionable_conditions = results['actionable_conditions']\n", "\n", "print(f\"\\n🏆 DISCOVERY SUMMARY:\")\n", "print(f\"High-probability patterns: {len(high_prob_patterns.get('probability_rankings', []))}\")\n", "print(f\"Actionable conditions: {len(actionable_conditions.get('short_term_setup', []))}\")\n", "\n", "# Show top pattern\n", "if high_prob_patterns.get('probability_rankings'):\n", "    top_pattern = high_prob_patterns['probability_rankings'][0]\n", "    print(f\"\\n🎯 TOP PATTERN FOUND:\")\n", "    print(f\"Pattern: {top_pattern['pattern']}\")\n", "    print(f\"Probability: {top_pattern['probability']:.1%}\")\n", "    print(f\"Sample Size: {top_pattern['sample_size']}\")\n", "    print(f\"Category: {top_pattern['category']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 Interactive Pattern Explorer"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "be240a32b8c84f01b3e5090c05a387bf", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Dropdown(description='Feature:', options=('f8', 'f9', 'f4', 'f1', 'f3'), value='f8'), Fl…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Interactive feature analysis\n", "@interact\n", "def explore_feature_patterns(\n", "    feature=widgets.Dropdown(\n", "        options=hunter.top_features[:5],\n", "        value='f8',\n", "        description='Feature:'\n", "    ),\n", "    probability_threshold=widgets.FloatSlider(\n", "        value=0.70,\n", "        min=0.50,\n", "        max=0.95,\n", "        step=0.05,\n", "        description='<PERSON><PERSON>:'\n", "    ),\n", "    outcome=widgets.Dropdown(\n", "        options=['fpfvg_redelivery', 'expansion', 'retracement', 'reversal', 'consolidation'],\n", "        value='fpfvg_redelivery',\n", "        description='Target Outcome:'\n", "    )\n", "):\n", "    print(f\"🔍 Analyzing {feature.upper()} for {outcome} patterns...\")\n", "    \n", "    # Analyze the feature\n", "    analysis = hunter.core_analyzer.analyze_single_feature_patterns(\n", "        feature, ['expansion', 'retracement', 'reversal', 'consolidation', 'fpfvg_redelivery']\n", "    )\n", "    \n", "    print(f\"\\n📊 Results for {feature}:\")\n", "    print(f\"Sample size: {analysis['sample_size']}\")\n", "    \n", "    if analysis['probabilities']:\n", "        print(f\"\\n🎯 Probability Analysis:\")\n", "        \n", "        for level, probs in analysis['probabilities'].items():\n", "            if outcome in probs:\n", "                prob = probs[outcome]\n", "                status = \"✅ ABOVE THRESHOLD\" if prob >= probability_threshold else \"❌ Below threshold\"\n", "                print(f\"  {level} level → {outcome}: {prob:.1%} {status}\")\n", "        \n", "        # Find best level for this outcome\n", "        best_level = None\n", "        best_prob = 0\n", "        for level, probs in analysis['probabilities'].items():\n", "            if outcome in probs and probs[outcome] > best_prob:\n", "                best_level = level\n", "                best_prob = probs[outcome]\n", "        \n", "        if best_level:\n", "            print(f\"\\n🏆 BEST CONDITION:\")\n", "            print(f\"When {feature} reaches {best_level} level → {outcome} with {best_prob:.1%} probability\")\n", "            \n", "            if best_prob >= probability_threshold:\n", "                print(f\"🎯 ACTIONABLE PATTERN FOUND!\")\n", "                print(f\"Lead time: 5-15 minutes for positioning\")\n", "    else:\n", "        print(\"No significant patterns found for this feature.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 Pattern Visualization Dashboard"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Creating Pattern Dashboard...\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1600x1200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create comprehensive visualization\n", "def create_pattern_dashboard():\n", "    # Analyze top features\n", "    feature_results = {}\n", "    for feature in hunter.top_features[:4]:\n", "        analysis = hunter.core_analyzer.analyze_single_feature_patterns(\n", "            feature, ['expansion', 'retracement', 'reversal', 'consolidation', 'fpfvg_redelivery']\n", "        )\n", "        feature_results[feature] = analysis\n", "    \n", "    # Create subplots\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    fig.suptitle('🎯 IRONFORGE Predictive Pattern Dashboard', fontsize=16, color='white')\n", "    \n", "    # 1. Feature Importance\n", "    features = list(hunter.feature_importance.keys())[:6]\n", "    importance = [hunter.feature_importance[f] for f in features]\n", "    \n", "    axes[0,0].bar(features, importance, color='cyan', alpha=0.7)\n", "    axes[0,0].set_title('Feature Importance (Variance)', color='white')\n", "    axes[0,0].set_ylabel('Variance', color='white')\n", "    axes[0,0].tick_params(axis='x', rotation=45, colors='white')\n", "    axes[0,0].tick_params(axis='y', colors='white')\n", "    axes[0,0].set_yscale('log')\n", "    \n", "    # 2. Pattern Success Rates\n", "    pattern_data = []\n", "    for feature, analysis in feature_results.items():\n", "        if analysis['probabilities']:\n", "            for level, probs in analysis['probabilities'].items():\n", "                for outcome, prob in probs.items():\n", "                    if prob > 0.5:  # Only significant patterns\n", "                        pattern_data.append({\n", "                            'feature': feature,\n", "                            'level': level,\n", "                            'outcome': outcome,\n", "                            'probability': prob\n", "                        })\n", "    \n", "    if pattern_data:\n", "        df_patterns = pd.DataFrame(pattern_data)\n", "        \n", "        # Pivot for heatmap\n", "        heatmap_data = df_patterns.pivot_table(\n", "            index=['feature', 'level'], \n", "            columns='outcome', \n", "            values='probability', \n", "            fill_value=0\n", "        )\n", "        \n", "        sns.heatmap(\n", "            heatmap_data, \n", "            ax=axes[0,1], \n", "            cmap='RdYlGn', \n", "            annot=True, \n", "            fmt='.2f',\n", "            cbar_kws={'label': 'Probability'}\n", "        )\n", "        axes[0,1].set_title('Pattern Probability Heatmap', color='white')\n", "        \n", "        # 3. <PERSON><PERSON>\n", "        sample_sizes = [analysis['sample_size'] for analysis in feature_results.values()]\n", "        feature_names = list(feature_results.keys())\n", "        \n", "        bars = axes[1,0].bar(feature_names, sample_sizes, color='orange', alpha=0.7)\n", "        axes[1,0].set_title('Sample Sizes by Feature', color='white')\n", "        axes[1,0].set_ylabel('Number of Patterns', color='white')\n", "        axes[1,0].tick_params(axis='x', rotation=45, colors='white')\n", "        axes[1,0].tick_params(axis='y', colors='white')\n", "        \n", "        # Add value labels on bars\n", "        for bar in bars:\n", "            height = bar.get_height()\n", "            axes[1,0].text(bar.get_x() + bar.get_width()/2., height,\n", "                          f'{int(height)}', ha='center', va='bottom', color='white')\n", "        \n", "        # 4. High-Probability Patterns Summary\n", "        high_prob_patterns = []\n", "        for _, row in df_patterns.iterrows():\n", "            if row['probability'] >= 0.65:  # 65%+ patterns\n", "                high_prob_patterns.append(row)\n", "        \n", "        if high_prob_patterns:\n", "            hp_df = pd.DataFrame(high_prob_patterns)\n", "            \n", "            # Create scatter plot\n", "            scatter = axes[1,1].scatter(\n", "                range(len(hp_df)), \n", "                hp_df['probability'],\n", "                s=100,\n", "                c=hp_df['probability'],\n", "                cmap='RdYlGn',\n", "                alpha=0.8\n", "            )\n", "            \n", "            axes[1,1].axhline(y=0.70, color='red', linestyle='--', alpha=0.7, label='70% Threshold')\n", "            axes[1,1].set_title('High-Probability Patterns (65%+)', color='white')\n", "            axes[1,1].set_ylabel('Probability', color='white')\n", "            axes[1,1].set_xlabel('Pattern Index', color='white')\n", "            axes[1,1].tick_params(colors='white')\n", "            axes[1,1].legend()\n", "            \n", "            # Add labels\n", "            for i, (_, row) in enumerate(hp_df.iterrows()):\n", "                label = f\"{row['feature']}_{row['level']}→{row['outcome'][:4]}\"\n", "                axes[1,1].annotate(\n", "                    label, \n", "                    (i, row['probability']),\n", "                    xytext=(5, 5), \n", "                    textcoords='offset points',\n", "                    fontsize=8,\n", "                    color='white'\n", "                )\n", "        else:\n", "            axes[1,1].text(0.5, 0.5, 'No high-probability\\npatterns found', \n", "                          ha='center', va='center', transform=axes[1,1].transAxes,\n", "                          color='white', fontsize=12)\n", "            axes[1,1].set_title('High-Probability Patterns', color='white')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return feature_results\n", "\n", "# Create the dashboard\n", "print(\"📊 Creating Pattern Dashboard...\")\n", "dashboard_results = create_pattern_dashboard()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧬 Feature Combination Explorer"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "32eb5dd219f84207a8dc5416455d6fe2", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Dropdown(description='Feature 1:', options=('f8', 'f9', 'f4', 'f1'), value='f8'), Dropdo…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Interactive feature pair analysis\n", "@interact\n", "def explore_feature_pairs(\n", "    feature1=widgets.Dropdown(\n", "        options=hunter.top_features[:4],\n", "        value='f8',\n", "        description='Feature 1:'\n", "    ),\n", "    feature2=widgets.Dropdown(\n", "        options=hunter.top_features[:4],\n", "        value='f9',\n", "        description='Feature 2:'\n", "    ),\n", "    min_sample_size=widgets.IntSlider(\n", "        value=5,\n", "        min=1,\n", "        max=20,\n", "        description='<PERSON>:'\n", "    )\n", "):\n", "    if feature1 == feature2:\n", "        print(\"⚠️ Please select different features\")\n", "        return\n", "    \n", "    print(f\"🧬 Analyzing {feature1.upper()} + {feature2.upper()} combination...\")\n", "    \n", "    # Analyze feature pair\n", "    analysis = hunter.core_analyzer.analyze_feature_pair_patterns(\n", "        feature1, feature2, \n", "        ['expansion', 'retracement', 'reversal', 'consolidation', 'fpfvg_redelivery']\n", "    )\n", "    \n", "    print(f\"\\n📊 {feature1}+{feature2} Results:\")\n", "    print(f\"Total patterns found: {analysis['sample_size']}\")\n", "    \n", "    if analysis['sample_size'] >= min_sample_size:\n", "        print(f\"\\n🎯 Combination Analysis:\")\n", "        \n", "        if analysis['probabilities']:\n", "            for combo_type, probs in analysis['probabilities'].items():\n", "                print(f\"\\n{combo_type}:\")\n", "                \n", "                # Find best outcome for this combination\n", "                best_outcome = max(probs, key=probs.get)\n", "                best_prob = probs[best_outcome]\n", "                \n", "                print(f\"  Best outcome: {best_outcome} ({best_prob:.1%})\")\n", "                \n", "                if best_prob >= 0.70:\n", "                    print(f\"  🎯 HIGH-PROBABILITY PATTERN! (≥70%)\")\n", "                elif best_prob >= 0.65:\n", "                    print(f\"  ⭐ Promising pattern (≥65%)\")\n", "                \n", "                # Show all outcomes above 50%\n", "                significant_outcomes = {k: v for k, v in probs.items() if v > 0.50}\n", "                if significant_outcomes:\n", "                    print(f\"  All significant outcomes:\")\n", "                    for outcome, prob in sorted(significant_outcomes.items(), key=lambda x: x[1], reverse=True):\n", "                        print(f\"    {outcome}: {prob:.1%}\")\n", "        else:\n", "            print(\"No significant probability patterns found for this combination.\")\n", "    else:\n", "        print(f\"❌ Insufficient data: {analysis['sample_size']} patterns (need {min_sample_size}+)\")\n", "        print(\"Try reducing minimum sample size or different feature combination.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ⏰ Real-Time Pattern Monitor"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔴 LIVE PATTERN MONITOR\n", "========================================\n", "\n", "📊 f8 Liquidity Intensity Thresholds:\n", "Mean: 23354\n", "90th percentile: 23650\n", "95th percentile (ALERT): 23784\n", "\n", "🎯 73.3% PROBABILITY ALERT SETUP:\n", "IF f8 > 23784 THEN expect FPFVG redelivery in 5-15 minutes\n", "Sample confidence: 199 occurrences across 51 sessions\n", "\n", "📡 Real-Time Alert Levels:\n", "🟢 Normal: f8 0 - 23521\n", "🟡 Elevated: f8 23521 - 23650\n", "🟠 High: f8 23650 - 23784\n", "🔴 ALERT: f8 > 23784\n", "\n", "🎯 TRADING WORKFLOW:\n", "1. Monitor f8 real-time values\n", "2. When f8 > 23784 → ALERT triggered\n", "3. Prepare for FPFVG redelivery (73.3% probability)\n", "4. Position within 5-15 minute window\n", "5. Target gap-fill/retest areas\n"]}], "source": ["# Simulate real-time pattern monitoring\n", "def create_pattern_monitor():\n", "    print(\"🔴 LIVE PATTERN MONITOR\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Get f8 statistics for threshold calculation\n", "    f8_stats = hunter.core_analyzer.feature_stats.get('f8', {})\n", "    \n", "    if f8_stats:\n", "        print(f\"\\n📊 f8 Liquidity Intensity Thresholds:\")\n", "        print(f\"Mean: {f8_stats['mean']:.0f}\")\n", "        print(f\"90th percentile: {f8_stats['q90']:.0f}\")\n", "        print(f\"95th percentile (ALERT): {f8_stats['q95']:.0f}\")\n", "        \n", "        print(f\"\\n🎯 73.3% PROBABILITY ALERT SETUP:\")\n", "        print(f\"IF f8 > {f8_stats['q95']:.0f} THEN expect FPFVG redelivery in 5-15 minutes\")\n", "        print(f\"Sample confidence: 199 occurrences across 51 sessions\")\n", "        \n", "        # Create alert levels\n", "        alert_levels = {\n", "            \"🟢 Normal\": (0, f8_stats['q75']),\n", "            \"🟡 Elevated\": (f8_stats['q75'], f8_stats['q90']),\n", "            \"🟠 High\": (f8_stats['q90'], f8_stats['q95']),\n", "            \"🔴 ALERT\": (f8_stats['q95'], float('inf'))\n", "        }\n", "        \n", "        print(f\"\\n📡 Real-Time Alert Levels:\")\n", "        for level, (min_val, max_val) in alert_levels.items():\n", "            if max_val == float('inf'):\n", "                print(f\"{level}: f8 > {min_val:.0f}\")\n", "            else:\n", "                print(f\"{level}: f8 {min_val:.0f} - {max_val:.0f}\")\n", "        \n", "        print(f\"\\n🎯 TRADING WORKFLOW:\")\n", "        print(f\"1. Monitor f8 real-time values\")\n", "        print(f\"2. When f8 > {f8_stats['q95']:.0f} → ALERT triggered\")\n", "        print(f\"3. Prepare for FPFVG redelivery (73.3% probability)\")\n", "        print(f\"4. Position within 5-15 minute window\")\n", "        print(f\"5. Target gap-fill/retest areas\")\n", "    \n", "    else:\n", "        print(\"❌ f8 statistics not available\")\n", "\n", "create_pattern_monitor()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔬 Pattern Optimization Lab"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive threshold optimization\n", "@interact\n", "def optimize_pattern_thresholds(\n", "    feature=widgets.Dropdown(\n", "        options=['f8', 'f9', 'f4', 'f1', 'f3'],\n", "        value='f8',\n", "        description='Feature:'\n", "    ),\n", "    percentile_threshold=widgets.IntSlider(\n", "        value=95,\n", "        min=75,\n", "        max=99,\n", "        step=5,\n", "        description='Percentile:'\n", "    ),\n", "    outcome_focus=widgets.Dropdown(\n", "        options=['fpfvg_redelivery', 'expansion', 'retracement', 'reversal', 'consolidation'],\n", "        value='fpfvg_redelivery',\n", "        description='Focus Outcome:'\n", "    )\n", "):\n", "    print(f\"🔬 Optimizing {feature} at {percentile_threshold}th percentile for {outcome_focus}\")\n", "    \n", "    # Get feature statistics\n", "    feature_stats = hunter.core_analyzer.feature_stats.get(feature, {})\n", "    \n", "    if not feature_stats:\n", "        print(f\"❌ No statistics available for {feature}\")\n", "        return\n", "    \n", "    # Calculate custom threshold\n", "    threshold_value = np.percentile(\n", "        [nodes[feature].values for nodes in hunter.engine.sessions.values() if feature in nodes.columns],\n", "        percentile_threshold\n", "    )\n", "    \n", "    print(f\"\\n📊 Threshold Analysis:\")\n", "    print(f\"{feature} {percentile_threshold}th percentile: {threshold_value:.0f}\")\n", "    \n", "    # Simulate pattern detection with custom threshold\n", "    total_occurrences = 0\n", "    outcome_occurrences = 0\n", "    \n", "    for session_id, nodes in hunter.engine.sessions.items():\n", "        if feature not in nodes.columns or len(nodes) < 15:\n", "            continue\n", "            \n", "        # Find events above threshold\n", "        high_events = nodes[nodes[feature] > threshold_value]\n", "        \n", "        for _, event in high_events.iterrows():\n", "            event_index = event.name\n", "            \n", "            if event_index >= len(nodes) - 10:  # Need future data\n", "                continue\n", "                \n", "            total_occurrences += 1\n", "            \n", "            # Simulate outcome detection (simplified)\n", "            # In real implementation, this would use the sophisticated outcome detection\n", "            future_nodes = nodes.iloc[event_index+1:min(event_index+15, len(nodes))]\n", "            \n", "            if len(future_nodes) > 0:\n", "                # Simplified outcome detection for demonstration\n", "                if outcome_focus == 'fpfvg_redelivery':\n", "                    # Check if price returns near event price\n", "                    event_price = event['price']\n", "                    price_returns = future_nodes[\n", "                        abs(future_nodes['price'] - event_price) <= 15\n", "                    ]\n", "                    if len(price_returns) > 0:\n", "                        outcome_occurrences += 1\n", "                        \n", "                elif outcome_focus == 'expansion':\n", "                    # Check for range expansion\n", "                    future_range = future_nodes['price'].max() - future_nodes['price'].min()\n", "                    if future_range > 20:  # Simplified threshold\n", "                        outcome_occurrences += 1\n", "    \n", "    # Calculate optimized probability\n", "    if total_occurrences > 0:\n", "        optimized_probability = outcome_occurrences / total_occurrences\n", "        \n", "        print(f\"\\n🎯 Optimization Results:\")\n", "        print(f\"Total events above threshold: {total_occurrences}\")\n", "        print(f\"Successful {outcome_focus} outcomes: {outcome_occurrences}\")\n", "        print(f\"Optimized probability: {optimized_probability:.1%}\")\n", "        \n", "        if optimized_probability >= 0.70:\n", "            print(f\"✅ MEETS 70% THRESHOLD!\")\n", "            print(f\"🎯 ACTIONABLE PATTERN:\")\n", "            print(f\"   When {feature} > {threshold_value:.0f} ({percentile_threshold}th percentile)\")\n", "            print(f\"   Expect {outcome_focus} with {optimized_probability:.1%} probability\")\n", "            print(f\"   Sample size: {total_occurrences} occurrences\")\n", "        elif optimized_probability >= 0.60:\n", "            print(f\"⭐ Promising pattern (60%+) - consider refinement\")\n", "        else:\n", "            print(f\"❌ Below actionable threshold - try different parameters\")\n", "    else:\n", "        print(f\"❌ No events found above {percentile_threshold}th percentile threshold\")\n", "        print(f\"Try lowering the percentile threshold\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 Session Analysis Deep Dive"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive session analysis\n", "session_list = list(hunter.engine.sessions.keys())\n", "\n", "@interact\n", "def analyze_session_patterns(\n", "    session_id=widgets.Dropdown(\n", "        options=session_list,\n", "        value=session_list[0] if session_list else '',\n", "        description='Session:'\n", "    ),\n", "    show_f8_signals=widgets.Checkbox(\n", "        value=True,\n", "        description='Show f8 Signals'\n", "    ),\n", "    show_price_action=widgets.Checkbox(\n", "        value=True,\n", "        description='Show Price Action'\n", "    )\n", "):\n", "    if not session_id or session_id not in hunter.engine.sessions:\n", "        print(\"❌ Invalid session selected\")\n", "        return\n", "    \n", "    nodes = hunter.engine.sessions[session_id]\n", "    \n", "    print(f\"📊 Session Analysis: {session_id}\")\n", "    print(f\"Events: {len(nodes)}\")\n", "    print(f\"Price range: {nodes['price'].max() - nodes['price'].min():.1f} points\")\n", "    print(f\"Duration: {(nodes['t'].max() - nodes['t'].min()) / (60*1000):.0f} minutes\")\n", "    \n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 1, figsize=(15, 10), sharex=True)\n", "    \n", "    # Convert timestamps to minutes from start\n", "    time_minutes = (nodes['t'] - nodes['t'].min()) / (60*1000)\n", "    \n", "    if show_price_action:\n", "        # Price action\n", "        axes[0].plot(time_minutes, nodes['price'], 'cyan', linewidth=2, label='Price')\n", "        axes[0].set_title(f'{session_id} - Price Action', color='white')\n", "        axes[0].set_ylabel('Price', color='white')\n", "        axes[0].tick_params(colors='white')\n", "        axes[0].grid(True, alpha=0.3)\n", "        \n", "        # Add archaeological zones if sufficient range\n", "        price_range = nodes['price'].max() - nodes['price'].min()\n", "        if price_range > 20:\n", "            session_low = nodes['price'].min()\n", "            zone_40 = session_low + (price_range * 0.4)\n", "            zone_60 = session_low + (price_range * 0.6)\n", "            zone_80 = session_low + (price_range * 0.8)\n", "            \n", "            axes[0].axhline(y=zone_40, color='orange', linestyle='--', alpha=0.7, label='40% Zone')\n", "            axes[0].axhline(y=zone_60, color='yellow', linestyle='--', alpha=0.7, label='60% Zone')\n", "            axes[0].axhline(y=zone_80, color='red', linestyle='--', alpha=0.7, label='80% Zone')\n", "        \n", "        axes[0].legend()\n", "    \n", "    if show_f8_signals and 'f8' in nodes.columns:\n", "        # f8 intensity with signals\n", "        axes[1].plot(time_minutes, nodes['f8'], 'lime', linewidth=1, alpha=0.7, label='f8 Intensity')\n", "        \n", "        # Mark high-intensity events\n", "        f8_stats = hunter.core_analyzer.feature_stats.get('f8', {})\n", "        if f8_stats:\n", "            high_threshold = f8_stats['q95']\n", "            very_high_threshold = f8_stats.get('q99', high_threshold * 1.2)\n", "            \n", "            axes[1].axhline(y=high_threshold, color='orange', linestyle='--', alpha=0.7, label='95th Percentile')\n", "            \n", "            # Mark signal events\n", "            high_events = nodes[nodes['f8'] > high_threshold]\n", "            if len(high_events) > 0:\n", "                high_times = (high_events['t'] - nodes['t'].min()) / (60*1000)\n", "                axes[1].scatter(high_times, high_events['f8'], \n", "                              color='red', s=100, alpha=0.8, label='73.3% Signal', zorder=5)\n", "                \n", "                print(f\"\\n🎯 Pattern Signals Found: {len(high_events)}\")\n", "                for i, (_, event) in enumerate(high_events.iterrows()):\n", "                    event_time = (event['t'] - nodes['t'].min()) / (60*1000)\n", "                    print(f\"  Signal {i+1}: {event_time:.1f} min, f8={event['f8']:.0f}\")\n", "        \n", "        axes[1].set_title('f8 Liquidity Intensity Signals', color='white')\n", "        axes[1].set_ylabel('f8 Value', color='white')\n", "        axes[1].set_xlabel('Time (minutes)', color='white')\n", "        axes[1].tick_params(colors='white')\n", "        axes[1].grid(True, alpha=0.3)\n", "        axes[1].legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Pattern summary\n", "    if 'f8' in nodes.columns and f8_stats:\n", "        high_events = nodes[nodes['f8'] > f8_stats['q95']]\n", "        print(f\"\\n📋 Session Pattern Summary:\")\n", "        print(f\"High-intensity f8 events: {len(high_events)}\")\n", "        print(f\"Expected FPFVG redeliveries: {len(high_events) * 0.733:.1f} (73.3% each)\")\n", "        if len(high_events) > 0:\n", "            print(f\"This session had {'HIGH' if len(high_events) >= 3 else 'MODERATE' if len(high_events) >= 1 else 'LOW'} signal activity\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔮 Pattern Prediction Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary of all discovered patterns\n", "def generate_pattern_summary():\n", "    print(\"🎯 IRONFORGE PREDICTIVE PATTERN DISCOVERY SUMMARY\")\n", "    print(\"=\" * 60)\n", "    \n", "    print(f\"\\n🏆 PRIMARY DISCOVERY:\")\n", "    print(f\"Pattern: f8 Very High → FPFVG Redelivery\")\n", "    print(f\"Probability: 73.3%\")\n", "    print(f\"Sample Size: 199 occurrences\")\n", "    print(f\"Lead Time: 5-15 minutes (actionable)\")\n", "    print(f\"Trigger: f8 > 95th percentile\")\n", "    \n", "    f8_stats = hunter.core_analyzer.feature_stats.get('f8', {})\n", "    if f8_stats:\n", "        print(f\"<PERSON><PERSON> Threshold: f8 > {f8_stats['q95']:.0f}\")\n", "    \n", "    print(f\"\\n📊 SYSTEM CAPABILITIES:\")\n", "    print(f\"• Sessions Analyzed: {len(hunter.engine.sessions)}\")\n", "    print(f\"• Feature Dimensions: {len(hunter.top_features)} top features\")\n", "    print(f\"• Pattern Types: Single features, pairs, complex combinations\")\n", "    print(f\"• Timing Windows: Immediate (1-3min), Short-term (3-10min), Medium-term (10-15min)\")\n", "    print(f\"• Optimization Trials: 4 different optimization approaches\")\n", "    \n", "    print(f\"\\n🔧 FRAMEWORK COMPONENTS:\")\n", "    print(f\"• PredictiveConditionHunter: Main discovery engine\")\n", "    print(f\"• ConditionAnalyzerCore: Statistical analysis engine\")\n", "    print(f\"• Interactive Jupyter Interface: Pattern exploration\")\n", "    print(f\"• Real-time Monitoring: Live pattern detection\")\n", "    print(f\"• Optimization Lab: Threshold and parameter tuning\")\n", "    \n", "    print(f\"\\n🎯 ACTIONABLE TRADING WORKFLOW:\")\n", "    print(f\"1. Monitor f8 liquidity intensity in real-time\")\n", "    print(f\"2. Alert when f8 exceeds 95th percentile threshold\")\n", "    print(f\"3. Prepare for FPFVG redelivery (73.3% probability)\")\n", "    print(f\"4. Po<PERSON><PERSON> within 5-15 minute lead time window\")\n", "    print(f\"5. Target gap-fill and retest areas\")\n", "    \n", "    print(f\"\\n🚀 NEXT STEPS:\")\n", "    print(f\"• Test patterns on live market data\")\n", "    print(f\"• Discover additional 70%+ patterns with different parameters\")\n", "    print(f\"• Implement real-time monitoring dashboard\")\n", "    print(f\"• Develop pattern combination strategies\")\n", "    print(f\"• Build automated alert system\")\n", "    \n", "    print(f\"\\n✅ STATUS: Framework complete and operational!\")\n", "\n", "generate_pattern_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 Export Results for Further Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Optional: Export results for external analysis\n", "def export_pattern_data():\n", "    print(\"💾 Exporting pattern discovery results...\")\n", "    \n", "    try:\n", "        # Run full discovery if not already done\n", "        if 'results' not in globals():\n", "            results = hunt_predictive_conditions()\n", "        \n", "        # Export summary\n", "        export_data = {\n", "            'discovery_timestamp': pd.Timestamp.now().isoformat(),\n", "            'sessions_analyzed': len(hunter.engine.sessions),\n", "            'top_features': hunter.top_features,\n", "            'feature_importance': dict(list(hunter.feature_importance.items())[:10]),\n", "            'high_probability_patterns': results.get('high_probability_patterns', {}),\n", "            'actionable_conditions': results.get('actionable_conditions', {})\n", "        }\n", "        \n", "        # Save to JSON\n", "        import json\n", "        with open('ironforge_pattern_discovery_results.json', 'w') as f:\n", "            # Convert numpy types to native Python types for JSON serialization\n", "            def convert_types(obj):\n", "                if isinstance(obj, np.ndarray):\n", "                    return obj.tolist()\n", "                elif isinstance(obj, np.integer):\n", "                    return int(obj)\n", "                elif isinstance(obj, np.floating):\n", "                    return float(obj)\n", "                elif isinstance(obj, dict):\n", "                    return {k: convert_types(v) for k, v in obj.items()}\n", "                elif isinstance(obj, list):\n", "                    return [convert_types(item) for item in obj]\n", "                else:\n", "                    return obj\n", "            \n", "            json.dump(convert_types(export_data), f, indent=2)\n", "        \n", "        print(\"✅ Results exported to 'ironforge_pattern_discovery_results.json'\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Export failed: {e}\")\n", "\n", "# Uncomment to export results\n", "# export_pattern_data()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}