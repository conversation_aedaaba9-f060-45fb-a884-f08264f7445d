# Repository Guidelines

## Project Structure & Module Organization
- Source: `ironforge/` (primary package); shared infra in `iron_core/`.
- Tests: `tests/` with `unit/`, `integration/`, and `performance/` suites.
- Scripts & data: `scripts/`, `data/`; example assets in `examples/` and `docs/`.
- Configs/entry points: `pyproject.toml`, `.pre-commit-config.yaml`, `requirements*.txt`, `orchestrator.py`, `run_*.py`.

## Build, Test, and Development Commands
- `make setup`: Create a dev env, install `.[dev]`, install pre-commit hooks.
- `make fmt`: Format with Black.
- `make lint`: Lint with Ruff (no auto-fix here).
- `make type`: Type-check with mypy (focuses on `ironforge/`).
- `make test`: Run `pytest` for `tests/` (as configured in `pyproject.toml`).
- `make precommit`: Run all hooks locally (<PERSON>, <PERSON><PERSON>, my<PERSON>, Bandit).
- Examples: `pytest -q`, `pytest tests/unit/ -q` for targeted runs.

## Coding Style & Naming Conventions
- Formatting: Black; line length 100.
- Linting: Ruff rules `E,F,I,UP,B,SIM,C4,ARG` (E501 ignored; Black governs wraps).
- Types: Prefer typed functions; mypy enforces `disallow_untyped_defs = true`.
- Naming: `snake_case` for modules/functions, `PascalCase` for classes, `UPPER_SNAKE_CASE` for constants; tests as `test_*.py`.

## Testing Guidelines
- Framework: pytest; tests live under `tests/`.
- Layout: unit tests in `tests/unit/`; integration in `tests/integration/` (excluded from some checks); performance/golden suites under `performance/`.
- Conventions: `test_<unit>_<behavior>.py::test_<case>()`.
- Run: `make test` or `pytest -q`. Add/adjust tests for all new behavior.

## Commit & Pull Request Guidelines
- Commits: Conventional Commits (e.g., `feat: …`, `fix: …`, `style: …`). Keep changes focused; add a brief rationale in the body when not obvious.
- PRs: Provide description, link issues, and attach test evidence (logs or artifacts). Ensure pre-commit and CI pass.
- CI/Reviews: GitHub Actions run lint/tests and Claude review; address findings before merge.

## Security & Configuration Tips
- Do not commit secrets; prefer env vars or `configs/` templates.
- Bandit runs via pre-commit; fix or justify findings.
- Repo guardrails: `.githooks` verifies `ironforge/` exists; keep package structure intact.
