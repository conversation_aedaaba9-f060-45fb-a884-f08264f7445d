{"engines": {"analysis": {"description": "Pattern analysis and session adaptation components", "components": [{"name": "extract_lattice_summary", "file": "extract_lattice_summary.py", "primary_classes": [], "primary_functions": ["extract_lattice_summary"], "patterns": [], "lines_of_code": 161, "complexity": 19, "class_count": 0, "function_count": 1, "docstring": "Extract and display key findings from global lattice results...", "imports": 3, "decorators": []}, {"name": "temporal_correlator", "file": "iron_core/mathematical/temporal_correlator.py", "primary_classes": ["CorrelationResult", "TemporalCorrelationEngine", "SequencePatternAnalyzer", "HTFEvent", "HTFIntensity", "HTFMasterController", "CascadeType", "<PERSON><PERSON>E<PERSON>"], "primary_functions": [], "patterns": [], "lines_of_code": 482, "complexity": 57, "class_count": 8, "function_count": 17, "docstring": "Temporal Correlator - Extracted from cascade classifier for modular integration\nHandles prediction-validation correlation and sequence analysis...", "imports": 11, "decorators": ["dataclass"]}, {"name": "__init__", "file": "ironforge/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 14, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Archaeological Discovery System\nPackage version and main exports...", "imports": 2, "decorators": []}, {"name": "__init__", "file": "ironforge/analysis/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 28, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Analysis components for pattern analysis...", "imports": 3, "decorators": []}, {"name": "broad_spectrum_archaeology", "file": "ironforge/analysis/broad_spectrum_archaeology.py", "primary_classes": ["BroadSpectrumArchaeology"], "primary_functions": [], "patterns": [], "lines_of_code": 47, "complexity": 3, "class_count": 1, "function_count": 2, "docstring": "Broad Spectrum Archaeology\nComprehensive pattern discovery across market sessions...", "imports": 5, "decorators": []}, {"name": "enhanced_session_adapter", "file": "ironforge/analysis/enhanced_session_adapter.py", "primary_classes": ["EnhancedSessionAdapter"], "primary_functions": [], "patterns": [], "lines_of_code": 47, "complexity": 3, "class_count": 1, "function_count": 2, "docstring": "Enhanced Session Adapter\nAdapts session data for archaeological analysis...", "imports": 5, "decorators": []}, {"name": "__init__", "file": "ironforge/analysis/fpfvg/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 2, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "FPFVG Network Analysis Package....", "imports": 0, "decorators": []}, {"name": "chain_builder", "file": "ironforge/analysis/fpfvg/chain_builder.py", "primary_classes": [], "primary_functions": ["construct_directed_network", "calculate_network_density", "identify_network_motifs", "find_chains", "dfs_chain"], "patterns": ["Factory"], "lines_of_code": 291, "complexity": 54, "class_count": 0, "function_count": 11, "docstring": "FPFVG Network Chain Builder - Pure DFS/Graph Construction Logic....", "imports": 3, "decorators": []}, {"name": "features", "file": "ironforge/analysis/fpfvg/features.py", "primary_classes": [], "primary_functions": ["score_redelivery_strength", "calculate_price_proximity_score", "calculate_range_pos_proximity_score", "calculate_zone_confluence_score", "calculate_temporal_penalty_score", "analyze_score_distribution", "calculate_range_position", "get_zone_proximity", "extract_magnitude", "get_candidate_summary_stats", "test_zone_enrichment", "test_pm_belt_interaction", "test_reproducibility", "generate_summary_insights"], "patterns": [], "lines_of_code": 613, "complexity": 66, "class_count": 0, "function_count": 14, "docstring": "FPFVG Feature Extraction and Scoring Logic....", "imports": 5, "decorators": []}, {"name": "runner", "file": "ironforge/analysis/fpfvg/runner.py", "primary_classes": ["FPFVGNetworkAnalyzer"], "primary_functions": ["build_chains", "validate_chain", "compute_chain_features"], "patterns": [], "lines_of_code": 352, "complexity": 21, "class_count": 1, "function_count": 8, "docstring": "FPFVG Analysis Runner - Thin Orchestration and I/O Logic....", "imports": 26, "decorators": []}, {"name": "validators", "file": "ironforge/analysis/fpfvg/validators.py", "primary_classes": [], "primary_functions": ["validate_candidates", "validate_network_graph", "is_in_pm_belt", "safe_float"], "patterns": [], "lines_of_code": 415, "complexity": 70, "class_count": 0, "function_count": 10, "docstring": "FPFVG Data Validation and Consistency Checks....", "imports": 3, "decorators": []}, {"name": "fpfvg_network_analyzer", "file": "ironforge/analysis/fpfvg_network_analyzer.py", "primary_classes": ["FPFVGNetworkAnalyzer"], "primary_functions": ["dfs_chain"], "patterns": ["Factory"], "lines_of_code": 1297, "complexity": 144, "class_count": 1, "function_count": 37, "docstring": "🔄 IRONFORGE FPFVG Redelivery Network Analyzer (Step 3A)\n========================================================\n\nMicro Mechanism Analysis: Prove FVGs form networks whose re-deliveries align with Theo...", "imports": 17, "decorators": []}, {"name": "fpfvg_redelivery_lattice", "file": "ironforge/analysis/fpfvg_redelivery_lattice.py", "primary_classes": ["FPFVGRedeliveryLattice"], "primary_functions": [], "patterns": [], "lines_of_code": 1229, "complexity": 180, "class_count": 1, "function_count": 46, "docstring": "🔄 IRONFORGE FPFVG Redelivery Network Lattice\n============================================\n\nTheory B Testing Framework: \"Zones Know Their Completion\"\nTests whether FVG redelivery events position themse...", "imports": 12, "decorators": []}, {"name": "global_lattice_builder", "file": "ironforge/analysis/global_lattice_builder.py", "primary_classes": ["GlobalLatticeBuilder"], "primary_functions": [], "patterns": ["Factory", "Builder"], "lines_of_code": 610, "complexity": 83, "class_count": 1, "function_count": 24, "docstring": "IRONFORGE Global Lattice Builder\n===============================\n\nCreates comprehensive Monthly→1m lattice mappings across all enhanced sessions.\nBuilds the global terrain map with nodes, edges, hot z...", "imports": 16, "decorators": []}, {"name": "lattice_terrain_analyzer", "file": "ironforge/analysis/lattice_terrain_analyzer.py", "primary_classes": ["LatticeTerrainAnalyzer"], "primary_functions": [], "patterns": [], "lines_of_code": 387, "complexity": 11, "class_count": 1, "function_count": 9, "docstring": "IRONFORGE Lattice <PERSON>in Analyzer\n==================================\n\nAnalyzes the global lattice results and identifies key terrain features.\nSTEP 2: Hot zone identification and cascade analysis....", "imports": 16, "decorators": []}, {"name": "refined_sweep_detector", "file": "ironforge/analysis/refined_sweep_detector.py", "primary_classes": ["RefinedSweepEvent", "RefinedSweepDetector"], "primary_functions": [], "patterns": [], "lines_of_code": 696, "complexity": 110, "class_count": 2, "function_count": 27, "docstring": "🔧 IRONFORGE Refined Sweep Detector\n==================================\n\nImmediate refinements to fix \"framework complete but data empty\" issue.\n\nKey Fixes:\n1. Weekly Sweep Detection: Lower thresholds, ...", "imports": 8, "decorators": ["dataclass"]}, {"name": "specialized_lattice_builder", "file": "ironforge/analysis/specialized_lattice_builder.py", "primary_classes": ["SpecializedLatticeBuilder"], "primary_functions": [], "patterns": ["Factory", "Builder"], "lines_of_code": 570, "complexity": 56, "class_count": 1, "function_count": 13, "docstring": "IRONFORGE Specialized Lattice Builder\n====================================\n\nSTEP 3: Builds specialized lattice views based on global terrain findings.\nFocuses on candidate areas identified in the terr...", "imports": 16, "decorators": []}, {"name": "timeframe_lattice_mapper", "file": "ironforge/analysis/timeframe_lattice_mapper.py", "primary_classes": ["TimeframeLatticeMapper"], "primary_functions": [], "patterns": [], "lines_of_code": 228, "complexity": 24, "class_count": 1, "function_count": 6, "docstring": "Timeframe Lattice Mapper\nPattern analysis component for timeframe relationships...", "imports": 5, "decorators": []}, {"name": "weekly_daily_sweep_cascade_analyzer", "file": "ironforge/analysis/weekly_daily_sweep_cascade_analyzer.py", "primary_classes": ["SweepEvent", "CascadeLink", "WeeklyDailySweepCascadeAnalyzer"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 996, "complexity": 113, "class_count": 3, "function_count": 38, "docstring": "📈 IRONFORGE Weekly→Daily Liquidity Sweep Cascade Analyzer (Step 3B)\n=====================================================================\n\nMacro Driver Analysis: Weekly dominance verification through ...", "imports": 10, "decorators": ["dataclass"]}, {"name": "weekly_daily_sweep_cascade_lattice", "file": "ironforge/analysis/weekly_daily_sweep_cascade_lattice.py", "primary_classes": ["WeeklyDailySweepCascadeLattice"], "primary_functions": [], "patterns": [], "lines_of_code": 967, "complexity": 139, "class_count": 1, "function_count": 44, "docstring": "📈 IRONFORGE Weekly→Daily Liquidity Sweep Cascade Lattice\n=========================================================\n\nMacro-Level Cascade Pattern Discovery Framework\nMaps higher timeframe (Weekly→Daily)...", "imports": 13, "decorators": []}, {"name": "__init__", "file": "ironforge/metrics/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 14, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Metrics Module\n========================\nAdvanced metrics for temporal pattern analysis and confluence scoring....", "imports": 3, "decorators": []}, {"name": "scanner", "file": "ironforge/motifs/scanner.py", "primary_classes": ["MotifMatch"], "primary_functions": ["scan_session_for_cards", "run_cli_scan"], "patterns": [], "lines_of_code": 139, "complexity": 25, "class_count": 1, "function_count": 4, "docstring": "", "imports": 7, "decorators": ["dataclass"]}, {"name": "run_fpfvg_network_analysis", "file": "run_fpfvg_network_analysis.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 346, "complexity": 38, "class_count": 0, "function_count": 1, "docstring": "🔄 IRONFORGE FPFVG Network Analysis Execution (Step 3A)\n======================================================\n\nMicro Mechanism Analysis: Prove FVGs form networks whose re-deliveries align with Theory ...", "imports": 4, "decorators": []}, {"name": "run_fpfvg_network_analysis_simple", "file": "run_fpfvg_network_analysis_simple.py", "primary_classes": ["SimpleFPFVGAnalyzer"], "primary_functions": ["main"], "patterns": ["Factory"], "lines_of_code": 454, "complexity": 54, "class_count": 1, "function_count": 16, "docstring": "🔄 IRONFORGE FPFVG Network Analysis (Step 3A) - Simplified\n==========================================================\n\nFocused implementation to prove FVG redelivery alignment with Theory B zones and P...", "imports": 13, "decorators": []}, {"name": "run_fpfvg_redelivery_lattice", "file": "run_fpfvg_redelivery_lattice.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 241, "complexity": 22, "class_count": 0, "function_count": 1, "docstring": "🔄 IRONFORGE FPFVG Redelivery Network Lattice Execution\n======================================================\n\nTheory B Testing: \"Zones Know Their Completion\"\nTests whether FVG formations position the...", "imports": 4, "decorators": []}, {"name": "run_global_lattice", "file": "run_global_lattice.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 145, "complexity": 14, "class_count": 0, "function_count": 1, "docstring": "🌐 IRONFORGE Global Lattice Execution\n====================================\n\nExecutes the comprehensive Monthly→1m global lattice build across all enhanced sessions.\nThis is STEP 1 of the discovery fram...", "imports": 4, "decorators": []}, {"name": "run_specialized_lattice", "file": "run_specialized_lattice.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 250, "complexity": 25, "class_count": 0, "function_count": 1, "docstring": "🧩 IRONFORGE Specialized Lattice Execution\n==========================================\n\nSTEP 3: Builds specialized lattice views based on terrain analysis findings.\nPriority 1: NY PM Archaeological Belt...", "imports": 4, "decorators": []}, {"name": "run_terrain_analysis", "file": "run_terrain_analysis.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 189, "complexity": 20, "class_count": 0, "function_count": 1, "docstring": "🔍 IRONFORGE Terrain Analysis Execution\n======================================\n\nSTEP 2: Analyzes the global lattice terrain to identify hot zones and cascade patterns.\nBased on the successful global la...", "imports": 4, "decorators": []}, {"name": "run_weekly_daily_cascade_lattice", "file": "run_weekly_daily_cascade_lattice.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 307, "complexity": 43, "class_count": 0, "function_count": 1, "docstring": "📈 IRONFORGE Weekly→Daily Liquidity Sweep Cascade Lattice Execution\n==================================================================\n\nMacro-Level Cascade Pattern Discovery\nMaps higher timeframe (Week...", "imports": 4, "decorators": []}, {"name": "run_weekly_daily_sweep_cascade_step_3b_refined", "file": "run_weekly_daily_sweep_cascade_step_3b_refined.py", "primary_classes": ["RefinedCascadeAnalyzer"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 436, "complexity": 38, "class_count": 1, "function_count": 8, "docstring": "📈 IRONFORGE Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B) - REFINED\n==============================================================================\n\nRefined implementation with lowered detect...", "imports": 9, "decorators": []}, {"name": "run_working_cascade_analysis", "file": "run_working_cascade_analysis.py", "primary_classes": ["WorkingSweepEvent", "WorkingCascadeLink", "WorkingCascadeAnalyzer"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 541, "complexity": 59, "class_count": 3, "function_count": 18, "docstring": "🎯 IRONFORGE Working Cascade Analysis\n====================================\n\nStreamlined implementation based on actual data structure findings.\nUses proven patterns from simple_threshold_test.py to get...", "imports": 12, "decorators": ["dataclass"]}, {"name": "analyze_concrete_patterns", "file": "scripts/analysis/analyze_concrete_patterns.py", "primary_classes": [], "primary_functions": ["analyze_actual_events_by_subpattern", "analyze_market_mechanics", "analyze_specific_examples", "decode_price_levels", "main"], "patterns": [], "lines_of_code": 342, "complexity": 56, "class_count": 0, "function_count": 5, "docstring": "Concrete Analysis: What Do These Sub-Patterns Actually Mean?\n===========================================================\nMove beyond vague descriptions to specific market mechanics...", "imports": 7, "decorators": []}, {"name": "analyze_nypm_patterns", "file": "scripts/analysis/analyze_nypm_patterns.py", "primary_classes": [], "primary_functions": ["analyze_nypm_patterns"], "patterns": [], "lines_of_code": 135, "complexity": 18, "class_count": 0, "function_count": 1, "docstring": "Analyze NY PM session patterns from IRONFORGE discoveries...", "imports": 6, "decorators": []}, {"name": "bridge_node_mapper", "file": "scripts/analysis/bridge_node_mapper.py", "primary_classes": ["BridgeNodeMapper"], "primary_functions": ["main", "convert_numpy_types"], "patterns": [], "lines_of_code": 544, "complexity": 82, "class_count": 1, "function_count": 15, "docstring": "IRONFORGE Bridge Node Mapper\n============================\n\nMaps cascade pathways from HTF events → PM belt events to identify:\n1. Whether PM belt events are terminal nodes or relay points\n2. Which HTF...", "imports": 15, "decorators": []}, {"name": "comprehensive_discovery_report", "file": "scripts/analysis/comprehensive_discovery_report.py", "primary_classes": [], "primary_functions": ["analyze_all_sessions", "analyze_single_session", "create_time_clusters", "extract_session_type", "analyze_temporal_patterns", "analyze_pattern_quality", "analyze_discovered_patterns", "main"], "patterns": ["Factory"], "lines_of_code": 354, "complexity": 53, "class_count": 0, "function_count": 8, "docstring": "Comprehensive IRONFORGE Discovery Report\n========================================\nDeep analysis of what the enhanced system with Simple Event-Time Clustering discovers...", "imports": 7, "decorators": []}, {"name": "decode_subpattern_findings", "file": "scripts/analysis/decode_subpattern_findings.py", "primary_classes": [], "primary_functions": ["decode_feature_7", "decode_feature_clusters", "decode_sub_pattern_0", "decode_sub_pattern_1", "decode_sub_pattern_2", "synthesize_discovery", "main"], "patterns": [], "lines_of_code": 248, "complexity": 8, "class_count": 0, "function_count": 7, "docstring": "Decode Sub-Pattern Findings - What is Feature 7 and Sub-Pattern 0?\n=================================================================\nDecode the actual meaning of the discovered sub-patterns and key fe...", "imports": 0, "decorators": []}, {"name": "enrichment_analyzer", "file": "scripts/analysis/enrichment_analyzer.py", "primary_classes": ["EnrichmentAnalyzer"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 370, "complexity": 40, "class_count": 1, "function_count": 11, "docstring": "IRONFORGE Enrichment Analyzer\n=============================\n\nTests the hypothesis: Do events in the 14:35-38pm PM belt disproportionately \nmap to specific lattice zones (like the 40% dimensional ancho...", "imports": 14, "decorators": []}, {"name": "explore_discoveries", "file": "scripts/analysis/explore_discoveries.py", "primary_classes": [], "primary_functions": ["load_time_patterns_from_graphs", "analyze_discovered_patterns", "explore_time_clustering_insights", "show_session_insights", "main"], "patterns": [], "lines_of_code": 258, "complexity": 37, "class_count": 0, "function_count": 5, "docstring": "Explore IRONFORGE Discovery Results with Time Pattern Intelligence\n================================================================\nSee what the Simple Event-Time Clustering + Cross-TF Mapping reveals...", "imports": 6, "decorators": []}, {"name": "investigate_cross_session_synchronization", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "primary_classes": [], "primary_functions": ["extract_event_timing_data", "build_synchronization_matrix", "identify_synchronized_time_slots", "analyze_temporal_patterns", "test_synchronization_hypothesis", "create_synchronization_visualization", "main"], "patterns": ["Factory"], "lines_of_code": 441, "complexity": 55, "class_count": 0, "function_count": 7, "docstring": "RANK 1: Cross-Session Temporal Synchronization Investigation\n===========================================================\nDiscover if events cluster at consistent intraday times across different calend...", "imports": 12, "decorators": []}, {"name": "investigate_htf_structural_inheritance", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "primary_classes": [], "primary_functions": ["extract_htf_ltf_relationships", "get_dominant_event", "analyze_htf_ltf_correlations", "discover_htf_inheritance_rules", "analyze_event_type_htf_preferences", "test_htf_coherence_hypothesis", "create_htf_inheritance_visualization", "main"], "patterns": ["Factory"], "lines_of_code": 786, "complexity": 78, "class_count": 0, "function_count": 8, "docstring": "RANK 6: Cross-Timeframe Structural Inheritance Investigation\n===========================================================\nInvestigating how events inherit structural properties from higher timeframes,\n...", "imports": 16, "decorators": []}, {"name": "investigate_pattern_subarchitecture", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "primary_classes": [], "primary_functions": ["load_tgat_patterns", "load_feature_vectors", "analyze_pattern_archetypes", "discover_sub_patterns", "analyze_clusters", "calculate_silhouette_score", "visualize_sub_patterns", "characterize_sub_patterns", "main"], "patterns": [], "lines_of_code": 343, "complexity": 30, "class_count": 0, "function_count": 9, "docstring": "TGAT Pattern Sub-Architecture Investigation\n==========================================\nDiscover hidden sub-patterns within the 3 main TGAT archetypes using 38D feature space analysis...", "imports": 12, "decorators": []}, {"name": "phase2_feature_pipeline_enhancement", "file": "scripts/analysis/phase2_feature_pipeline_enhancement.py", "primary_classes": ["FeaturePipelineEnhancer"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 527, "complexity": 50, "class_count": 1, "function_count": 9, "docstring": "IRONFORGE Phase 2: Feature Pipeline Enhancement\n============================================\n\nTGAT Model Quality Recovery - Phase 2 Implementation\n\nCONTEXT: Phase 1 breakthrough confirmed TGAT archite...", "imports": 12, "decorators": []}, {"name": "phase4_full_scale_archaeological_discovery", "file": "scripts/analysis/phase4_full_scale_archaeological_discovery.py", "primary_classes": ["FullScaleArchaeologicalDiscovery"], "primary_functions": ["run_phase4a_full_scale"], "patterns": [], "lines_of_code": 323, "complexity": 20, "class_count": 1, "function_count": 6, "docstring": "Phase 4a: Full Scale Archaeological Discovery\n============================================\nRun TGAT at full scale on all clean sessions with full features.\nTest pattern discovery on long runs and mult...", "imports": 10, "decorators": []}, {"name": "phase4b_attention_head_analysis", "file": "scripts/analysis/phase4b_attention_head_analysis.py", "primary_classes": ["AttentionHeadAnalyzer"], "primary_functions": ["run_phase4b_attention_analysis", "attention_hook", "hook_fn"], "patterns": [], "lines_of_code": 428, "complexity": 53, "class_count": 1, "function_count": 13, "docstring": "Phase 4b: 4-Head Attention Verification\n======================================\nVerify that each of the 4 TGAT attention heads discovers different\npattern archetypes, not all focusing on the same links...", "imports": 13, "decorators": []}, {"name": "phase4b_attention_verification", "file": "scripts/analysis/phase4b_attention_verification.py", "primary_classes": ["AttentionHeadAnalyzer"], "primary_functions": ["run_phase4b_attention_analysis"], "patterns": [], "lines_of_code": 334, "complexity": 35, "class_count": 1, "function_count": 8, "docstring": "Phase 4b: 4-Head Attention Verification\n======================================\nVerify that each of the 4 TGAT attention heads discovers different\npattern archetypes, not all focusing on the same links...", "imports": 13, "decorators": []}, {"name": "phase4c_temporal_resonance", "file": "scripts/analysis/phase4c_temporal_resonance.py", "primary_classes": ["TemporalResonanceAnalyzer"], "primary_functions": ["run_phase4c_temporal_resonance", "extract_date"], "patterns": [], "lines_of_code": 591, "complexity": 83, "class_count": 1, "function_count": 9, "docstring": "Phase 4c: Temporal Resonance Testing (Cross-Session Links)\n=========================================================\nProve IRONFORGE discovers permanent, cross-session structures.\nBuild multi-session ...", "imports": 15, "decorators": []}, {"name": "phase5_archaeological_discovery_validation", "file": "scripts/analysis/phase5_archaeological_discovery_validation.py", "primary_classes": ["Phase5ArchaeologicalValidator"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 461, "complexity": 26, "class_count": 1, "function_count": 9, "docstring": "Phase 5: Archaeological Discovery Validation\n==========================================\nTest TGAT model pattern discovery capability on authentic enhanced features \nafter Phase 2 decontamination to va...", "imports": 13, "decorators": []}, {"name": "phase5_direct_tgat_validation", "file": "scripts/analysis/phase5_direct_tgat_validation.py", "primary_classes": ["Phase5DirectTGATValidator"], "primary_functions": ["make_serializable", "main"], "patterns": [], "lines_of_code": 523, "complexity": 56, "class_count": 1, "function_count": 9, "docstring": "Phase 5: Direct TGAT Discovery Validation\n========================================\nDirect test of TGAT archaeological discovery on enhanced sessions\nto validate pattern quality vs contaminated baselin...", "imports": 14, "decorators": []}, {"name": "phase5_enhanced_session_validation", "file": "scripts/analysis/phase5_enhanced_session_validation.py", "primary_classes": [], "primary_functions": ["create_mock_graph_from_session", "test_enhanced_session_pattern_discovery", "analyze_pattern_quality_improvement", "main"], "patterns": ["Factory"], "lines_of_code": 259, "complexity": 25, "class_count": 0, "function_count": 4, "docstring": "Phase 5: Enhanced Session TGAT Validation\n=========================================\nTest TGAT pattern discovery on enhanced sessions and validate quality improvement....", "imports": 11, "decorators": []}, {"name": "process_all_sessions", "file": "scripts/analysis/process_all_sessions.py", "primary_classes": [], "primary_functions": ["process_all_sessions", "analyze_all_time_patterns"], "patterns": [], "lines_of_code": 169, "complexity": 18, "class_count": 0, "function_count": 2, "docstring": "Process ALL Sessions with Simple Event-Time Clustering + Cross-TF Mapping\n=========================================================================\nComplete analysis of the entire dataset with tempora...", "imports": 5, "decorators": []}, {"name": "quick_pattern_discovery", "file": "scripts/analysis/quick_pattern_discovery.py", "primary_classes": ["QuickPatternDiscovery"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 210, "complexity": 15, "class_count": 1, "function_count": 3, "docstring": "IRONFORGE Quick Pattern Discovery\n=================================\n\nFast pattern discovery script for immediate insights.\nAnalyzes top sessions and shows key patterns quickly.\n\nUsage:\n    python3 qui...", "imports": 6, "decorators": []}, {"name": "real_pattern_finder", "file": "scripts/analysis/real_pattern_finder.py", "primary_classes": [], "primary_functions": ["extract_price_from_node_feature", "find_real_patterns"], "patterns": [], "lines_of_code": 113, "complexity": 15, "class_count": 0, "function_count": 2, "docstring": "Real Pattern Finder - Finds ACTUAL cross-timeframe patterns in IRONFORGE graphs\nNo embeddings, no similarities, just real market relationships...", "imports": 5, "decorators": []}, {"name": "run_archaeology_demonstration", "file": "scripts/analysis/run_archaeology_demonstration.py", "primary_classes": ["DemoTimeframe", "DemoEventType", "DemoSessionPhase", "DemoArchaeologicalEvent"], "primary_functions": ["setup_demonstration_environment", "generate_synthetic_archaeological_events", "demonstrate_lattice_mapping", "demonstrate_temporal_clustering", "demonstrate_structural_analysis", "create_demonstration_visualizations", "generate_demonstration_report", "main", "find_cascade_paths"], "patterns": ["Factory"], "lines_of_code": 1260, "complexity": 92, "class_count": 4, "function_count": 9, "docstring": "IRONFORGE Archaeological Discovery Demonstration\n===============================================\n\nComplete demonstration of the broad-spectrum market archaeology system\nusing enhanced synthetic data t...", "imports": 20, "decorators": ["dataclass"]}, {"name": "run_contaminated_session_enhancement", "file": "scripts/analysis/run_contaminated_session_enhancement.py", "primary_classes": [], "primary_functions": ["get_contaminated_sessions", "main"], "patterns": [], "lines_of_code": 74, "complexity": 12, "class_count": 0, "function_count": 2, "docstring": "Run Phase 2 Enhancement on Contaminated Sessions\n==============================================\n\nTarget the 33 contaminated TGAT-ready sessions that need decontamination....", "imports": 3, "decorators": []}, {"name": "run_enhanced_adapter_demonstration", "file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "primary_classes": ["EnhancedAdapterDemo"], "primary_functions": ["run_quick_demo", "run_full_demo", "run_integration_test"], "patterns": [], "lines_of_code": 519, "complexity": 49, "class_count": 1, "function_count": 12, "docstring": "Enhanced Session Adapter Live Demonstration System\n==================================================\n\nDemonstrates the Enhanced Session Adapter with real IRONFORGE enhanced\nsessions, showing the dram...", "imports": 14, "decorators": []}, {"name": "run_full_archaeology_discovery", "file": "scripts/analysis/run_full_archaeology_discovery.py", "primary_classes": [], "primary_functions": ["setup_production_environment", "discover_archaeological_phenomena", "generate_lattice_mapping", "analyze_temporal_patterns", "analyze_structural_relationships", "create_comprehensive_visualizations", "generate_executive_report", "main"], "patterns": ["Factory"], "lines_of_code": 616, "complexity": 22, "class_count": 0, "function_count": 8, "docstring": "IRONFORGE Full Archaeological Discovery Workflow\n===============================================\n\nProduction workflow for running complete broad-spectrum market archaeology\non IRONFORGE's 57 enhanced ...", "imports": 20, "decorators": []}, {"name": "run_full_scale_discovery", "file": "scripts/analysis/run_full_scale_discovery.py", "primary_classes": [], "primary_functions": ["run_full_scale_discovery"], "patterns": [], "lines_of_code": 197, "complexity": 13, "class_count": 0, "function_count": 1, "docstring": "IRONFORGE Full-Scale Archaeological Discovery\n============================================\nRuns Sprint 2 discovery system on all 66 sessions to achieve 2,800+ pattern discoveries.\n\nFeatures:\n- 37D tem...", "imports": 13, "decorators": []}, {"name": "run_full_session_analysis", "file": "scripts/analysis/run_full_session_analysis.py", "primary_classes": ["SessionAnalyzer"], "primary_functions": [], "patterns": [], "lines_of_code": 443, "complexity": 23, "class_count": 1, "function_count": 9, "docstring": "IRONFORGE Full Session Analysis\n===============================\n\nComprehensive analysis script to run IRONFORGE over all logged sessions\nand extract patterns, data, timing analysis, and generate visua...", "imports": 17, "decorators": []}, {"name": "run_htf_orchestrator", "file": "scripts/analysis/run_htf_orchestrator.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 81, "complexity": 6, "class_count": 0, "function_count": 1, "docstring": "Run IRONFORGE Orchestrator with PRICE RELATIVITY Enhanced Sessions\nDemonstrates permanent pattern discovery with structural relationships\n\nCRITICAL UPGRADE: Now uses price relativity features for perm...", "imports": 4, "decorators": []}, {"name": "lattice_population_runner", "file": "scripts/utilities/lattice_population_runner.py", "primary_classes": ["LatticePopulationRunner"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 289, "complexity": 28, "class_count": 1, "function_count": 8, "docstring": "IRONFORGE Lattice Population Runner\n==================================\n\nProcesses all enhanced session events through the fixed TimeframeLatticeMapper\nto generate a comprehensive multi-timeframe latti...", "imports": 12, "decorators": []}, {"name": "setup", "file": "setup.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 60, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Setup script for IRONFORGE Archaeological Discovery System...", "imports": 3, "decorators": []}, {"name": "lattice_visualizer", "file": "visualizations/lattice_visualizer.py", "primary_classes": ["VisualizationConfig", "LatticeVisualizer"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 986, "complexity": 64, "class_count": 2, "function_count": 10, "docstring": "IRONFORGE Lattice Visualizer\n============================\n\nInteractive visualization system for the timeframe × cycle-position lattice.\nCreates comprehensive visual representations of archaeological p...", "imports": 51, "decorators": ["dataclass"]}], "file_count": 62, "total_lines": 24778, "complexity_score": 2653, "key_classes": [{"name": "CorrelationResult", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": "Result from temporal correlation analysis..."}, {"name": "TemporalCorrelationEngine", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": "Engine for correlating predictions with validation data across sequences..."}, {"name": "SequencePatternAnalyzer", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": "Analyzer for detecting patterns in cascade sequences..."}, {"name": "HTFEvent", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": "Higher Timeframe event..."}, {"name": "HTFIntensity", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": "HTF intensity calculation result..."}, {"name": "HTFMasterController", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": "Higher Timeframe Master Controller\n\nImplements fractal cascade architecture where HTF events serve a..."}, {"name": "CascadeType", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": ""}, {"name": "<PERSON><PERSON>E<PERSON>", "file": "iron_core/mathematical/temporal_correlator.py", "docstring": ""}, {"name": "BroadSpectrumArchaeology", "file": "ironforge/analysis/broad_spectrum_archaeology.py", "docstring": "Comprehensive archaeological analysis across multiple sessions\nDiscovers broad spectrum patterns and..."}, {"name": "EnhancedSessionAdapter", "file": "ironforge/analysis/enhanced_session_adapter.py", "docstring": "Adapts raw session data for enhanced archaeological analysis\nProvides session context and enhancemen..."}, {"name": "FPFVGNetworkAnalyzer", "file": "ironforge/analysis/fpfvg/runner.py", "docstring": "FPFVG Redelivery Network Analyzer - Main orchestration class\n\nCoordinates the analysis pipeline whil..."}, {"name": "FPFVGNetworkAnalyzer", "file": "ironforge/analysis/fpfvg_network_analyzer.py", "docstring": "FPFVG Redelivery Network Analyzer\n\nImplements comprehensive network analysis to prove FPFVG redelive..."}, {"name": "FPFVGRedeliveryLattice", "file": "ironforge/analysis/fpfvg_redelivery_lattice.py", "docstring": "FPFVG (Fair Value Gap) Redelivery Network Lattice Builder\n\nConstructs specialized lattice views focu..."}, {"name": "GlobalLatticeBuilder", "file": "ironforge/analysis/global_lattice_builder.py", "docstring": "Global lattice builder for comprehensive Monthly→1m analysis across all sessions.\n\nFeatures:\n- Multi..."}, {"name": "LatticeTerrainAnalyzer", "file": "ironforge/analysis/lattice_terrain_analyzer.py", "docstring": "Analyzes global lattice terrain to identify hot zones and cascade patterns.\n\nBased on the successful..."}, {"name": "RefinedSweepEvent", "file": "ironforge/analysis/refined_sweep_detector.py", "docstring": "Enhanced sweep event with refined detection..."}, {"name": "RefinedSweepDetector", "file": "ironforge/analysis/refined_sweep_detector.py", "docstring": "Refined sweep detector with relaxed thresholds and enhanced detection..."}, {"name": "SpecializedLatticeBuilder", "file": "ironforge/analysis/specialized_lattice_builder.py", "docstring": "Builds specialized lattice views for deep archaeological analysis.\n\nFocus Areas:\n1. NY PM Archaeolog..."}, {"name": "TimeframeLatticeMapper", "file": "ironforge/analysis/timeframe_lattice_mapper.py", "docstring": "Maps discovered patterns across different timeframes\nAnalyzes multi-timeframe pattern relationships..."}, {"name": "SweepEvent", "file": "ironforge/analysis/weekly_daily_sweep_cascade_analyzer.py", "docstring": "Structured sweep event representation..."}, {"name": "CascadeLink", "file": "ironforge/analysis/weekly_daily_sweep_cascade_analyzer.py", "docstring": "Cascade relationship between events..."}, {"name": "WeeklyDailySweepCascadeAnalyzer", "file": "ironforge/analysis/weekly_daily_sweep_cascade_analyzer.py", "docstring": "Weekly→Daily Liquidity Sweep Cascade Analyzer (Step 3B)\n\nImplements comprehensive macro driver analy..."}, {"name": "WeeklyDailySweepCascadeLattice", "file": "ironforge/analysis/weekly_daily_sweep_cascade_lattice.py", "docstring": "Weekly→Daily Liquidity Sweep Cascade Lattice Builder\n\nMaps macro-level cascade patterns where Weekly..."}, {"name": "MotifMatch", "file": "ironforge/motifs/scanner.py", "docstring": ""}, {"name": "SimpleFPFVGAnalyzer", "file": "run_fpfvg_network_analysis_simple.py", "docstring": "Simplified FPFVG network analyzer focused on key statistical tests..."}, {"name": "RefinedCascadeAnalyzer", "file": "run_weekly_daily_sweep_cascade_step_3b_refined.py", "docstring": "Enhanced cascade analyzer with refined detection thresholds..."}, {"name": "WorkingSweepEvent", "file": "run_working_cascade_analysis.py", "docstring": "Streamlined sweep event based on actual data patterns..."}, {"name": "WorkingCascadeLink", "file": "run_working_cascade_analysis.py", "docstring": "Streamlined cascade link..."}, {"name": "WorkingCascadeAnalyzer", "file": "run_working_cascade_analysis.py", "docstring": "Streamlined cascade analyzer using proven data patterns..."}, {"name": "BridgeNodeMapper", "file": "scripts/analysis/bridge_node_mapper.py", "docstring": "Maps HTF → PM cascade pathways and bridge node relationships..."}, {"name": "EnrichmentAnalyzer", "file": "scripts/analysis/enrichment_analyzer.py", "docstring": "Analyzes statistical enrichment of PM belt events in lattice zones..."}, {"name": "FeaturePipelineEnhancer", "file": "scripts/analysis/phase2_feature_pipeline_enhancement.py", "docstring": "Phase 2 Feature Pipeline Enhancement for TGAT Model Quality Recovery\n\nReplaces artificial default va..."}, {"name": "FullScaleArchaeologicalDiscovery", "file": "scripts/analysis/phase4_full_scale_archaeological_discovery.py", "docstring": "Full scale archaeological discovery with no session limits or chunking...."}, {"name": "AttentionHeadAnalyzer", "file": "scripts/analysis/phase4b_attention_head_analysis.py", "docstring": "Analyze TGAT attention heads to verify archeological pattern specialization...."}, {"name": "AttentionHeadAnalyzer", "file": "scripts/analysis/phase4b_attention_verification.py", "docstring": "Analyze TGAT attention heads to verify archeological pattern specialization...."}, {"name": "TemporalResonanceAnalyzer", "file": "scripts/analysis/phase4c_temporal_resonance.py", "docstring": "Analyze temporal resonance across multiple sessions...."}, {"name": "Phase5ArchaeologicalValidator", "file": "scripts/analysis/phase5_archaeological_discovery_validation.py", "docstring": "Phase 5 TGAT Archaeological Discovery Validation\nTests pattern discovery on authentic enhanced featu..."}, {"name": "Phase5DirectTGATValidator", "file": "scripts/analysis/phase5_direct_tgat_validation.py", "docstring": "Direct TGAT validation without container dependencies..."}, {"name": "QuickPatternDiscovery", "file": "scripts/analysis/quick_pattern_discovery.py", "docstring": "Fast pattern discovery for immediate insights..."}, {"name": "DemoTimeframe", "file": "scripts/analysis/run_archaeology_demonstration.py", "docstring": ""}, {"name": "DemoEventType", "file": "scripts/analysis/run_archaeology_demonstration.py", "docstring": ""}, {"name": "DemoSessionPhase", "file": "scripts/analysis/run_archaeology_demonstration.py", "docstring": ""}, {"name": "DemoArchaeologicalEvent", "file": "scripts/analysis/run_archaeology_demonstration.py", "docstring": "Enhanced synthetic archaeological event..."}, {"name": "EnhancedAdapterDemo", "file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "docstring": "Live demonstration system for Enhanced Session Adapter..."}, {"name": "SessionAnalyzer", "file": "scripts/analysis/run_full_session_analysis.py", "docstring": "Comprehensive session analysis with pattern extraction and visualization..."}, {"name": "LatticePopulationRunner", "file": "scripts/utilities/lattice_population_runner.py", "docstring": "Populates the complete IRONFORGE lattice with all available enhanced sessions..."}, {"name": "VisualizationConfig", "file": "visualizations/lattice_visualizer.py", "docstring": "Configuration for visualization settings..."}, {"name": "LatticeVisualizer", "file": "visualizations/lattice_visualizer.py", "docstring": "Comprehensive visualization system for market archaeology lattice..."}], "public_interfaces": [{"name": "extract_lattice_summary", "file": "extract_lattice_summary.py", "parameters": 1, "docstring": "Extract key summary information from lattice file..."}, {"name": "construct_directed_network", "file": "ironforge/analysis/fpfvg/chain_builder.py", "parameters": 4, "docstring": "Construct directed network of FPFVG events\n\nNetwork Rules:\n- Node = FPFVG instance (formation or red..."}, {"name": "calculate_network_density", "file": "ironforge/analysis/fpfvg/chain_builder.py", "parameters": 1, "docstring": "Calculate network density (edges / max_possible_edges)..."}, {"name": "identify_network_motifs", "file": "ironforge/analysis/fpfvg/chain_builder.py", "parameters": 1, "docstring": "Identify common network motifs (chains, convergences, divergences)..."}, {"name": "find_chains", "file": "ironforge/analysis/fpfvg/chain_builder.py", "parameters": 2, "docstring": "Find chains in the network using DFS..."}, {"name": "dfs_chain", "file": "ironforge/analysis/fpfvg/chain_builder.py", "parameters": 3, "docstring": ""}, {"name": "score_redelivery_strength", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 6, "docstring": "Score re-delivery strength using weighted factors\n\nFormula: w1·(price proximity) + w2·(range_pos pro..."}, {"name": "calculate_price_proximity_score", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 2, "docstring": "Calculate price proximity score (1.0 = identical prices)..."}, {"name": "calculate_range_pos_proximity_score", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 2, "docstring": "Calculate range position proximity score..."}, {"name": "calculate_zone_confluence_score", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 2, "docstring": "Calculate zone confluence score..."}, {"name": "calculate_temporal_penalty_score", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 2, "docstring": "Calculate temporal penalty score (higher for longer delays)..."}, {"name": "analyze_score_distribution", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 1, "docstring": "Analyze distribution of redelivery scores..."}, {"name": "calculate_range_position", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 3, "docstring": "Calculate range position (0-1) for a price level within session range\n\nArgs:\n    price_level: Price ..."}, {"name": "get_zone_proximity", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 3, "docstring": "Calculate proximity to Theory B zones\n\nArgs:\n    range_pos: Range position (0-1)\n    theory_b_zones:..."}, {"name": "extract_magnitude", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 1, "docstring": "Extract magnitude/importance of event from event data..."}, {"name": "get_candidate_summary_stats", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 1, "docstring": "Calculate summary statistics for FPFVG candidates..."}, {"name": "test_zone_enrichment", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 4, "docstring": "Test zone enrichment: are redeliveries enriched in Theory B zones?\n\nUses Fisher exact test to compar..."}, {"name": "test_pm_belt_interaction", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 3, "docstring": "Test PM-belt interaction: P(redelivery hits 14:35-:38 | prior FVG in session) vs baseline\n\nH0: No in..."}, {"name": "test_reproducibility", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 4, "docstring": "Test reproducibility with per-session bootstrap analysis\n\nGoal: Validate that findings are reproduci..."}, {"name": "generate_summary_insights", "file": "ironforge/analysis/fpfvg/features.py", "parameters": 1, "docstring": "Generate high-level insights from analysis results..."}, {"name": "build_chains", "file": "ironforge/analysis/fpfvg/runner.py", "parameters": 2, "docstring": "Build chains from adjacency list (re-exported from chain_builder)..."}, {"name": "validate_chain", "file": "ironforge/analysis/fpfvg/runner.py", "parameters": 1, "docstring": "Validate FPFVG chain data (re-exported from validators)..."}, {"name": "compute_chain_features", "file": "ironforge/analysis/fpfvg/runner.py", "parameters": 1, "docstring": "Compute chain features (re-exported from features)..."}, {"name": "validate_candidates", "file": "ironforge/analysis/fpfvg/validators.py", "parameters": 1, "docstring": "Validate FPFVG candidates for required fields and data consistency\n\nReturns validation results with ..."}, {"name": "validate_network_graph", "file": "ironforge/analysis/fpfvg/validators.py", "parameters": 1, "docstring": "Validate network graph structure and consistency..."}, {"name": "is_in_pm_belt", "file": "ironforge/analysis/fpfvg/validators.py", "parameters": 3, "docstring": "Check if timestamp falls within PM belt window\n\nArgs:\n    timestamp: ISO timestamp string\n    pm_bel..."}, {"name": "safe_float", "file": "ironforge/analysis/fpfvg/validators.py", "parameters": 2, "docstring": "Safely convert value to float with fallback..."}, {"name": "dfs_chain", "file": "ironforge/analysis/fpfvg_network_analyzer.py", "parameters": 3, "docstring": ""}, {"name": "scan_session_for_cards", "file": "ironforge/motifs/scanner.py", "parameters": 5, "docstring": ""}, {"name": "run_cli_scan", "file": "ironforge/motifs/scanner.py", "parameters": 4, "docstring": ""}, {"name": "main", "file": "run_fpfvg_network_analysis.py", "parameters": 0, "docstring": "Execute FPFVG Network Analysis (Step 3A)..."}, {"name": "main", "file": "run_fpfvg_network_analysis_simple.py", "parameters": 0, "docstring": "Execute simplified FPFVG network analysis..."}, {"name": "main", "file": "run_fpfvg_redelivery_lattice.py", "parameters": 0, "docstring": "Execute FPFVG Redelivery Network Lattice analysis..."}, {"name": "main", "file": "run_global_lattice.py", "parameters": 0, "docstring": "Execute global lattice build..."}, {"name": "main", "file": "run_specialized_lattice.py", "parameters": 0, "docstring": "Execute specialized lattice building for archaeological deep dive..."}, {"name": "main", "file": "run_terrain_analysis.py", "parameters": 0, "docstring": "Execute terrain analysis for hot zones and cascades..."}, {"name": "main", "file": "run_weekly_daily_cascade_lattice.py", "parameters": 0, "docstring": "Execute Weekly→Daily Liquidity Sweep Cascade Lattice analysis..."}, {"name": "main", "file": "run_weekly_daily_sweep_cascade_step_3b_refined.py", "parameters": 0, "docstring": "Execute REFINED Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B)..."}, {"name": "main", "file": "run_working_cascade_analysis.py", "parameters": 0, "docstring": "Execute working cascade analysis..."}, {"name": "analyze_actual_events_by_subpattern", "file": "scripts/analysis/analyze_concrete_patterns.py", "parameters": 0, "docstring": "Analyze what actual semantic events occur in each sub-pattern..."}, {"name": "analyze_market_mechanics", "file": "scripts/analysis/analyze_concrete_patterns.py", "parameters": 0, "docstring": "Analyze what these patterns mean in terms of actual market mechanics..."}, {"name": "analyze_specific_examples", "file": "scripts/analysis/analyze_concrete_patterns.py", "parameters": 0, "docstring": "Look at specific examples of each sub-pattern..."}, {"name": "decode_price_levels", "file": "scripts/analysis/analyze_concrete_patterns.py", "parameters": 0, "docstring": "Analyze what specific price levels and movements these patterns represent..."}, {"name": "main", "file": "scripts/analysis/analyze_concrete_patterns.py", "parameters": 0, "docstring": "Main concrete analysis..."}, {"name": "analyze_nypm_patterns", "file": "scripts/analysis/analyze_nypm_patterns.py", "parameters": 0, "docstring": "Analyze discovered patterns specifically for NY PM sessions..."}, {"name": "main", "file": "scripts/analysis/bridge_node_mapper.py", "parameters": 0, "docstring": "Run bridge node analysis..."}, {"name": "convert_numpy_types", "file": "scripts/analysis/bridge_node_mapper.py", "parameters": 1, "docstring": ""}, {"name": "analyze_all_sessions", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 0, "docstring": "Analyze all preserved sessions comprehensively..."}, {"name": "analyze_single_session", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 2, "docstring": "Comprehensive analysis of a single session..."}, {"name": "create_time_clusters", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 2, "docstring": "Create time clusters from events..."}, {"name": "extract_session_type", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 1, "docstring": "Extract session type from session name..."}, {"name": "analyze_temporal_patterns", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 1, "docstring": "Analyze temporal patterns across sessions..."}, {"name": "analyze_pattern_quality", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 1, "docstring": "Analyze the quality and characteristics of discovered patterns..."}, {"name": "analyze_discovered_patterns", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 0, "docstring": "Analyze the 500 patterns from TGAT discovery..."}, {"name": "main", "file": "scripts/analysis/comprehensive_discovery_report.py", "parameters": 0, "docstring": "Main analysis function..."}, {"name": "decode_feature_7", "file": "scripts/analysis/decode_subpattern_findings.py", "parameters": 0, "docstring": "Decode what Feature 7 actually represents in the 47D feature vector..."}, {"name": "decode_feature_clusters", "file": "scripts/analysis/decode_subpattern_findings.py", "parameters": 0, "docstring": "Decode the other significant features from the clustering..."}, {"name": "decode_sub_pattern_0", "file": "scripts/analysis/decode_subpattern_findings.py", "parameters": 0, "docstring": "Decode what Sub-Pattern 0 actually represents..."}, {"name": "decode_sub_pattern_1", "file": "scripts/analysis/decode_subpattern_findings.py", "parameters": 0, "docstring": "Decode what Sub-Pattern 1 represents..."}, {"name": "decode_sub_pattern_2", "file": "scripts/analysis/decode_subpattern_findings.py", "parameters": 0, "docstring": "Decode what Sub-Pattern 2 represents..."}, {"name": "synthesize_discovery", "file": "scripts/analysis/decode_subpattern_findings.py", "parameters": 0, "docstring": "Synthesize the complete discovery meaning..."}, {"name": "main", "file": "scripts/analysis/decode_subpattern_findings.py", "parameters": 0, "docstring": "Main analysis function..."}, {"name": "main", "file": "scripts/analysis/enrichment_analyzer.py", "parameters": 0, "docstring": "Run enrichment analysis..."}, {"name": "load_time_patterns_from_graphs", "file": "scripts/analysis/explore_discoveries.py", "parameters": 0, "docstring": "Load time patterns from preserved graphs..."}, {"name": "analyze_discovered_patterns", "file": "scripts/analysis/explore_discoveries.py", "parameters": 0, "docstring": "Load and analyze the 500 discovered patterns..."}, {"name": "explore_time_clustering_insights", "file": "scripts/analysis/explore_discoveries.py", "parameters": 2, "docstring": "Analyze the time clustering discoveries..."}, {"name": "show_session_insights", "file": "scripts/analysis/explore_discoveries.py", "parameters": 1, "docstring": "Show insights by session..."}, {"name": "main", "file": "scripts/analysis/explore_discoveries.py", "parameters": 0, "docstring": "Main exploration function..."}, {"name": "extract_event_timing_data", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "parameters": 0, "docstring": "Extract absolute time-of-day for each semantic event across all sessions..."}, {"name": "build_synchronization_matrix", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "parameters": 1, "docstring": "Build co-occurrence matrix: Time_Bin[i] vs Sessions[j]..."}, {"name": "identify_synchronized_time_slots", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "parameters": 2, "docstring": "Identify time slots with >60% cross-session occurrence..."}, {"name": "analyze_temporal_patterns", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "parameters": 2, "docstring": "Analyze the temporal patterns for evidence of systematic timing..."}, {"name": "test_synchronization_hypothesis", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "parameters": 2, "docstring": "Test the core hypothesis: IF event@time occurs on Day_N, THEN probability increases on Day_N+1..."}, {"name": "create_synchronization_visualization", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "parameters": 2, "docstring": "Create visualization of temporal synchronization patterns..."}, {"name": "main", "file": "scripts/analysis/investigate_cross_session_synchronization.py", "parameters": 0, "docstring": "Main cross-session synchronization investigation..."}, {"name": "extract_htf_ltf_relationships", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 0, "docstring": "Extract HTF context and corresponding LTF event characteristics..."}, {"name": "get_dominant_event", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 1, "docstring": "Determine the dominant LTF event type..."}, {"name": "analyze_htf_ltf_correlations", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 1, "docstring": "Analyze correlations between HTF context and LTF event characteristics..."}, {"name": "discover_htf_inheritance_rules", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 1, "docstring": "Discover rules for how HTF states influence LTF event characteristics..."}, {"name": "analyze_event_type_htf_preferences", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 1, "docstring": "Analyze HTF context preferences for different LTF event types..."}, {"name": "test_htf_coherence_hypothesis", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 1, "docstring": "Test the specific hypothesis that discovered patterns align across timeframes..."}, {"name": "create_htf_inheritance_visualization", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 4, "docstring": "Create comprehensive visualization of HTF-LTF inheritance patterns..."}, {"name": "main", "file": "scripts/analysis/investigate_htf_structural_inheritance.py", "parameters": 0, "docstring": "Main HTF structural inheritance investigation..."}, {"name": "load_tgat_patterns", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 0, "docstring": "Load the 568 TGAT patterns with full feature data..."}, {"name": "load_feature_vectors", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 0, "docstring": "Load 38D feature vectors from preserved graphs..."}, {"name": "analyze_pattern_archetypes", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 1, "docstring": "Analyze the distribution and characteristics of the 3 main pattern types..."}, {"name": "discover_sub_patterns", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 3, "docstring": "Discover sub-patterns using k-means clustering on 38D feature space..."}, {"name": "analyze_clusters", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 3, "docstring": "Analyze the characteristics of discovered clusters..."}, {"name": "calculate_silhouette_score", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 2, "docstring": "Calculate silhouette score for cluster quality assessment..."}, {"name": "visualize_sub_patterns", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 3, "docstring": "Create visualization of discovered sub-patterns..."}, {"name": "characterize_sub_patterns", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 3, "docstring": "Characterize the discovered sub-patterns with detailed analysis..."}, {"name": "main", "file": "scripts/analysis/investigate_pattern_subarchitecture.py", "parameters": 0, "docstring": "Main sub-pattern discovery analysis..."}, {"name": "main", "file": "scripts/analysis/phase2_feature_pipeline_enhancement.py", "parameters": 0, "docstring": "Main execution for Phase 2 Feature Pipeline Enhancement...."}, {"name": "run_phase4a_full_scale", "file": "scripts/analysis/phase4_full_scale_archaeological_discovery.py", "parameters": 0, "docstring": "Run Phase 4a full scale archaeological discovery...."}, {"name": "run_phase4b_attention_analysis", "file": "scripts/analysis/phase4b_attention_head_analysis.py", "parameters": 0, "docstring": "Run Phase 4b attention head analysis...."}, {"name": "attention_hook", "file": "scripts/analysis/phase4b_attention_head_analysis.py", "parameters": 1, "docstring": ""}, {"name": "hook_fn", "file": "scripts/analysis/phase4b_attention_head_analysis.py", "parameters": 3, "docstring": ""}, {"name": "run_phase4b_attention_analysis", "file": "scripts/analysis/phase4b_attention_verification.py", "parameters": 0, "docstring": "Run Phase 4b attention head analysis...."}, {"name": "run_phase4c_temporal_resonance", "file": "scripts/analysis/phase4c_temporal_resonance.py", "parameters": 0, "docstring": "Run Phase 4c temporal resonance analysis...."}, {"name": "extract_date", "file": "scripts/analysis/phase4c_temporal_resonance.py", "parameters": 1, "docstring": ""}, {"name": "main", "file": "scripts/analysis/phase5_archaeological_discovery_validation.py", "parameters": 0, "docstring": "Execute Phase 5 Archaeological Discovery Validation..."}, {"name": "make_serializable", "file": "scripts/analysis/phase5_direct_tgat_validation.py", "parameters": 1, "docstring": "Convert complex objects to JSON-serializable format.\n<PERSON><PERSON> RichNodeFeature, torch.Tensor, and othe..."}, {"name": "main", "file": "scripts/analysis/phase5_direct_tgat_validation.py", "parameters": 0, "docstring": "Execute Phase 5 Direct TGAT Validation..."}, {"name": "create_mock_graph_from_session", "file": "scripts/analysis/phase5_enhanced_session_validation.py", "parameters": 1, "docstring": "Convert enhanced session data to graph format for TGAT\nReturns: (X, edge_index, edge_times, edge_att..."}, {"name": "test_enhanced_session_pattern_discovery", "file": "scripts/analysis/phase5_enhanced_session_validation.py", "parameters": 0, "docstring": "Test TGAT pattern discovery on enhanced sessions..."}, {"name": "analyze_pattern_quality_improvement", "file": "scripts/analysis/phase5_enhanced_session_validation.py", "parameters": 1, "docstring": "Analyze if enhanced sessions show improved pattern quality..."}, {"name": "main", "file": "scripts/analysis/phase5_enhanced_session_validation.py", "parameters": 0, "docstring": "Run enhanced session validation..."}, {"name": "process_all_sessions", "file": "scripts/analysis/process_all_sessions.py", "parameters": 0, "docstring": "Process all available sessions with time pattern analysis..."}, {"name": "analyze_all_time_patterns", "file": "scripts/analysis/process_all_sessions.py", "parameters": 0, "docstring": "Analyze time patterns from all preserved graphs..."}, {"name": "main", "file": "scripts/analysis/quick_pattern_discovery.py", "parameters": 0, "docstring": ""}, {"name": "extract_price_from_node_feature", "file": "scripts/analysis/real_pattern_finder.py", "parameters": 1, "docstring": "Extract price from RichNodeFeature string representation..."}, {"name": "find_real_patterns", "file": "scripts/analysis/real_pattern_finder.py", "parameters": 0, "docstring": "Find ONE specific real pattern across all sessions:\n- 1m node near 23,000 level  \n- Has scale edge t..."}, {"name": "setup_demonstration_environment", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 0, "docstring": "Setup demonstration environment..."}, {"name": "generate_synthetic_archaeological_events", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 0, "docstring": "Generate comprehensive synthetic archaeological events..."}, {"name": "demonstrate_lattice_mapping", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 1, "docstring": "Demonstrate lattice mapping capabilities..."}, {"name": "demonstrate_temporal_clustering", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 1, "docstring": "Demonstrate temporal clustering analysis..."}, {"name": "demonstrate_structural_analysis", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 1, "docstring": "Demonstrate structural relationship analysis..."}, {"name": "create_demonstration_visualizations", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 3, "docstring": "Create comprehensive demonstration visualizations..."}, {"name": "generate_demonstration_report", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 5, "docstring": "Generate comprehensive demonstration report..."}, {"name": "main", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 0, "docstring": "Main demonstration workflow..."}, {"name": "find_cascade_paths", "file": "scripts/analysis/run_archaeology_demonstration.py", "parameters": 3, "docstring": ""}, {"name": "get_contaminated_sessions", "file": "scripts/analysis/run_contaminated_session_enhancement.py", "parameters": 0, "docstring": "Get list of contaminated sessions that need enhancement...."}, {"name": "main", "file": "scripts/analysis/run_contaminated_session_enhancement.py", "parameters": 0, "docstring": "Run enhancement on contaminated sessions...."}, {"name": "run_quick_demo", "file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "parameters": 0, "docstring": "Run a quick demonstration with 3 sessions..."}, {"name": "run_full_demo", "file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "parameters": 0, "docstring": "Run full demonstration with more sessions..."}, {"name": "run_integration_test", "file": "scripts/analysis/run_enhanced_adapter_demonstration.py", "parameters": 0, "docstring": "Run integration test simulating production environment..."}, {"name": "setup_production_environment", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 0, "docstring": "Setup production environment for archaeological discovery..."}, {"name": "discover_archaeological_phenomena", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 0, "docstring": "Run comprehensive archaeological discovery..."}, {"name": "generate_lattice_mapping", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 1, "docstring": "Generate complete lattice mapping with hot zones..."}, {"name": "analyze_temporal_patterns", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 1, "docstring": "Analyze temporal clustering patterns..."}, {"name": "analyze_structural_relationships", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 1, "docstring": "Analyze structural links and cascade patterns..."}, {"name": "create_comprehensive_visualizations", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 3, "docstring": "Create comprehensive visualization suite..."}, {"name": "generate_executive_report", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 1, "docstring": "Generate executive summary report..."}, {"name": "main", "file": "scripts/analysis/run_full_archaeology_discovery.py", "parameters": 0, "docstring": "Main production archaeological discovery workflow..."}, {"name": "run_full_scale_discovery", "file": "scripts/analysis/run_full_scale_discovery.py", "parameters": 0, "docstring": "Execute full-scale archaeological discovery on all 66 sessions..."}, {"name": "main", "file": "scripts/analysis/run_htf_orchestrator.py", "parameters": 0, "docstring": ""}, {"name": "main", "file": "scripts/utilities/lattice_population_runner.py", "parameters": 0, "docstring": "Run the complete lattice population..."}], "avg_complexity": 42.79}, "learning": {"description": "Machine learning, TGAT discovery, and graph building", "components": [{"name": "contracts", "file": "ironforge/contracts.py", "primary_classes": ["DiscoveryResult"], "primary_functions": [], "patterns": [], "lines_of_code": 11, "complexity": 0, "class_count": 1, "function_count": 0, "docstring": "", "imports": 2, "decorators": ["dataclass"]}, {"name": "__init__", "file": "ironforge/graph_builder/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 1, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Graph builders for igraph and PyG conversion....", "imports": 0, "decorators": []}, {"name": "igraph_builder", "file": "ironforge/graph_builder/igraph_builder.py", "primary_classes": [], "primary_functions": ["from_parquet"], "patterns": [], "lines_of_code": 15, "complexity": 1, "class_count": 0, "function_count": 1, "docstring": "Build igraph Graph objects from Parquet DataFrames....", "imports": 2, "decorators": []}, {"name": "pyg_converters", "file": "ironforge/graph_builder/pyg_converters.py", "primary_classes": [], "primary_functions": ["igraph_to_pyg"], "patterns": [], "lines_of_code": 11, "complexity": 1, "class_count": 0, "function_count": 1, "docstring": "Convert igraph objects to PyTorch Geometric format....", "imports": 2, "decorators": []}, {"name": "__init__", "file": "ironforge/learning/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 35, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Learning components for archaeological discovery...", "imports": 4, "decorators": []}, {"name": "discovery_pipeline", "file": "ironforge/learning/discovery_pipeline.py", "primary_classes": ["TemporalDiscoveryPipeline"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 645, "complexity": 62, "class_count": 1, "function_count": 12, "docstring": "Temporal Discovery Pipeline for IRONFORGE (Wave 3)\n=================================================\nShard‑aware pipeline for temporal TGAT discovery.  This class will load\nParquet shards, construct t...", "imports": 20, "decorators": []}, {"name": "enhanced_graph_builder", "file": "ironforge/learning/enhanced_graph_builder.py", "primary_classes": ["RichNodeFeature", "RichEdgeFeature", "EnhancedGraphBuilder"], "primary_functions": ["get_zone"], "patterns": ["Factory", "Builder"], "lines_of_code": 996, "complexity": 199, "class_count": 3, "function_count": 31, "docstring": "Enhanced Graph Builder for 45D/20D architecture\nArchaeological discovery graph construction with semantic features...", "imports": 9, "decorators": []}, {"name": "tgat_discovery", "file": "ironforge/learning/tgat_discovery.py", "primary_classes": ["TemporalAttentionLayer", "IRONFORGEDiscovery"], "primary_functions": [], "patterns": [], "lines_of_code": 315, "complexity": 17, "class_count": 2, "function_count": 8, "docstring": "TGAT Discovery Engine for Archaeological Pattern Discovery\nTemporal Graph Attention Network for market pattern archaeology...", "imports": 12, "decorators": []}, {"name": "cli", "file": "ironforge/sdk/cli.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 445, "complexity": 23, "class_count": 0, "function_count": 2, "docstring": "IRONFORGE SDK Command‑Line Interface (Waves 3-6)\n==================================================\nProvides CLI subcommands for:\n- ``discover‑temporal``: Run TGAT discovery on Parquet shards (Wave 3)...", "imports": 16, "decorators": []}, {"name": "orchestrator", "file": "orchestrator.py", "primary_classes": ["IRONFORGE"], "primary_functions": [], "patterns": [], "lines_of_code": 585, "complexity": 56, "class_count": 1, "function_count": 14, "docstring": "IRONFORGE Main Orchestrator\nCoordinates learning, preservation, and synthesis\nEnhanced with Performance Monitor for Sprint 2 tracking...", "imports": 24, "decorators": ["property"]}], "file_count": 10, "total_lines": 3059, "complexity_score": 359, "key_classes": [{"name": "DiscoveryResult", "file": "ironforge/contracts.py", "docstring": ""}, {"name": "TemporalDiscoveryPipeline", "file": "ironforge/learning/discovery_pipeline.py", "docstring": "Shard‑aware pipeline for temporal TGAT discovery.\n\nParameters\n----------\ndata_path : str or Path\n   ..."}, {"name": "RichNodeFeature", "file": "ironforge/learning/enhanced_graph_builder.py", "docstring": "45D node feature vector with semantic preservation..."}, {"name": "RichEdgeFeature", "file": "ironforge/learning/enhanced_graph_builder.py", "docstring": "20D edge feature vector with semantic relationships..."}, {"name": "EnhancedGraphBuilder", "file": "ironforge/learning/enhanced_graph_builder.py", "docstring": "Enhanced graph builder for archaeological discovery\nTransforms JSON session data into rich 45D/20D g..."}, {"name": "TemporalAttentionLayer", "file": "ironforge/learning/tgat_discovery.py", "docstring": "Multi-head temporal attention for 45D node features..."}, {"name": "IRONFORGEDiscovery", "file": "ironforge/learning/tgat_discovery.py", "docstring": "IRONFORGE Discovery Engine using TGAT\nArchaeological pattern discovery through temporal graph attent..."}, {"name": "IRONFORGE", "file": "orchestrator.py", "docstring": "Main orchestrator for discovery system..."}], "public_interfaces": [{"name": "from_parquet", "file": "ironforge/graph_builder/igraph_builder.py", "parameters": 2, "docstring": "Create igraph Graph from nodes and edges DataFrames...."}, {"name": "igraph_to_pyg", "file": "ironforge/graph_builder/pyg_converters.py", "parameters": 1, "docstring": "Convert igraph Graph to PyTorch Geometric Data object...."}, {"name": "get_zone", "file": "ironforge/learning/enhanced_graph_builder.py", "parameters": 1, "docstring": ""}, {"name": "main", "file": "ironforge/sdk/cli.py", "parameters": 1, "docstring": "Entry point for the `ironforge` CLI...."}], "avg_complexity": 35.9}, "synthesis": {"description": "Pattern validation and production graduation", "components": [{"name": "__init__", "file": "ironforge/synthesis/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 21, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Synthesis components for pattern validation...", "imports": 2, "decorators": []}, {"name": "pattern_graduation", "file": "ironforge/synthesis/pattern_graduation.py", "primary_classes": ["PatternGraduation"], "primary_functions": [], "patterns": [], "lines_of_code": 210, "complexity": 15, "class_count": 1, "function_count": 6, "docstring": "Pattern Graduation System\n87% threshold validation pipeline for discovered patterns...", "imports": 8, "decorators": []}, {"name": "production_graduation", "file": "ironforge/synthesis/production_graduation.py", "primary_classes": ["ProductionGraduation"], "primary_functions": [], "patterns": [], "lines_of_code": 271, "complexity": 32, "class_count": 1, "function_count": 8, "docstring": "Production Graduation System\nBridge validated patterns to production features...", "imports": 14, "decorators": []}, {"name": "metrics", "file": "ironforge/validation/metrics.py", "primary_classes": [], "primary_functions": ["precision_at_k", "temporal_auc", "motif_half_life", "pattern_stability_score", "archaeological_significance", "compute_validation_metrics"], "patterns": [], "lines_of_code": 352, "complexity": 30, "class_count": 0, "function_count": 6, "docstring": "Validation Metrics for IRONFORGE (Wave 4)\n==========================================\nCore metrics for evaluating temporal pattern discovery with emphasis on\nstability, temporal consistency, and archae...", "imports": 5, "decorators": []}, {"name": "runner", "file": "ironforge/validation/runner.py", "primary_classes": ["ValidationConfig", "ValidationRunner"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 680, "complexity": 50, "class_count": 2, "function_count": 16, "docstring": "Validation Runner for IRONFORGE (Wave 4)\n=========================================\nOrchestrates validation experiments with splits, controls, ablations,\nand comprehensive reporting for temporal patter...", "imports": 16, "decorators": ["dataclass"]}], "file_count": 5, "total_lines": 1534, "complexity_score": 127, "key_classes": [{"name": "PatternGraduation", "file": "ironforge/synthesis/pattern_graduation.py", "docstring": "Validation system ensuring discovered patterns exceed 87% baseline accuracy..."}, {"name": "ProductionGraduation", "file": "ironforge/synthesis/production_graduation.py", "docstring": "Production feature export for graduated patterns\nConverts validated archaeological discoveries into ..."}, {"name": "ValidationConfig", "file": "ironforge/validation/runner.py", "docstring": "Configuration for validation experiments.\n\nParameters\n----------\nmode : str\n    Validation mode: \"oo..."}, {"name": "ValidationRunner", "file": "ironforge/validation/runner.py", "docstring": "Orchestrates comprehensive validation experiments...."}], "public_interfaces": [{"name": "precision_at_k", "file": "ironforge/validation/metrics.py", "parameters": 3, "docstring": "Calculate precision at top-k predictions.\n\nParameters\n----------\ny_true : Sequence[int]\n    True bin..."}, {"name": "temporal_auc", "file": "ironforge/validation/metrics.py", "parameters": 3, "docstring": "AUC computed with chronological tie-breaking to reduce look-ahead bias.\n\nParameters\n----------\ny_tru..."}, {"name": "motif_half_life", "file": "ironforge/validation/metrics.py", "parameters": 1, "docstring": "Estimate stability/decay using exponential fit on inter-hit intervals.\n\nParameters\n----------\nhits_t..."}, {"name": "pattern_stability_score", "file": "ironforge/validation/metrics.py", "parameters": 3, "docstring": "Measure temporal stability of pattern scores across time windows.\n\nParameters\n----------\ny_score : S..."}, {"name": "archaeological_significance", "file": "ironforge/validation/metrics.py", "parameters": 3, "docstring": "Calculate archaeological significance metrics for discovered patterns.\n\nParameters\n----------\npatter..."}, {"name": "compute_validation_metrics", "file": "ironforge/validation/metrics.py", "parameters": 5, "docstring": "Compute comprehensive validation metrics for pattern discovery.\n\nParameters\n----------\ny_true : Sequ..."}], "avg_complexity": 25.4}, "integration": {"description": "System integration, configuration, and dependency injection", "components": [{"name": "config", "file": "config.py", "primary_classes": ["IRONFORGEConfig"], "primary_functions": ["get_config", "initialize_config"], "patterns": ["Factory"], "lines_of_code": 236, "complexity": 38, "class_count": 1, "function_count": 20, "docstring": "IRONFORGE Configuration Management\n=================================\n\nCentralized configuration system to eliminate hardcoded paths and make\nIRONFORGE deployable across different environments.\n\nSuppor...", "imports": 7, "decorators": []}, {"name": "__init__", "file": "iron_core/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 34, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Iron-Core: Shared Infrastructure for IRON Ecosystem\n==================================================\n\nProvides common performance, mathematical, and integration components\nfor all IRON suite package...", "imports": 7, "decorators": []}, {"name": "__init__", "file": "iron_core/integration/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 9, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Iron-Core Integration Module\n===========================\n\nCross-suite integration framework for IRON ecosystem.\nFuture home of cross-suite communication protocols....", "imports": 0, "decorators": []}, {"name": "__init__", "file": "iron_core/mathematical/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 21, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Iron-Core Mathematical Module\n============================\n\nShared mathematical components for IRON ecosystem including:\n- Fisher Information Monitor\n- Hawkes Engine \n- Adaptive RG Optimizer\n- Scaling...", "imports": 0, "decorators": []}, {"name": "adaptive_rg_optimizer", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "primary_classes": ["AdaptiveRGParameters", "ThresholdOptimizationResult", "ScalingCalibrationResult", "AdaptiveRGOptimizer"], "primary_functions": ["create_adaptive_rg_optimizer", "entropy_objective"], "patterns": ["Factory"], "lines_of_code": 812, "complexity": 78, "class_count": 4, "function_count": 9, "docstring": "Adaptive RG Optimizer - Mathematical Physics Engine\n===================================================\n\nTransforms IRONPULSE from heuristic pattern matching to mathematical physics\nsimulation by impl...", "imports": 13, "decorators": ["dataclass"]}, {"name": "cascade_classifier", "file": "iron_core/mathematical/cascade_classifier.py", "primary_classes": ["CascadeType", "CascadeEvent", "CascadeSequence", "CascadeClassifier"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 324, "complexity": 33, "class_count": 4, "function_count": 10, "docstring": "Cascade Classification and Sequential Analysis System\nAdvanced system to detect, classify, and correlate cascade types across temporal sequences...", "imports": 11, "decorators": ["dataclass"]}, {"name": "constraints", "file": "iron_core/mathematical/constraints.py", "primary_classes": ["SystemConstants", "HTFConstants", "RGConstants", "FPFVGConstants", "CascadeType", "CASCADE_TYPES_V1", "TheoryWeights", "ConsensusThr<PERSON>olds", "SessionPhases", "SessionTypes", "BusinessRules", "ValidationRules"], "primary_functions": ["perform_system_integrity_check"], "patterns": [], "lines_of_code": 411, "complexity": 29, "class_count": 12, "function_count": 17, "docstring": "Mathematical Constants and Business Rules - Unified Source of Truth\n\nThis module contains ALL mathematical constants and business rules extracted from the \nproven grok-claude-automation system (91.1% ...", "imports": 8, "decorators": ["staticmethod", "classmethod"]}, {"name": "fisher_information_monitor", "file": "iron_core/mathematical/fisher_information_monitor.py", "primary_classes": ["FisherSpikeResult", "RegimeTransition", "FisherInformationMonitor"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 376, "complexity": 33, "class_count": 3, "function_count": 14, "docstring": "Fisher Information Spike Monitor - The 24-Minute Crystallization Detector\n=========================================================================\n\nThis module implements the Fisher Information spike...", "imports": 10, "decorators": ["dataclass"]}, {"name": "grammar_fisher_correlator", "file": "iron_core/mathematical/grammar_fisher_correlator.py", "primary_classes": ["GrammarParseState", "FisherGrammarCorrelation", "GrammarFisherCorrelator"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 359, "complexity": 43, "class_count": 3, "function_count": 12, "docstring": "Grammar-Fisher Correlation Engine\n=================================\n\nImplements the critical discovery that Fisher spikes correspond to grammatical phrase \nboundaries - moments when partial parse tree...", "imports": 10, "decorators": ["dataclass"]}, {"name": "hawkes_engine", "file": "iron_core/mathematical/hawkes_engine.py", "primary_classes": ["HawkesParameters", "HawkesEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "primary_functions": [], "patterns": [], "lines_of_code": 306, "complexity": 25, "class_count": 3, "function_count": 9, "docstring": "Enhanced Multi-Dimensional Hawkes Process Engine\n\nINHERITANCE-BASED ENHANCEMENT APPROACH:\n- Inherits from proven grok-claude-automation HawkesCascadePredictor (91.1% accuracy)\n- Adds multi-dimensional...", "imports": 12, "decorators": ["dataclass"]}, {"name": "invariants", "file": "iron_core/mathematical/invariants.py", "primary_classes": ["DriftEvent", "Contract", "InvariantGuard"], "primary_functions": ["architectural_control", "demo_function", "decorator", "wrapper"], "patterns": [], "lines_of_code": 291, "complexity": 34, "class_count": 3, "function_count": 11, "docstring": "Invariant Guards - Lightweight Architectural Control System\n==========================================================\n\nFeature drift occurs when implementation entropy exceeds semantic binding energy...", "imports": 10, "decorators": ["guard.register", "dataclass"]}, {"name": "mathematical_hooks", "file": "iron_core/mathematical/mathematical_hooks.py", "primary_classes": ["HookType", "AlertLevel", "RecoveryAction", "HookContext", "AlertEvent", "MathematicalHook", "ParameterDriftHook", "PerformanceDegradationHook", "MathematicalInvariantValidationHook", "Hook<PERSON><PERSON><PERSON>"], "primary_functions": ["create_oracle_hook_manager"], "patterns": ["Factory"], "lines_of_code": 831, "complexity": 52, "class_count": 10, "function_count": 17, "docstring": "", "imports": 19, "decorators": ["dataclass", "abstractmethod"]}, {"name": "__init__", "file": "iron_core/mathematical/mathematical_layers/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 8, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Mathematical Layers - 5-Layer Architecture Framework\n===================================================\n\nCore mathematical abstractions for the IRON ecosystem....", "imports": 0, "decorators": []}, {"name": "api_interface", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "primary_classes": ["PredictionRequest", "PredictionResponse", "ValidationRequest", "ValidationResponse", "OptimizationRequest", "OptimizationResponse", "StatusResponse", "APIInterfaceLayer", "MathematicalModelAPI", "BaseModel", "FastAPI"], "primary_functions": ["create_mathematical_api"], "patterns": ["Factory"], "lines_of_code": 707, "complexity": 49, "class_count": 11, "function_count": 14, "docstring": "Layer 5: API Interface Layer\n============================\n\nClean external APIs for mathematical models and integration endpoints.\nProvides REST API, WebSocket, and programmatic interfaces for mathemat...", "imports": 31, "decorators": ["self.app.websocket", "self.app.get", "self.app.post", "self.app.middleware", "abstractmethod"]}, {"name": "core_algorithms", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "primary_classes": ["AlgorithmPerformanceMetrics", "CoreAlgorithmLayer", "HawkesAlgorithmImplementation", "FFTOptimizedCorrelator", "QuantumInspiredOptimizer"], "primary_functions": ["create_algorithm_factory", "benchmark_all_algorithms", "negative_log_likelihood", "quadratic_objective", "test_function"], "patterns": ["Factory"], "lines_of_code": 669, "complexity": 63, "class_count": 5, "function_count": 26, "docstring": "Layer 2: Core Algorithms\n========================\n\nHigh-performance implementations of mathematical models with numerical optimization.\nTranslates theoretical models from Layer 1 into efficient, produ...", "imports": 23, "decorators": ["dataclass", "abstractmethod"]}, {"name": "integration_layer", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "primary_classes": ["IntegrationStatus", "ModelPriority", "ModelMetadata", "ModelChainStep", "<PERSON><PERSON><PERSON><PERSON>", "Integration<PERSON>ayer", "MathematicalModelRegistry"], "primary_functions": ["create_oracle_prediction_chain", "oracle_data_transform", "hawkes_output_transform", "htf_conditional_execution", "htf_parameter_adjustment", "consensus_input_transform", "consensus_output_transform"], "patterns": ["Factory", "Builder"], "lines_of_code": 660, "complexity": 77, "class_count": 7, "function_count": 27, "docstring": "Layer 3: Integration Layer\n=========================\n\nConnects mathematical models to business logic and existing Oracle systems.\nProvides model composition, chaining, and seamless integration with Or...", "imports": 27, "decorators": ["dataclass", "abstractmethod"]}, {"name": "theory_abstraction", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "primary_classes": ["MathematicalDomain", "MathematicalParameters", "MathematicalModel", "TheoryAbstractionLayer", "HawkesTheoryAbstraction", "HTFTheoryAbstraction", "InformationTheoreticModel"], "primary_functions": ["create_mathematical_model_factory", "validate_all_mathematical_models"], "patterns": ["Factory"], "lines_of_code": 436, "complexity": 47, "class_count": 7, "function_count": 37, "docstring": "Layer 1: Theory Abstraction\n===========================\n\nPure mathematical formulations without implementation details.\nDefines mathematical models, constraints, and theoretical properties.\n\nThis laye...", "imports": 13, "decorators": ["dataclass", "abstractmethod"]}, {"name": "rg_scaler_production", "file": "iron_core/mathematical/rg_scaler_production.py", "primary_classes": ["RGScalingResult", "RGScaler"], "primary_functions": ["create_production_rg_scaler"], "patterns": ["Factory"], "lines_of_code": 354, "complexity": 28, "class_count": 2, "function_count": 9, "docstring": "", "imports": 8, "decorators": ["dataclass"]}, {"name": "scaling_patterns", "file": "iron_core/mathematical/scaling_patterns.py", "primary_classes": ["ScalingStrategy", "ComputationComplexity", "ScalingConfig", "ScalingMetrics", "ScalingPattern", "HorizontalScalingPattern", "VerticalScalingPattern", "AdaptiveScalingManager"], "primary_functions": ["sample_computation"], "patterns": [], "lines_of_code": 383, "complexity": 33, "class_count": 8, "function_count": 14, "docstring": "Scaling Patterns for Mathematical Computations\n==============================================\n\nHorizontal and vertical scaling patterns with intelligent data partitioning\nfor mathematical model comput...", "imports": 21, "decorators": ["dataclass", "abstractmethod"]}, {"name": "__init__", "file": "iron_core/performance/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 20, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Iron-Core Performance Module\n===========================\n\nLazy loading and dependency injection infrastructure for IRON ecosystem.\nProvides 88.7% performance improvement through component lazy loading...", "imports": 7, "decorators": []}, {"name": "container", "file": "iron_core/performance/container.py", "primary_classes": ["IRONContainer"], "primary_functions": ["get_container", "initialize_container"], "patterns": ["Container/DI"], "lines_of_code": 151, "complexity": 13, "class_count": 1, "function_count": 8, "docstring": "IRON-Core Container Architecture\n===============================\n\nUnified dependency injection container for the entire IRON ecosystem.\nProvides shared infrastructure for IRONPULSE, IRONFORGE, and fut...", "imports": 9, "decorators": []}, {"name": "lazy_loader", "file": "iron_core/performance/lazy_loader.py", "primary_classes": ["LazyComponent", "MathematicalComponent<PERSON><PERSON>der", "LazyLoadingManager"], "primary_functions": ["get_lazy_manager", "lazy_load", "initialize_lazy_loading", "decorator", "wrapper"], "patterns": [], "lines_of_code": 352, "complexity": 51, "class_count": 3, "function_count": 21, "docstring": "IRONPULSE Lazy Loading Architecture\n==================================\n\nImplements lazy loading for mathematical components to achieve:\n- <5 second system initialization (vs 120+ current)\n- 95% memory...", "imports": 14, "decorators": ["wraps", "property", "staticmethod"]}, {"name": "setup", "file": "iron_core/setup.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 125, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Iron-Core Package Setup\n=======================\nShared infrastructure package for IRON ecosystem with dependency injection,\nlazy loading, and mathematical component management.\n\nFeatures:\n- Lazy loadi...", "imports": 3, "decorators": []}, {"name": "__init__", "file": "ironforge/integration/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 12, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Integration layer for system coordination...", "imports": 3, "decorators": []}, {"name": "ironforge_container", "file": "ironforge/integration/ironforge_container.py", "primary_classes": ["IRONFORGEContainer"], "primary_functions": ["get_ironforge_container", "initialize_ironforge_lazy_loading"], "patterns": ["Container/DI"], "lines_of_code": 72, "complexity": 10, "class_count": 1, "function_count": 6, "docstring": "IRONFORGE Container for lazy loading and dependency injection...", "imports": 5, "decorators": []}], "file_count": 25, "total_lines": 7959, "complexity_score": 736, "key_classes": [{"name": "IRONFORGEConfig", "file": "config.py", "docstring": "Configuration manager for IRONFORGE system.\n\nEliminates hardcoded paths and provides environment-spe..."}, {"name": "AdaptiveRGParameters", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "docstring": "Optimized RG parameters for current market regime..."}, {"name": "ThresholdOptimizationResult", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "docstring": "Result from information-theoretic threshold optimization..."}, {"name": "ScalingCalibrationResult", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "docstring": "Result from RG scaling exponent calibration..."}, {"name": "AdaptiveRGOptimizer", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "docstring": "Mathematical Physics Engine for RG Optimization\n\nImplements the complete transformation from heurist..."}, {"name": "CascadeType", "file": "iron_core/mathematical/cascade_classifier.py", "docstring": ""}, {"name": "CascadeEvent", "file": "iron_core/mathematical/cascade_classifier.py", "docstring": "Individual cascade event with full context..."}, {"name": "CascadeSequence", "file": "iron_core/mathematical/cascade_classifier.py", "docstring": "Sequence of related cascade events..."}, {"name": "CascadeClassifier", "file": "iron_core/mathematical/cascade_classifier.py", "docstring": "Advanced cascade classification system with sequential analysis\n\nClassifies individual cascade event..."}, {"name": "SystemConstants", "file": "iron_core/mathematical/constraints.py", "docstring": "Core mathematical invariants - DO NOT MODIFY..."}, {"name": "HTFConstants", "file": "iron_core/mathematical/constraints.py", "docstring": "Higher Timeframe (HTF) System Parameters - IMMUTABLE..."}, {"name": "RGConstants", "file": "iron_core/mathematical/constraints.py", "docstring": "Renormalization Group (RG) Scaling Constants - IMMUTABLE..."}, {"name": "FPFVGConstants", "file": "iron_core/mathematical/constraints.py", "docstring": "Fair Price Fair Value Gap (FPFVG) Constants - 87.5% Contamination Filtering..."}, {"name": "CascadeType", "file": "iron_core/mathematical/constraints.py", "docstring": "CASCADE_TYPES v1.0 - IMMUTABLE Taxonomy..."}, {"name": "CASCADE_TYPES_V1", "file": "iron_core/mathematical/constraints.py", "docstring": "Immutable cascade classification system - DO NOT MODIFY..."}, {"name": "TheoryWeights", "file": "iron_core/mathematical/constraints.py", "docstring": "Multi-Theory Integration Weights - IMMUTABLE..."}, {"name": "ConsensusThr<PERSON>olds", "file": "iron_core/mathematical/constraints.py", "docstring": "Multi-Theory Consensus Decision Thresholds..."}, {"name": "SessionPhases", "file": "iron_core/mathematical/constraints.py", "docstring": "Session Phase Enumeration..."}, {"name": "SessionTypes", "file": "iron_core/mathematical/constraints.py", "docstring": "Trading Session Types..."}, {"name": "BusinessRules", "file": "iron_core/mathematical/constraints.py", "docstring": "Domain-Specific Business Logic - Critical for System Accuracy..."}, {"name": "ValidationRules", "file": "iron_core/mathematical/constraints.py", "docstring": "Validation rules for mathematical integrity..."}, {"name": "FisherSpikeResult", "file": "iron_core/mathematical/fisher_information_monitor.py", "docstring": "Result from Fisher Information spike analysis..."}, {"name": "RegimeTransition", "file": "iron_core/mathematical/fisher_information_monitor.py", "docstring": "Detected regime transition event..."}, {"name": "FisherInformationMonitor", "file": "iron_core/mathematical/fisher_information_monitor.py", "docstring": "Fisher Information Spike Detection System\n\nMonitors Fisher Information to detect the critical crysta..."}, {"name": "GrammarParseState", "file": "iron_core/mathematical/grammar_fisher_correlator.py", "docstring": "Current state of grammatical parsing with Fisher correlation..."}, {"name": "FisherGrammarCorrelation", "file": "iron_core/mathematical/grammar_fisher_correlator.py", "docstring": "Correlation between Fisher spike and grammar phrase boundary..."}, {"name": "GrammarFisherCorrelator", "file": "iron_core/mathematical/grammar_fisher_correlator.py", "docstring": "Predictive parser that tracks Fisher Information as parsing confidence\n\nKey Discovery: Fisher spikes..."}, {"name": "HawkesParameters", "file": "iron_core/mathematical/hawkes_engine.py", "docstring": "Parameters for Hawkes process..."}, {"name": "HawkesEvent", "file": "iron_core/mathematical/hawkes_engine.py", "docstring": "Individual event in Hawkes process..."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "iron_core/mathematical/hawkes_engine.py", "docstring": "Enhanced Multi-Dimensional Hawkes Process Engine\n\nImplements proven HTF coupling with multi-dimensio..."}, {"name": "DriftEvent", "file": "iron_core/mathematical/invariants.py", "docstring": "Record of architectural drift detection..."}, {"name": "Contract", "file": "iron_core/mathematical/invariants.py", "docstring": "Semantic binding contract for functions..."}, {"name": "InvariantGuard", "file": "iron_core/mathematical/invariants.py", "docstring": "Minimal viable architectural control system..."}, {"name": "HookType", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Types of mathematical model hooks..."}, {"name": "AlertLevel", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Alert severity levels..."}, {"name": "RecoveryAction", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Automated recovery actions..."}, {"name": "HookContext", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Context information for hook execution..."}, {"name": "AlertEvent", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Mathematical model alert event..."}, {"name": "MathematicalHook", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Base class for mathematical model hooks..."}, {"name": "ParameterDriftHook", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Detects parameter drift in mathematical models.\nUses statistical tests and trend analysis to identif..."}, {"name": "PerformanceDegradationHook", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Monitors mathematical model performance degradation.\nTracks execution time, memory usage, and accura..."}, {"name": "MathematicalInvariantValidationHook", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Validates mathematical invariants and constraints.\nEnsures mathematical models maintain their theore..."}, {"name": "Hook<PERSON><PERSON><PERSON>", "file": "iron_core/mathematical/mathematical_hooks.py", "docstring": "Central manager for mathematical model hooks.\nCoordinates hook execution, manages alerts, and handle..."}, {"name": "PredictionRequest", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Request schema for mathematical predictions..."}, {"name": "PredictionResponse", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Response schema for mathematical predictions..."}, {"name": "ValidationRequest", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Request schema for model validation..."}, {"name": "ValidationResponse", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Response schema for model validation..."}, {"name": "OptimizationRequest", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Request schema for parameter optimization..."}, {"name": "OptimizationResponse", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Response schema for parameter optimization..."}, {"name": "StatusResponse", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Response schema for system status..."}, {"name": "APIInterfaceLayer", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "Base class for API interface implementations.\nProvides framework for exposing mathematical models vi..."}, {"name": "MathematicalModelAPI", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": "FastAPI-based REST API for mathematical models.\nProvides comprehensive API endpoints for all mathema..."}, {"name": "BaseModel", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": ""}, {"name": "FastAPI", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "docstring": ""}, {"name": "AlgorithmPerformanceMetrics", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "docstring": "Performance metrics for algorithm implementations..."}, {"name": "CoreAlgorithmLayer", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "docstring": "Base class for high-performance algorithm implementations.\nFocuses on computational efficiency and n..."}, {"name": "HawkesAlgorithmImplementation", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "docstring": "High-performance Hawkes process implementation with numerical optimizations.\nBased on Oracle system ..."}, {"name": "FFTOptimizedCorrelator", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "docstring": "FFT-based correlation optimization reducing O(n²) to O(n log n).\nOptimizes cross-session temporal co..."}, {"name": "QuantumInspiredOptimizer", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "docstring": "Quantum-inspired optimization for complex parameter spaces.\nUses simulated annealing with quantum tu..."}, {"name": "IntegrationStatus", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "docstring": "Status of model integration..."}, {"name": "ModelPriority", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "docstring": "Priority levels for model execution..."}, {"name": "ModelMetadata", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "docstring": "Metadata for registered mathematical models..."}, {"name": "ModelChainStep", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "docstring": "Individual step in a model execution chain..."}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "docstring": "Chain of mathematical models for complex predictions.\nSupports conditional execution, data transform..."}, {"name": "Integration<PERSON>ayer", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "docstring": "Base class for mathematical model integration with business systems.\nProvides framework for model re..."}, {"name": "MathematicalModelRegistry", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "docstring": "Registry for mathematical models with Oracle system integration.\nManages model lifecycle, dependenci..."}, {"name": "MathematicalDomain", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "docstring": "Mathematical domains for model classification..."}, {"name": "MathematicalParameters", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "docstring": "Type-safe mathematical parameter container with constraints..."}, {"name": "MathematicalModel", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "docstring": "Protocol/Interface for mathematical models.\nDefines the contract that all mathematical models must s..."}, {"name": "TheoryAbstractionLayer", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "docstring": "Base class for theoretical mathematical model abstractions.\nFocuses purely on mathematical theory wi..."}, {"name": "HawkesTheoryAbstraction", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "docstring": "Theoretical abstraction of Hawkes processes.\nBased on the validated Oracle system formulation:\nλ(t) ..."}, {"name": "HTFTheoryAbstraction", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "docstring": "Higher Time Frame (HTF) theoretical abstraction.\nMulti-scale coupling between HTF and session-level ..."}, {"name": "InformationTheoreticModel", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "docstring": "Information-theoretic mathematical model for Three-Oracle consensus.\nBased on mutual information max..."}, {"name": "RGScalingResult", "file": "iron_core/mathematical/rg_scaler_production.py", "docstring": "Results from RG scaling transformation..."}, {"name": "RGScaler", "file": "iron_core/mathematical/rg_scaler_production.py", "docstring": "Production RG Scaler - The Universal Lens\n\nImplements the experimentally-discovered inverse scaling ..."}, {"name": "ScalingStrategy", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Available scaling strategies..."}, {"name": "ComputationComplexity", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Computation complexity levels..."}, {"name": "ScalingConfig", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Configuration for scaling operations..."}, {"name": "ScalingMetrics", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Metrics from scaling operations..."}, {"name": "ScalingPattern", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Abstract base class for scaling patterns..."}, {"name": "HorizontalScalingPattern", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Horizontal scaling with data partitioning..."}, {"name": "VerticalScalingPattern", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Vertical scaling with memory optimization..."}, {"name": "AdaptiveScalingManager", "file": "iron_core/mathematical/scaling_patterns.py", "docstring": "Manager for adaptive scaling strategy selection..."}, {"name": "IRONContainer", "file": "iron_core/performance/container.py", "docstring": "IRON-Core dependency injection container.\n\nProvides unified infrastructure for mathematical componen..."}, {"name": "LazyComponent", "file": "iron_core/performance/lazy_loader.py", "docstring": "Lazy loading wrapper for mathematical components.\n\nDelays initialization until first access to elimi..."}, {"name": "MathematicalComponent<PERSON><PERSON>der", "file": "iron_core/performance/lazy_loader.py", "docstring": "Specialized loader for IRON ecosystem mathematical components.\n\nProvides validation functions for ma..."}, {"name": "LazyLoadingManager", "file": "iron_core/performance/lazy_loader.py", "docstring": "Manager for lazy loading of IRON ecosystem mathematical components.\n\nImplements performance optimiza..."}, {"name": "IRONFORGEContainer", "file": "ironforge/integration/ironforge_container.py", "docstring": "Container for lazy loading IRONFORGE components\n\nNOTE: Components are created fresh for each session..."}], "public_interfaces": [{"name": "get_config", "file": "config.py", "parameters": 1, "docstring": "Get or create global IRONFORGE configuration...."}, {"name": "initialize_config", "file": "config.py", "parameters": 1, "docstring": "Initialize IRONFORGE configuration system...."}, {"name": "create_adaptive_rg_optimizer", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "parameters": 1, "docstring": "Factory function for production adaptive RG optimizer..."}, {"name": "entropy_objective", "file": "iron_core/mathematical/adaptive_rg_optimizer.py", "parameters": 1, "docstring": "Objective function for maximum entropy threshold optimization..."}, {"name": "perform_system_integrity_check", "file": "iron_core/mathematical/constraints.py", "parameters": 0, "docstring": "Perform complete system integrity check on all constants\nReturns dict of validation results..."}, {"name": "architectural_control", "file": "iron_core/mathematical/invariants.py", "parameters": 4, "docstring": "Convenience wrapper for guard.register..."}, {"name": "demo_function", "file": "iron_core/mathematical/invariants.py", "parameters": 1, "docstring": ""}, {"name": "decorator", "file": "iron_core/mathematical/invariants.py", "parameters": 1, "docstring": ""}, {"name": "wrapper", "file": "iron_core/mathematical/invariants.py", "parameters": 2, "docstring": ""}, {"name": "create_oracle_hook_manager", "file": "iron_core/mathematical/mathematical_hooks.py", "parameters": 0, "docstring": "Create HookManager with Oracle-specific configuration..."}, {"name": "create_mathematical_api", "file": "iron_core/mathematical/mathematical_layers/api_interface.py", "parameters": 2, "docstring": "Create mathematical model API with registry and hooks..."}, {"name": "create_algorithm_factory", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "parameters": 0, "docstring": "Factory for creating algorithm implementations..."}, {"name": "benchmark_all_algorithms", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "parameters": 0, "docstring": "Benchmark all algorithm implementations..."}, {"name": "negative_log_likelihood", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "parameters": 1, "docstring": "Negative log-likelihood objective function..."}, {"name": "quadratic_objective", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "parameters": 1, "docstring": ""}, {"name": "test_function", "file": "iron_core/mathematical/mathematical_layers/core_algorithms.py", "parameters": 1, "docstring": ""}, {"name": "create_oracle_prediction_chain", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "parameters": 0, "docstring": "Create standard Oracle prediction chain..."}, {"name": "oracle_data_transform", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "parameters": 1, "docstring": "Transform Oracle session data for <PERSON><PERSON> input..."}, {"name": "hawkes_output_transform", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "parameters": 1, "docstring": "Transform Hawkes output for Oracle consumption..."}, {"name": "htf_conditional_execution", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "parameters": 1, "docstring": "Execute HTF coupling only if intensity exceeds threshold..."}, {"name": "htf_parameter_adjustment", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "parameters": 1, "docstring": "Adjust HTF parameters based on previous predictions..."}, {"name": "consensus_input_transform", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "parameters": 1, "docstring": "Prepare data for Three-Oracle system..."}, {"name": "consensus_output_transform", "file": "iron_core/mathematical/mathematical_layers/integration_layer.py", "parameters": 1, "docstring": "Transform consensus output for final prediction..."}, {"name": "create_mathematical_model_factory", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "parameters": 0, "docstring": "Factory for creating mathematical model instances..."}, {"name": "validate_all_mathematical_models", "file": "iron_core/mathematical/mathematical_layers/theory_abstraction.py", "parameters": 0, "docstring": "Validate consistency of all mathematical models..."}, {"name": "create_production_rg_scaler", "file": "iron_core/mathematical/rg_scaler_production.py", "parameters": 1, "docstring": "Create production-ready RG Scaler instance\n\nArgs:\n    config: Optional configuration overrides\n    \n..."}, {"name": "sample_computation", "file": "iron_core/mathematical/scaling_patterns.py", "parameters": 1, "docstring": "Sample mathematical computation..."}, {"name": "get_container", "file": "iron_core/performance/container.py", "parameters": 0, "docstring": "Get global IRON-Core dependency injection container with thread-safe singleton pattern.\n\nThis implem..."}, {"name": "initialize_container", "file": "iron_core/performance/container.py", "parameters": 0, "docstring": "Initialize IRON-Core container with performance reporting...."}, {"name": "get_lazy_manager", "file": "iron_core/performance/lazy_loader.py", "parameters": 0, "docstring": "Get global lazy loading manager with thread-safe singleton pattern.\n\nThread Safety:\n    This functio..."}, {"name": "lazy_load", "file": "iron_core/performance/lazy_loader.py", "parameters": 4, "docstring": "Decorator for lazy loading components (thread-safe)...."}, {"name": "initialize_lazy_loading", "file": "iron_core/performance/lazy_loader.py", "parameters": 0, "docstring": "Initialize lazy loading with standard IRON ecosystem components (thread-safe)...."}, {"name": "decorator", "file": "iron_core/performance/lazy_loader.py", "parameters": 1, "docstring": ""}, {"name": "wrapper", "file": "iron_core/performance/lazy_loader.py", "parameters": 2, "docstring": ""}, {"name": "get_ironforge_container", "file": "ironforge/integration/ironforge_container.py", "parameters": 0, "docstring": "Get the global IRONFORGE container instance..."}, {"name": "initialize_ironforge_lazy_loading", "file": "ironforge/integration/ironforge_container.py", "parameters": 0, "docstring": "Initialize IRONFORGE lazy loading system..."}], "avg_complexity": 29.44}, "validation": {"description": "Testing, validation, and quality assurance", "components": [{"name": "validation_framework", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "primary_classes": ["ValidationLevel", "TestResult", "ValidationResult", "ValidationSuite", "ValidationLayer", "MathematicalPropertyTest", "NumericalStabilityTest", "PerformanceBenchmarkTest", "MathematicalValidationFramework", "MockHawkesModel", "st"], "primary_functions": ["create_validation_framework", "given", "decorator"], "patterns": ["Factory"], "lines_of_code": 1403, "complexity": 153, "class_count": 11, "function_count": 35, "docstring": "Layer 4: Validation Framework\n============================\n\nComprehensive mathematical testing framework with property-based validation.\nEnsures mathematical accuracy, numerical stability, and perform...", "imports": 25, "decorators": ["staticmethod", "dataclass", "abstractmethod"]}, {"name": "__init__", "file": "iron_core/validation/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 8, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Iron-Core Validation Module\n===========================\n\nSystem health validation and testing framework for IRON ecosystem....", "imports": 0, "decorators": []}, {"name": "cards", "file": "ironforge/motifs/cards.py", "primary_classes": ["MotifStep", "MotifCard"], "primary_functions": ["default_cards"], "patterns": [], "lines_of_code": 63, "complexity": 1, "class_count": 2, "function_count": 1, "docstring": "", "imports": 2, "decorators": ["dataclass"]}, {"name": "__init__", "file": "ironforge/validation/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 72, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Validation Framework (Waves 1-4)\n==========================================\n\nComprehensive validation, testing, and quality assurance framework for IRONFORGE.\n\nCore Framework Components:\n- S...", "imports": 21, "decorators": []}, {"name": "controls", "file": "ironforge/validation/controls.py", "primary_classes": [], "primary_functions": ["time_shuffle_edges", "label_permutation", "node_feature_shuffle", "edge_direction_shuffle", "temporal_block_shuffle", "create_control_variants"], "patterns": ["Factory"], "lines_of_code": 302, "complexity": 20, "class_count": 0, "function_count": 6, "docstring": "Negative Controls for IRONFORGE Validation (Wave 4)\n===================================================\nImplements negative controls and synthetic shuffles to establish baselines\nand detect spurious p...", "imports": 4, "decorators": []}, {"name": "performance_monitor", "file": "ironforge/validation/performance_monitor.py", "primary_classes": ["PerformanceMonitor"], "primary_functions": [], "patterns": [], "lines_of_code": 694, "complexity": 45, "class_count": 1, "function_count": 16, "docstring": "IRONFORGE Performance Monitoring Framework\n==========================================\n\nPerformance benchmarking and monitoring system to track system performance,\ndetect regressions, and ensure optima...", "imports": 27, "decorators": ["contextmanager"]}, {"name": "splits", "file": "ironforge/validation/splits.py", "primary_classes": ["PurgedKFold"], "primary_functions": ["oos_split", "temporal_train_test_split"], "patterns": [], "lines_of_code": 176, "complexity": 6, "class_count": 1, "function_count": 3, "docstring": "Time-Series Safe Data Splitting for IRONFORGE (Wave 4)\n======================================================\nImplements purged K-fold with embargo and out-of-sample splits to prevent\nlook-ahead bias ...", "imports": 5, "decorators": ["dataclass"]}, {"name": "statistical_validator", "file": "ironforge/validation/statistical_validator.py", "primary_classes": ["StatisticalValidator"], "primary_functions": [], "patterns": [], "lines_of_code": 562, "complexity": 65, "class_count": 1, "function_count": 13, "docstring": "IRONFORGE Statistical Validation Framework\n==========================================\n\nComprehensive statistical validation for pattern quality, authenticity, and archaeological significance.\nEnsures ...", "imports": 15, "decorators": []}, {"name": "run_weekly_daily_sweep_cascade_step_3b", "file": "run_weekly_daily_sweep_cascade_step_3b.py", "primary_classes": [], "primary_functions": ["main"], "patterns": [], "lines_of_code": 240, "complexity": 20, "class_count": 0, "function_count": 1, "docstring": "📈 IRONFORGE Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B) - Execution\n================================================================================\n\nMacro Driver Analysis: Weekly dominanc...", "imports": 4, "decorators": []}], "file_count": 9, "total_lines": 3520, "complexity_score": 310, "key_classes": [{"name": "ValidationLevel", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Validation thoroughness levels..."}, {"name": "TestResult", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Test result status..."}, {"name": "ValidationResult", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Result of a validation test..."}, {"name": "ValidationSuite", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Collection of validation results..."}, {"name": "ValidationLayer", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Base class for validation layer implementations.\nProvides framework for mathematical accuracy and pe..."}, {"name": "MathematicalPropertyTest", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Property-based testing for mathematical models.\nTests mathematical properties using generated data...."}, {"name": "NumericalStabilityTest", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Tests for numerical stability of mathematical implementations.\nChecks behavior under extreme conditi..."}, {"name": "PerformanceBenchmarkTest", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Performance benchmarking for mathematical implementations.\nTests execution time, memory usage, and s..."}, {"name": "MathematicalValidationFramework", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": "Comprehensive validation framework that combines all testing approaches.\nMain entry point for mathem..."}, {"name": "MockHawkesModel", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": ""}, {"name": "st", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "docstring": ""}, {"name": "MotifStep", "file": "ironforge/motifs/cards.py", "docstring": "One step in a motif sequence...."}, {"name": "MotifCard", "file": "ironforge/motifs/cards.py", "docstring": ""}, {"name": "PerformanceMonitor", "file": "ironforge/validation/performance_monitor.py", "docstring": "Performance monitoring and benchmarking system for IRONFORGE.\n\nMonitors:\n- Execution time performanc..."}, {"name": "PurgedKFold", "file": "ironforge/validation/splits.py", "docstring": "Time-ordered, leakage-safe K-fold splits with embargo period.\n\nParameters\n----------\nn_splits : int\n..."}, {"name": "StatisticalValidator", "file": "ironforge/validation/statistical_validator.py", "docstring": "Statistical validation framework for IRONFORGE pattern quality assessment.\n\nValidates patterns again..."}], "public_interfaces": [{"name": "create_validation_framework", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "parameters": 1, "docstring": "Create mathematical validation framework with specified level..."}, {"name": "given", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "parameters": 2, "docstring": ""}, {"name": "decorator", "file": "iron_core/mathematical/mathematical_layers/validation_framework.py", "parameters": 1, "docstring": ""}, {"name": "default_cards", "file": "ironforge/motifs/cards.py", "parameters": 0, "docstring": "Three thin, testable cards derived from research notes...."}, {"name": "time_shuffle_edges", "file": "ironforge/validation/controls.py", "parameters": 3, "docstring": "Shuffle edge times across edges to break temporal signal.\n\nThis negative control destroys temporal r..."}, {"name": "label_permutation", "file": "ironforge/validation/controls.py", "parameters": 2, "docstring": "Permute labels as a negative control baseline.\n\nThis control destroys the relationship between featu..."}, {"name": "node_feature_shuffle", "file": "ironforge/validation/controls.py", "parameters": 3, "docstring": "Shuffle node features within or across feature groups.\n\nParameters\n----------\nnode_features : np.nda..."}, {"name": "edge_direction_shuffle", "file": "ironforge/validation/controls.py", "parameters": 2, "docstring": "Randomly flip edge directions to test directional sensitivity.\n\nParameters\n----------\nedge_index : n..."}, {"name": "temporal_block_shuffle", "file": "ironforge/validation/controls.py", "parameters": 3, "docstring": "Shuffle temporal blocks to preserve local structure but break global patterns.\n\nParameters\n---------..."}, {"name": "create_control_variants", "file": "ironforge/validation/controls.py", "parameters": 6, "docstring": "Create multiple negative control variants for comprehensive testing.\n\nParameters\n----------\nedge_ind..."}, {"name": "oos_split", "file": "ironforge/validation/splits.py", "parameters": 2, "docstring": "Simple out-of-sample split based on timestamp cutoff.\n\nParameters\n----------\ntimestamps : Sequence[i..."}, {"name": "temporal_train_test_split", "file": "ironforge/validation/splits.py", "parameters": 3, "docstring": "Train-test split with temporal ordering and embargo period.\n\nParameters\n----------\ntimestamps : Sequ..."}, {"name": "main", "file": "run_weekly_daily_sweep_cascade_step_3b.py", "parameters": 0, "docstring": "Execute Weekly→Daily Liquidity Sweep Cascade Analysis (Step 3B)..."}], "avg_complexity": 34.44}, "reporting": {"description": "Report generation and data visualization", "components": [{"name": "__init__", "file": "ironforge/reporting/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 22, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Wave 5 Reporting System\n==================================\nVisualization and reporting components for temporal pattern discovery.\n\nProvides:\n- Timeline heatmap generation for session density...", "imports": 4, "decorators": []}, {"name": "confluence", "file": "ironforge/reporting/confluence.py", "primary_classes": ["ConfluenceStripSpec"], "primary_functions": ["build_confluence_strip"], "patterns": [], "lines_of_code": 58, "complexity": 8, "class_count": 1, "function_count": 1, "docstring": "", "imports": 5, "decorators": ["dataclass"]}, {"name": "heatmap", "file": "ironforge/reporting/heatmap.py", "primary_classes": ["TimelineHeatmapSpec"], "primary_functions": ["build_session_heatmap"], "patterns": [], "lines_of_code": 61, "complexity": 6, "class_count": 1, "function_count": 2, "docstring": "", "imports": 5, "decorators": ["dataclass"]}, {"name": "html", "file": "ironforge/reporting/html.py", "primary_classes": [], "primary_functions": ["build_report_html"], "patterns": [], "lines_of_code": 26, "complexity": 3, "class_count": 0, "function_count": 2, "docstring": "", "imports": 3, "decorators": []}, {"name": "writer", "file": "ironforge/reporting/writer.py", "primary_classes": [], "primary_functions": ["write_png", "write_html"], "patterns": [], "lines_of_code": 16, "complexity": 2, "class_count": 0, "function_count": 2, "docstring": "", "imports": 2, "decorators": []}], "file_count": 5, "total_lines": 183, "complexity_score": 19, "key_classes": [{"name": "ConfluenceStripSpec", "file": "ironforge/reporting/confluence.py", "docstring": ""}, {"name": "TimelineHeatmapSpec", "file": "ironforge/reporting/heatmap.py", "docstring": ""}], "public_interfaces": [{"name": "build_confluence_strip", "file": "ironforge/reporting/confluence.py", "parameters": 4, "docstring": "Render a 0–100 confluence strip with optional event markers...."}, {"name": "build_session_heatmap", "file": "ironforge/reporting/heatmap.py", "parameters": 3, "docstring": "Render a single-session timeline heatmap.\nminute_bins: shape (T,), minute offsets from session open...."}, {"name": "build_report_html", "file": "ironforge/reporting/html.py", "parameters": 2, "docstring": "images: List of (caption, PIL.Image)..."}, {"name": "write_png", "file": "ironforge/reporting/writer.py", "parameters": 2, "docstring": ""}, {"name": "write_html", "file": "ironforge/reporting/writer.py", "parameters": 2, "docstring": ""}], "avg_complexity": 3.8}, "utilities": {"description": "Utility functions, scripts, and support tools", "components": [{"name": "batch_migrate_graphs", "file": "data_migration/batch_migrate_graphs.py", "primary_classes": ["BatchGraphMigrator"], "primary_functions": ["main"], "patterns": ["Factory"], "lines_of_code": 506, "complexity": 58, "class_count": 1, "function_count": 8, "docstring": "IRONFORGE Batch Graph Schema Migration\n======================================\n\nTechnical Debt Surgeon: Batch processing script for migrating\nlegacy 34D graph files to current 37D schema format while\nm...", "imports": 16, "decorators": []}, {"name": "schema_normalizer", "file": "data_migration/schema_normalizer.py", "primary_classes": ["SchemaNormalizer"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 383, "complexity": 47, "class_count": 1, "function_count": 10, "docstring": "IRONFORGE Schema Normalization - Technical Debt Surgeon Implementation\n====================================================================\n\nMigrates legacy 34D data to current 37D schema while mainta...", "imports": 10, "decorators": []}, {"name": "__init__", "file": "ironforge/scripts/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 6, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Scripts Module\n========================\nUtility scripts for data processing and system integration....", "imports": 0, "decorators": []}, {"name": "prepare_motifs_input", "file": "ironforge/scripts/prepare_motifs_input.py", "primary_classes": [], "primary_functions": ["build_motifs_input", "main"], "patterns": [], "lines_of_code": 91, "complexity": 16, "class_count": 0, "function_count": 5, "docstring": "", "imports": 6, "decorators": []}, {"name": "__init__", "file": "ironforge/sdk/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 2, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE SDK - Command-line interface and developer tools....", "imports": 0, "decorators": []}, {"name": "__init__", "file": "ironforge/utilities/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 14, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Core utilities and helpers...", "imports": 1, "decorators": []}, {"name": "performance_monitor", "file": "ironforge/utilities/performance_monitor.py", "primary_classes": ["PerformanceMonitor"], "primary_functions": [], "patterns": [], "lines_of_code": 67, "complexity": 6, "class_count": 1, "function_count": 3, "docstring": "Performance Monitor\nUtility for monitoring IRONFORGE component performance...", "imports": 7, "decorators": ["contextmanager"]}, {"name": "__init__", "file": "scripts/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 9, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Scripts Package\n========================\nUtility scripts for analysis, data processing, and system utilities....", "imports": 0, "decorators": []}, {"name": "__init__", "file": "scripts/analysis/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 8, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Analysis Scripts\n=========================\nScripts for running analysis workflows and archaeological discovery....", "imports": 0, "decorators": []}, {"name": "complete_phase2_enhancement", "file": "scripts/analysis/complete_phase2_enhancement.py", "primary_classes": [], "primary_functions": ["generate_authentic_htf_carryover", "generate_authentic_energy_density", "generate_liquidity_events", "enhance_session_features", "process_remaining_sessions"], "patterns": [], "lines_of_code": 280, "complexity": 23, "class_count": 0, "function_count": 5, "docstring": "Complete Phase 2 Feature Decontamination\n=======================================\nProcesses all remaining TGAT-ready sessions to achieve 100% coverage.\n\nEnhances the remaining 24 sessions with authenti...", "imports": 8, "decorators": []}, {"name": "investigate_causal_event_chains", "file": "scripts/analysis/investigate_causal_event_chains.py", "primary_classes": [], "primary_functions": ["extract_event_sequences", "build_transition_matrices", "analyze_lag_profiles", "identify_causal_chains", "test_specific_hypothesis", "create_causal_chain_visualization", "main"], "patterns": ["Factory"], "lines_of_code": 594, "complexity": 82, "class_count": 0, "function_count": 7, "docstring": "RANK 2: Multi-Event Causal Chain Investigation\n===============================================\nDiscover predictable sequences: expansion_phase → consolidation → liq_sweep\nwith consistent lag profiles ...", "imports": 14, "decorators": []}, {"name": "investigate_liquidity_sweep_catalyst", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "primary_classes": [], "primary_functions": ["extract_liquidity_sweep_sequences", "analyze_immediate_responses", "discover_catalyst_chains", "analyze_catalyst_timing_patterns", "test_specific_catalyst_hypotheses", "create_catalyst_visualization", "main"], "patterns": ["Factory"], "lines_of_code": 673, "complexity": 79, "class_count": 0, "function_count": 7, "docstring": "Liquidity Sweep Catalyst Investigation\n======================================\nInvestigating liq_sweep as initiating event rather than terminal event.\nFocus on discovering what causal chains liq_sweep ...", "imports": 13, "decorators": []}, {"name": "phase2_validation_framework", "file": "scripts/analysis/phase2_validation_framework.py", "primary_classes": ["FeatureAuthenticityValidator"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 414, "complexity": 55, "class_count": 1, "function_count": 12, "docstring": "IRONFORGE Phase 2: Feature Authenticity Validation Framework\n=========================================================\n\nValidates that decontamination successfully removed artificial default values\nan...", "imports": 12, "decorators": []}, {"name": "phase2_validation_summary", "file": "scripts/analysis/phase2_validation_summary.py", "primary_classes": [], "primary_functions": ["validate_decontamination"], "patterns": [], "lines_of_code": 127, "complexity": 5, "class_count": 0, "function_count": 1, "docstring": "Phase 2 Validation Summary: Before vs After Enhancement\n======================================================\n\nQuick validation showing the transformation from contaminated to authentic features....", "imports": 4, "decorators": []}, {"name": "phase4d_profile_run", "file": "scripts/analysis/phase4d_profile_run.py", "primary_classes": ["PerformanceProfiler"], "primary_functions": ["run_phase4d_performance_validation", "save_profile_csv"], "patterns": ["Factory"], "lines_of_code": 539, "complexity": 45, "class_count": 1, "function_count": 9, "docstring": "Phase 4d: Performance Validation (Full Capability, No Caps)\n==========================================================\nDemonstrate stable end-to-end runs at full sophistication without chunking/caps.\n...", "imports": 15, "decorators": []}, {"name": "run_direct_discovery", "file": "scripts/analysis/run_direct_discovery.py", "primary_classes": [], "primary_functions": ["run_direct_discovery"], "patterns": [], "lines_of_code": 70, "complexity": 7, "class_count": 0, "function_count": 1, "docstring": "Direct discovery runner - bypasses TGAT import issues...", "imports": 3, "decorators": []}, {"name": "run_manual_discovery", "file": "scripts/analysis/run_manual_discovery.py", "primary_classes": [], "primary_functions": ["run_full_discovery"], "patterns": [], "lines_of_code": 49, "complexity": 1, "class_count": 0, "function_count": 1, "docstring": "Manual IRONFORGE runner with Unicode fix...", "imports": 4, "decorators": []}, {"name": "__init__", "file": "scripts/data_processing/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 8, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Data Processing Scripts\n================================\nScripts for data transformation, enhancement, and processing pipelines....", "imports": 0, "decorators": []}, {"name": "enhanced_session_relativity_processor", "file": "scripts/data_processing/enhanced_session_relativity_processor.py", "primary_classes": ["EnhancedSessionRelativityProcessor"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 491, "complexity": 51, "class_count": 1, "function_count": 7, "docstring": "IRONFORGE Enhanced Session Relativity Processor\n===============================================\n\nTransforms absolute price patterns in enhanced sessions into permanent structural relationships.\n\nCriti...", "imports": 11, "decorators": []}, {"name": "enhanced_sessions_price_relativity_processor", "file": "scripts/data_processing/enhanced_sessions_price_relativity_processor.py", "primary_classes": ["EnhancedSessionsRelativityProcessor"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 349, "complexity": 47, "class_count": 1, "function_count": 8, "docstring": "Enhanced Sessions Price Relativity Processor\n==========================================\n\nApplies price relativity features to the 57 enhanced sessions from Phase 2\nto enable permanent structural patte...", "imports": 11, "decorators": []}, {"name": "price_field_standardizer", "file": "scripts/data_processing/price_field_standardizer.py", "primary_classes": ["PriceFieldStandardizer"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 199, "complexity": 23, "class_count": 1, "function_count": 5, "docstring": "Price Field Standardizer\n========================\nStandardizes price movement field names across all enhanced sessions.\nConverts 'price' fields to 'price_level' for graph builder compatibility.\n\nFinal...", "imports": 6, "decorators": []}, {"name": "price_relativity_generator", "file": "scripts/data_processing/price_relativity_generator.py", "primary_classes": ["PriceRelativityGenerator"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 582, "complexity": 88, "class_count": 1, "function_count": 8, "docstring": "IRONFORGE Price Relativity Feature Generator\n============================================\n\nTransforms absolute price patterns into permanent structural relationships.\n\nCritical Problem Solved:\n- Curre...", "imports": 11, "decorators": []}, {"name": "session_quality_assessor", "file": "scripts/data_processing/session_quality_assessor.py", "primary_classes": ["SessionQualityAssessor"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 438, "complexity": 77, "class_count": 1, "function_count": 9, "docstring": "Level 1 Session Quality Assessment Tool\n=====================================\n\nSystematically analyzes Level 1 session data quality to identify artificial patterns\ncausing 96.8% duplication in TGAT mo...", "imports": 11, "decorators": []}, {"name": "unicode_fix", "file": "scripts/data_processing/unicode_fix.py", "primary_classes": [], "primary_functions": ["sanitize_session_data", "load_clean_sessions"], "patterns": [], "lines_of_code": 42, "complexity": 7, "class_count": 0, "function_count": 2, "docstring": "Quick fix for Unicode issues in session data...", "imports": 3, "decorators": []}, {"name": "__init__", "file": "scripts/utilities/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 8, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "IRONFORGE Utility Scripts\n=========================\nGeneral utility scripts for debugging, monitoring, and system utilities....", "imports": 0, "decorators": []}, {"name": "benchmark_performance", "file": "scripts/utilities/benchmark_performance.py", "primary_classes": [], "primary_functions": ["measure_import_time", "benchmark_core_imports", "benchmark_container_loading", "main"], "patterns": [], "lines_of_code": 164, "complexity": 19, "class_count": 0, "function_count": 4, "docstring": "IRONFORGE Performance Benchmark\n==============================\nMeasures import times and system performance to validate refactoring claims....", "imports": 5, "decorators": []}, {"name": "daily_discovery_workflows", "file": "scripts/utilities/daily_discovery_workflows.py", "primary_classes": ["MarketAnalysis", "SessionDiscoveryResult", "DailyDiscoveryWorkflows"], "primary_functions": ["morning_prep", "hunt_patterns", "full_market_intel"], "patterns": [], "lines_of_code": 647, "complexity": 91, "class_count": 3, "function_count": 17, "docstring": "IRONFORGE Daily Discovery Workflows\n===================================\nPractical daily-use workflows for pattern discovery and market analysis.\nDesigned for systematic pattern hunting with actionable...", "imports": 18, "decorators": ["dataclass"]}, {"name": "debug_features", "file": "scripts/utilities/debug_features.py", "primary_classes": [], "primary_functions": ["debug_feature_dimensions"], "patterns": [], "lines_of_code": 89, "complexity": 13, "class_count": 0, "function_count": 1, "docstring": "Debug Price Relativity Feature Dimensions...", "imports": 4, "decorators": []}, {"name": "debug_graph_structure", "file": "scripts/utilities/debug_graph_structure.py", "primary_classes": [], "primary_functions": ["debug_graph_structure"], "patterns": [], "lines_of_code": 106, "complexity": 18, "class_count": 0, "function_count": 1, "docstring": "Debug Graph Structure\n====================\nDebug the actual graph structure returned by enhanced graph builder....", "imports": 4, "decorators": []}, {"name": "debug_lattice", "file": "scripts/utilities/debug_lattice.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 35, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "", "imports": 6, "decorators": []}, {"name": "debug_tgat_init", "file": "scripts/utilities/debug_tgat_init.py", "primary_classes": [], "primary_functions": ["debug_tgat_initialization"], "patterns": [], "lines_of_code": 42, "complexity": 2, "class_count": 0, "function_count": 1, "docstring": "Debug TGAT Initialization for Price Relativity...", "imports": 2, "decorators": []}, {"name": "example_htf_output", "file": "scripts/utilities/example_htf_output.py", "primary_classes": [], "primary_functions": ["demonstrate_htf_integration"], "patterns": [], "lines_of_code": 157, "complexity": 25, "class_count": 0, "function_count": 1, "docstring": "HTF Integration Example - Shows complete multi-timeframe graph output\nDemonstrates the final integrated system with pythonnodes and scale edges...", "imports": 2, "decorators": []}, {"name": "graph_builder_diagnostic", "file": "scripts/utilities/graph_builder_diagnostic.py", "primary_classes": [], "primary_functions": ["diagnose_graph_building"], "patterns": [], "lines_of_code": 121, "complexity": 11, "class_count": 0, "function_count": 1, "docstring": "Graph Builder Diagnostic\n========================\nDebug the graph building process to understand 'node_features' error....", "imports": 7, "decorators": []}, {"name": "htf_builder", "file": "scripts/utilities/htf_builder.py", "primary_classes": ["HTFBuilder"], "primary_functions": [], "patterns": ["Factory", "Builder"], "lines_of_code": 246, "complexity": 48, "class_count": 1, "function_count": 12, "docstring": "HTF Builder Improved - Build sophisticated multi-timeframe data for IRONFORGE\nGenerates pythonnodes arrays and htf_cross_map for graph builder integration...", "imports": 7, "decorators": []}, {"name": "ironforge_discovery_sdk", "file": "scripts/utilities/ironforge_discovery_sdk.py", "primary_classes": ["PatternAnalysis", "CrossSessionLink", "IRONFORGEDiscoverySDK"], "primary_functions": ["quick_discover_all_sessions", "analyze_session_patterns"], "patterns": ["Factory"], "lines_of_code": 522, "complexity": 40, "class_count": 3, "function_count": 16, "docstring": "IRONFORGE Discovery SDK\n======================\nProduction-ready SDK for systematic pattern discovery across 57 enhanced sessions.\nBridges validated archaeological capability into practical daily-use w...", "imports": 24, "decorators": ["dataclass"]}, {"name": "pattern_correlation_visualizer", "file": "scripts/utilities/pattern_correlation_visualizer.py", "primary_classes": ["PatternCorrelationVisualizer"], "primary_functions": [], "patterns": ["Factory"], "lines_of_code": 426, "complexity": 33, "class_count": 1, "function_count": 8, "docstring": "IRONFORGE Pattern Correlation Visualizer\n========================================\n\nAdvanced visualization system for pattern correlation analysis.\nReplaces generic statistical charts with actionable t...", "imports": 12, "decorators": []}, {"name": "pattern_intelligence", "file": "scripts/utilities/pattern_intelligence.py", "primary_classes": ["PatternTrend", "MarketRegime", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PatternIntelligenceEngine"], "primary_functions": ["analyze_market_intelligence", "find_similar_patterns"], "patterns": [], "lines_of_code": 537, "complexity": 43, "class_count": 4, "function_count": 11, "docstring": "IRONFORGE Pattern Intelligence Layer\n===================================\nAdvanced pattern classification, trending analysis, and relationship mapping.\nProvides actionable intelligence from discovered ...", "imports": 23, "decorators": ["dataclass"]}, {"name": "pattern_monitor", "file": "scripts/utilities/pattern_monitor.py", "primary_classes": ["PatternMonitor"], "primary_functions": ["main"], "patterns": [], "lines_of_code": 250, "complexity": 23, "class_count": 1, "function_count": 9, "docstring": "IRONFORGE Pattern Monitor\n=========================\n\nReal-time pattern monitoring and alerting system.\nMonitors for specific patterns and generates alerts.\n\nUsage:\n    python3 pattern_monitor.py [--wa...", "imports": 7, "decorators": []}, {"name": "performance_monitor", "file": "scripts/utilities/performance_monitor.py", "primary_classes": ["PerformanceMetrics", "PerformanceMonitor"], "primary_functions": ["monitor_ironforge_session", "main", "create_graph_analysis", "synthetic_processing"], "patterns": ["Factory"], "lines_of_code": 481, "complexity": 44, "class_count": 2, "function_count": 14, "docstring": "IRONFORGE Performance Monitor - Sprint 2 Enhancement\n===================================================\n\nMonitors 37D + 4 edge types performance vs baseline to ensure Sprint 2 \nstructural intelligenc...", "imports": 15, "decorators": ["dataclass"]}], "file_count": 39, "total_lines": 9772, "complexity_score": 1127, "key_classes": [{"name": "BatchGraphMigrator", "file": "data_migration/batch_migrate_graphs.py", "docstring": "Batch migration system for IRONFORGE graph files\n\nTechnical Debt Surgeon: Comprehensive batch proces..."}, {"name": "SchemaNormalizer", "file": "data_migration/schema_normalizer.py", "docstring": "Technical Debt Surgeon implementation for schema normalization\nMigrates 34D legacy data to 37D tempo..."}, {"name": "PerformanceMonitor", "file": "ironforge/utilities/performance_monitor.py", "docstring": "Monitor performance of IRONFORGE components\nTrack timing, memory usage, and component initialization..."}, {"name": "FeatureAuthenticityValidator", "file": "scripts/analysis/phase2_validation_framework.py", "docstring": "Validates feature authenticity and provides comprehensive contamination analysis\nbefore and after Ph..."}, {"name": "PerformanceProfiler", "file": "scripts/analysis/phase4d_profile_run.py", "docstring": "Performance profiling harness for IRONFORGE workloads...."}, {"name": "EnhancedSessionRelativityProcessor", "file": "scripts/data_processing/enhanced_session_relativity_processor.py", "docstring": "Processes enhanced sessions to add price relativity features\nfor permanent structural pattern discov..."}, {"name": "EnhancedSessionsRelativityProcessor", "file": "scripts/data_processing/enhanced_sessions_price_relativity_processor.py", "docstring": "Processes enhanced sessions to add price relativity features..."}, {"name": "PriceFieldStandardizer", "file": "scripts/data_processing/price_field_standardizer.py", "docstring": "Standardizes price movement field formats for TGAT validation...."}, {"name": "PriceRelativityGenerator", "file": "scripts/data_processing/price_relativity_generator.py", "docstring": "Generates normalized and relational features for permanent pattern discovery..."}, {"name": "SessionQualityAssessor", "file": "scripts/data_processing/session_quality_assessor.py", "docstring": ""}, {"name": "MarketAnalysis", "file": "scripts/utilities/daily_discovery_workflows.py", "docstring": "Daily market analysis result..."}, {"name": "SessionDiscoveryResult", "file": "scripts/utilities/daily_discovery_workflows.py", "docstring": "Real-time session discovery result..."}, {"name": "DailyDiscoveryWorkflows", "file": "scripts/utilities/daily_discovery_workflows.py", "docstring": "Production workflows for daily pattern discovery and market analysis\n\nProvides systematic, actionabl..."}, {"name": "HTFBuilder", "file": "scripts/utilities/htf_builder.py", "docstring": ""}, {"name": "PatternAnalysis", "file": "scripts/utilities/ironforge_discovery_sdk.py", "docstring": "Structured pattern analysis result..."}, {"name": "CrossSessionLink", "file": "scripts/utilities/ironforge_discovery_sdk.py", "docstring": "Cross-session pattern relationship..."}, {"name": "IRONFORGEDiscoverySDK", "file": "scripts/utilities/ironforge_discovery_sdk.py", "docstring": "Production SDK for IRONFORGE archaeological pattern discovery\n\nProvides systematic workflows for dis..."}, {"name": "PatternCorrelationVisualizer", "file": "scripts/utilities/pattern_correlation_visualizer.py", "docstring": "Advanced pattern correlation visualization system..."}, {"name": "PatternTrend", "file": "scripts/utilities/pattern_intelligence.py", "docstring": "Temporal trend in pattern occurrence..."}, {"name": "MarketRegime", "file": "scripts/utilities/pattern_intelligence.py", "docstring": "Identified market regime based on pattern clustering..."}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "scripts/utilities/pattern_intelligence.py", "docstring": "Real-time pattern alert..."}, {"name": "PatternIntelligenceEngine", "file": "scripts/utilities/pattern_intelligence.py", "docstring": "Advanced pattern intelligence system for actionable trading insights\n\nProvides sophisticated analysi..."}, {"name": "PatternMonitor", "file": "scripts/utilities/pattern_monitor.py", "docstring": "Real-time pattern monitoring system..."}, {"name": "PerformanceMetrics", "file": "scripts/utilities/performance_monitor.py", "docstring": "Container for performance measurement results..."}, {"name": "PerformanceMonitor", "file": "scripts/utilities/performance_monitor.py", "docstring": "Monitor IRONFORGE performance with Sprint 2 enhancements\nTracks 37D + 4 edge types vs baseline perfo..."}], "public_interfaces": [{"name": "main", "file": "data_migration/batch_migrate_graphs.py", "parameters": 0, "docstring": "Main execution with command line argument parsing..."}, {"name": "main", "file": "data_migration/schema_normalizer.py", "parameters": 0, "docstring": "Command-line interface for schema migration..."}, {"name": "build_motifs_input", "file": "ironforge/scripts/prepare_motifs_input.py", "parameters": 2, "docstring": ""}, {"name": "main", "file": "ironforge/scripts/prepare_motifs_input.py", "parameters": 1, "docstring": ""}, {"name": "generate_authentic_htf_carryover", "file": "scripts/analysis/complete_phase2_enhancement.py", "parameters": 2, "docstring": "Generate authentic HTF carryover strength based on market conditions...."}, {"name": "generate_authentic_energy_density", "file": "scripts/analysis/complete_phase2_enhancement.py", "parameters": 2, "docstring": "Calculate authentic energy density based on actual market activity...."}, {"name": "generate_liquidity_events", "file": "scripts/analysis/complete_phase2_enhancement.py", "parameters": 2, "docstring": "Generate realistic liquidity events based on session characteristics...."}, {"name": "enhance_session_features", "file": "scripts/analysis/complete_phase2_enhancement.py", "parameters": 1, "docstring": "Enhance a single session with authentic feature calculations...."}, {"name": "process_remaining_sessions", "file": "scripts/analysis/complete_phase2_enhancement.py", "parameters": 0, "docstring": "Process all remaining TGAT-ready sessions for complete Phase 2 coverage...."}, {"name": "extract_event_sequences", "file": "scripts/analysis/investigate_causal_event_chains.py", "parameters": 0, "docstring": "Extract chronological event sequences from each session..."}, {"name": "build_transition_matrices", "file": "scripts/analysis/investigate_causal_event_chains.py", "parameters": 1, "docstring": "Build event transition matrices within each session..."}, {"name": "analyze_lag_profiles", "file": "scripts/analysis/investigate_causal_event_chains.py", "parameters": 2, "docstring": "Calculate lag histograms and consistency metrics for each transition pair..."}, {"name": "identify_causal_chains", "file": "scripts/analysis/investigate_causal_event_chains.py", "parameters": 2, "docstring": "Identify causal chains with lag consistency >80%..."}, {"name": "test_specific_hypothesis", "file": "scripts/analysis/investigate_causal_event_chains.py", "parameters": 2, "docstring": "Test specific hypothesis: expansion_phase → consolidation → liq_sweep..."}, {"name": "create_causal_chain_visualization", "file": "scripts/analysis/investigate_causal_event_chains.py", "parameters": 3, "docstring": "Create visualization of causal chain discoveries..."}, {"name": "main", "file": "scripts/analysis/investigate_causal_event_chains.py", "parameters": 0, "docstring": "Main causal chain investigation..."}, {"name": "extract_liquidity_sweep_sequences", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "parameters": 0, "docstring": "Extract all sequences that START with liq_sweep events..."}, {"name": "analyze_immediate_responses", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "parameters": 2, "docstring": "Analyze what happens immediately after liq_sweep events..."}, {"name": "discover_catalyst_chains", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "parameters": 2, "docstring": "Discover the most common event chains triggered by liq_sweep..."}, {"name": "analyze_catalyst_timing_patterns", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "parameters": 1, "docstring": "Analyze when liq_sweeps occur and their effectiveness as catalysts..."}, {"name": "test_specific_catalyst_hypotheses", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "parameters": 1, "docstring": "Test specific hypotheses about liq_sweep catalyst behavior..."}, {"name": "create_catalyst_visualization", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "parameters": 4, "docstring": "Create comprehensive visualization of liq_sweep catalyst behavior..."}, {"name": "main", "file": "scripts/analysis/investigate_liquidity_sweep_catalyst.py", "parameters": 0, "docstring": "Main liquidity sweep catalyst investigation..."}, {"name": "main", "file": "scripts/analysis/phase2_validation_framework.py", "parameters": 0, "docstring": "Main validation execution...."}, {"name": "validate_decontamination", "file": "scripts/analysis/phase2_validation_summary.py", "parameters": 0, "docstring": "Show before/after decontamination results...."}, {"name": "run_phase4d_performance_validation", "file": "scripts/analysis/phase4d_profile_run.py", "parameters": 0, "docstring": "Run Phase 4d performance validation...."}, {"name": "save_profile_csv", "file": "scripts/analysis/phase4d_profile_run.py", "parameters": 1, "docstring": "Save profiling results to CSV...."}, {"name": "run_direct_discovery", "file": "scripts/analysis/run_direct_discovery.py", "parameters": 0, "docstring": ""}, {"name": "run_full_discovery", "file": "scripts/analysis/run_manual_discovery.py", "parameters": 0, "docstring": ""}, {"name": "main", "file": "scripts/data_processing/enhanced_session_relativity_processor.py", "parameters": 0, "docstring": "Main execution: Transform all enhanced sessions to use price relativity..."}, {"name": "main", "file": "scripts/data_processing/enhanced_sessions_price_relativity_processor.py", "parameters": 0, "docstring": "Main execution: Transform all enhanced sessions to use price relativity..."}, {"name": "main", "file": "scripts/data_processing/price_field_standardizer.py", "parameters": 0, "docstring": "Run price field standardization...."}, {"name": "main", "file": "scripts/data_processing/price_relativity_generator.py", "parameters": 0, "docstring": "Main execution: Transform all HTF sessions to use price relativity\n\nTechnical Debt Surgeon: Enhanced..."}, {"name": "main", "file": "scripts/data_processing/session_quality_assessor.py", "parameters": 0, "docstring": "Run complete quality assessment on all Level 1 sessions..."}, {"name": "sanitize_session_data", "file": "scripts/data_processing/unicode_fix.py", "parameters": 1, "docstring": "Recursively sanitize all strings in data structure..."}, {"name": "load_clean_sessions", "file": "scripts/data_processing/unicode_fix.py", "parameters": 0, "docstring": "Load all sessions with sanitization..."}, {"name": "measure_import_time", "file": "scripts/utilities/benchmark_performance.py", "parameters": 2, "docstring": "Measure time to import a module and optionally get a class..."}, {"name": "benchmark_core_imports", "file": "scripts/utilities/benchmark_performance.py", "parameters": 0, "docstring": "Benchmark core IRONFORGE imports..."}, {"name": "benchmark_container_loading", "file": "scripts/utilities/benchmark_performance.py", "parameters": 0, "docstring": "Benchmark lazy loading container performance..."}, {"name": "main", "file": "scripts/utilities/benchmark_performance.py", "parameters": 0, "docstring": "Run complete performance benchmark..."}, {"name": "morning_prep", "file": "scripts/utilities/daily_discovery_workflows.py", "parameters": 1, "docstring": "Quick morning market preparation..."}, {"name": "hunt_patterns", "file": "scripts/utilities/daily_discovery_workflows.py", "parameters": 1, "docstring": "Quick pattern hunting for specific session..."}, {"name": "full_market_intel", "file": "scripts/utilities/daily_discovery_workflows.py", "parameters": 0, "docstring": "Complete daily market intelligence workflow..."}, {"name": "debug_feature_dimensions", "file": "scripts/utilities/debug_features.py", "parameters": 0, "docstring": ""}, {"name": "debug_graph_structure", "file": "scripts/utilities/debug_graph_structure.py", "parameters": 0, "docstring": "Debug the actual graph structure...."}, {"name": "debug_tgat_initialization", "file": "scripts/utilities/debug_tgat_init.py", "parameters": 0, "docstring": ""}, {"name": "demonstrate_htf_integration", "file": "scripts/utilities/example_htf_output.py", "parameters": 0, "docstring": "Create example HTF output showing all features..."}, {"name": "diagnose_graph_building", "file": "scripts/utilities/graph_builder_diagnostic.py", "parameters": 0, "docstring": "Diagnose what's happening in graph building process...."}, {"name": "quick_discover_all_sessions", "file": "scripts/utilities/ironforge_discovery_sdk.py", "parameters": 0, "docstring": "Quick function to run discovery on all sessions..."}, {"name": "analyze_session_patterns", "file": "scripts/utilities/ironforge_discovery_sdk.py", "parameters": 1, "docstring": "Quick function to analyze patterns in a specific session..."}, {"name": "analyze_market_intelligence", "file": "scripts/utilities/pattern_intelligence.py", "parameters": 0, "docstring": "Complete market intelligence analysis workflow..."}, {"name": "find_similar_patterns", "file": "scripts/utilities/pattern_intelligence.py", "parameters": 2, "docstring": "Find patterns similar to a specific pattern from a session..."}, {"name": "main", "file": "scripts/utilities/pattern_monitor.py", "parameters": 0, "docstring": ""}, {"name": "monitor_ironforge_session", "file": "scripts/utilities/performance_monitor.py", "parameters": 3, "docstring": "Convenience function to monitor a complete IRONFORGE session\n\nArgs:\n    session_data: Input session ..."}, {"name": "main", "file": "scripts/utilities/performance_monitor.py", "parameters": 0, "docstring": "Command-line interface for performance monitoring..."}, {"name": "create_graph_analysis", "file": "scripts/utilities/performance_monitor.py", "parameters": 2, "docstring": "Create graph analysis for performance monitoring..."}, {"name": "synthetic_processing", "file": "scripts/utilities/performance_monitor.py", "parameters": 1, "docstring": "Synthetic processing function for testing..."}], "avg_complexity": 28.9}, "data": {"description": "Data storage and preservation", "components": [{"name": "__init__", "file": "ironforge/data_engine/__init__.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 1, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Data engine for Parquet shards and validated storage....", "imports": 0, "decorators": []}, {"name": "parquet_writer", "file": "ironforge/data_engine/parquet_writer.py", "primary_classes": [], "primary_functions": ["write_nodes", "write_edges"], "patterns": [], "lines_of_code": 37, "complexity": 7, "class_count": 0, "function_count": 4, "docstring": "Validated Parquet writers for nodes and edges....", "imports": 6, "decorators": []}, {"name": "schemas", "file": "ironforge/data_engine/schemas.py", "primary_classes": [], "primary_functions": [], "patterns": [], "lines_of_code": 16, "complexity": 0, "class_count": 0, "function_count": 0, "docstring": "Authoritative column definitions and data types for IRONFORGE data storage....", "imports": 0, "decorators": []}], "file_count": 3, "total_lines": 54, "complexity_score": 7, "key_classes": [], "public_interfaces": [{"name": "write_nodes", "file": "ironforge/data_engine/parquet_writer.py", "parameters": 2, "docstring": "Write nodes DataFrame to Parquet with validation...."}, {"name": "write_edges", "file": "ironforge/data_engine/parquet_writer.py", "parameters": 2, "docstring": "Write edges DataFrame to Parque<PERSON> with validation...."}], "avg_complexity": 2.33}, "metadata": {"total_files_classified": 158, "unclassified_files": ["__init__.py", "ironforge/metrics/confluence.py", "ironforge/motifs/__init__.py", "ironforge/sdk/__main__.py"], "classification_coverage": 97.53, "engine_distribution": {"analysis": 62, "learning": 10, "synthesis": 5, "integration": 25, "validation": 9, "reporting": 5, "utilities": 39, "data": 3}}}, "summary": {"total_engines": 8, "total_components": 158, "engine_distribution": {"analysis": 62, "learning": 10, "synthesis": 5, "integration": 25, "validation": 9, "reporting": 5, "utilities": 39, "data": 3}}}