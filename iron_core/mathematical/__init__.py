"""
Iron-Core Mathematical Module
============================

Shared mathematical components for IRON ecosystem including:
- Fisher Information Monitor
- Hawkes Engine 
- Adaptive RG Optimizer
- Scaling Patterns Manager
- Temporal Correlation Engine
- 5-Layer Mathematical Framework
"""

# Mathematical components are loaded lazily through the container system
# Import the container to access mathematical components:
#
# from iron_core.performance import get_container
# container = get_container()
# hawkes_engine = container.get_mathematical_component('hawkes_engine')

__all__ = []  # Mathematical components accessed through container system