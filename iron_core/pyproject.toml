[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "iron-core"
version = "1.0.0"
description = "Shared infrastructure for IRON ecosystem"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "IRON Ecosystem", email = "<EMAIL>"},
]
keywords = [
    "iron-core",
    "dependency-injection", 
    "lazy-loading",
    "performance-optimization",
    "mathematical-computing",
    "financial-modeling",
    "pytorch",
    "ecosystem-infrastructure",
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry", 
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Office/Business :: Financial",
]
requires-python = ">=3.8"
dependencies = [
    "torch>=1.9.0,<2.5.0",
    "numpy>=1.21.0,<2.0.0",
    "scikit-learn>=1.0.0,<1.5.0",
    "typing-extensions>=4.0.0",
]

[project.urls]
"Homepage" = "https://github.com/iron-ecosystem/iron-core"
"Documentation" = "https://github.com/iron-ecosystem/iron-core/docs"
"Repository" = "https://github.com/iron-ecosystem/iron-core"
"Bug Tracker" = "https://github.com/iron-ecosystem/iron-core/issues"

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-asyncio>=0.18.0",
    "pytest-cov>=2.12.0",
    "black>=21.0",
    "mypy>=0.910",
]
performance = [
    "psutil>=5.8.0",
    "memory-profiler>=0.60.0",
]
visualization = [
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
]

[project.scripts]
iron-core-test = "iron_core.validation:test_installation"

[tool.setuptools.packages.find]
# No additional configuration needed

[tool.setuptools.package-data]
iron_core = ["py.typed"]