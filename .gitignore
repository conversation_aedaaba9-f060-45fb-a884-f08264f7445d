# Python
*.pyc
*.pyo
*.pyd
__pycache__/
*.so
.Python

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Cache directories
.cache/
.pytest_cache/

# Generated artifacts (IRONFORGE refactor v0.7.1)
deliverables/
demo_deliverables/
discovery_cache/
results/
test_outputs/
preservation/
reports/

# Virtual environments (specific to IRONFORGE)
ironforge_env/
ironforge_refactor_env/
*_env/

# Additional Python cache
*.egg-info/

# Oracle and model artifacts (Phase 2 cleanup)
models/**/
runs/**/
data/**/training_pairs.parquet
oracle_probe/
*.pt
*.pkl
dist/*.whl

# Session fingerprinting artifacts (v1.0.2+ - Release assets policy)
# NOTE: Model artifacts NOT included in release distributions due to size and optional nature
# Production deployments should build offline library from historical data
models/session_fingerprints/**/
**/session_fingerprint.json
demo_run_output/

# Test and temporary directories
test_oracle_weights/
test_runs/
models/oracle/test*/
test_session_fingerprinting/
test_offline_library_builder/
test_online_classifier/