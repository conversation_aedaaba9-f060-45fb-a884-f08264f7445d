# IRONFORGE Remote Agent Configuration
# ===================================

# Agent Gateway Configuration
gateway:
  host: "0.0.0.0"
  port: 8001
  max_connections: 1000
  heartbeat_timeout: 60  # seconds
  message_queue_size: 1000
  
  # TLS Configuration (optional)
  tls:
    enabled: false
    cert_file: "certs/gateway.crt"
    key_file: "certs/gateway.key"
    ca_file: "certs/ca.crt"

# Agent Orchestrator Configuration
orchestrator:
  max_concurrent_tasks_per_agent: 1
  task_timeout: 300  # seconds
  heartbeat_timeout: 60  # seconds
  cleanup_interval: 3600  # seconds
  
  # Task Queue Configuration
  task_queue:
    max_size: 10000
    priority_levels: 5
    retry_policy:
      max_retries: 3
      retry_delay: 30  # seconds
      exponential_backoff: true

# Service Discovery Configuration
service_discovery:
  health_check_interval: 30  # seconds
  service_ttl_default: 300  # seconds
  cleanup_interval: 60  # seconds
  config_file: "configs/services.json"
  
  # Predefined Services
  services:
    - service_id: "ironforge-gateway"
      service_type: "gateway"
      host: "localhost"
      port: 8001
      protocol: "ws"
      path: "/ws/agent"
      health_check_url: "http://localhost:8001/api/health"
      tags: ["gateway", "websocket"]
      ttl: 300
    
    - service_id: "ironforge-api"
      service_type: "api"
      host: "localhost"
      port: 8000
      protocol: "http"
      path: "/api"
      health_check_url: "http://localhost:8000/health"
      tags: ["api", "rest"]
      ttl: 300

# Authentication Configuration
authentication:
  # JWT Configuration
  jwt:
    algorithm: "HS256"  # or "RS256" for RSA
    expiration_hours: 24
    secret_key: null  # Will be generated if not provided
    
    # RSA Keys (only if using RS256)
    private_key_path: "certs/jwt_private.pem"
    public_key_path: "certs/jwt_public.pem"
  
  # API Key Configuration
  api_keys:
    default_expiration_days: 365
    max_keys_per_user: 10
  
  # Role-Based Access Control
  roles:
    admin:
      permissions: ["read", "write", "execute", "admin"]
      description: "Full system access"
    
    agent:
      permissions: ["read", "write", "execute"]
      description: "Agent execution access"
    
    worker:
      permissions: ["read", "execute"]
      description: "Worker execution access"
    
    viewer:
      permissions: ["read"]
      description: "Read-only access"

# Remote Agent Configuration
remote_agent:
  # Connection Settings
  connection:
    reconnect_delay: 5  # seconds
    max_reconnect_attempts: 10
    heartbeat_interval: 30  # seconds
    connection_timeout: 10  # seconds
  
  # Task Execution Settings
  execution:
    max_concurrent_tasks: 1
    task_timeout: 300  # seconds
    result_cache_size: 100
  
  # Agent Capabilities
  capabilities:
    default: ["discovery", "confluence", "validation", "analysis"]
    discovery_agent: ["discovery"]
    analysis_agent: ["analysis", "validation"]
    full_agent: ["discovery", "confluence", "validation", "analysis"]

# Monitoring and Logging Configuration
monitoring:
  # Metrics Collection
  metrics:
    enabled: true
    collection_interval: 30  # seconds
    retention_days: 30
    
    # Prometheus Integration (optional)
    prometheus:
      enabled: false
      port: 9090
      path: "/metrics"
  
  # Health Checks
  health_checks:
    enabled: true
    interval: 30  # seconds
    timeout: 5  # seconds
    
    # Health Check Endpoints
    endpoints:
      - name: "gateway"
        url: "http://localhost:8001/api/health"
      - name: "api"
        url: "http://localhost:8000/health"
  
  # Logging Configuration
  logging:
    level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # File Logging
    file:
      enabled: true
      path: "logs/remote_agents.log"
      max_size_mb: 100
      backup_count: 5
    
    # Console Logging
    console:
      enabled: true
      colored: true

# Scaling Configuration
scaling:
  # Auto-scaling Settings
  auto_scaling:
    enabled: false
    min_agents: 1
    max_agents: 10
    
    # Scaling Triggers
    triggers:
      cpu_threshold: 80  # percent
      memory_threshold: 80  # percent
      queue_depth_threshold: 100
      response_time_threshold: 5000  # milliseconds
  
  # Load Balancing
  load_balancing:
    strategy: "round_robin"  # round_robin, least_connections, weighted
    health_check_required: true

# Security Configuration
security:
  # Network Security
  network:
    allowed_hosts: ["localhost", "127.0.0.1"]
    blocked_hosts: []
    rate_limiting:
      enabled: true
      requests_per_minute: 1000
      burst_size: 100
  
  # Data Security
  data:
    encryption_at_rest: false
    encryption_in_transit: true
    data_retention_days: 90
  
  # Audit Logging
  audit:
    enabled: true
    log_file: "logs/audit.log"
    events: ["login", "logout", "task_assignment", "task_completion", "error"]

# Development and Testing Configuration
development:
  # Mock Services
  mock_services:
    enabled: false
    discovery_service: true
    authentication_service: true
  
  # Debug Settings
  debug:
    enabled: false
    verbose_logging: false
    trace_requests: false
  
  # Testing
  testing:
    test_agents: 3
    test_tasks_per_agent: 10
    test_duration_minutes: 5

# Production Configuration Overrides
production:
  gateway:
    host: "0.0.0.0"
    port: 8001
  
  authentication:
    jwt:
      algorithm: "RS256"
      expiration_hours: 8
  
  monitoring:
    metrics:
      enabled: true
      prometheus:
        enabled: true
  
  security:
    network:
      rate_limiting:
        enabled: true
        requests_per_minute: 500
    
    audit:
      enabled: true
  
  scaling:
    auto_scaling:
      enabled: true
      min_agents: 2
      max_agents: 20
