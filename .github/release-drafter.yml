name-template: 'v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'
categories:
  - title: 'Features'
    labels: ['feat', 'feature', 'enhancement']
  - title: 'Fixes'
    labels: ['fix', 'bug', 'bugfix']
  - title: 'Performance'
    labels: ['perf', 'performance']
  - title: 'Documentation'
    labels: ['docs']
  - title: 'Refactoring'
    labels: ['refactor']
  - title: 'CI/CD'
    labels: ['ci']
  - title: 'Chore'
    labels: ['chore', 'build']
change-template: '- $TITLE (#$NUMBER) by @$AUTHOR'
exclude-labels: ['skip-changelog']
version-resolver:
  major:
    labels: ['major', 'breaking']
  minor:
    labels: ['minor', 'feat', 'feature', 'enhancement']
  patch:
    labels: ['patch', 'fix', 'bug', 'bugfix', 'perf', 'performance', 'ci', 'chore', 'refactor', 'docs']
template: |
  ## What's Changed
  $CHANGES