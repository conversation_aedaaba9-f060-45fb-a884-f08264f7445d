name: release-assets

on:
  release:
    types: [published, created]

jobs:
  build-and-attach:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          cache: 'pip'
      - name: Upgrade pip
        run: python -m pip install -U pip
      - name: Install build deps
        run: pip install -e ".[dev]" build
      - name: Build wheel
        run: python -m build
      - name: Attach wheel to release
        uses: softprops/action-gh-release@v2
        with:
          files: dist/*.whl

