name: Nightly Smoke

on:
  schedule:
    - cron: '0 7 * * *' # 07:00 UTC daily

jobs:
  smoke:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install -U pip
          pip install -e .[dev]

      - name: Verify package presence
        run: |
          test -d ironforge || (echo "ironforge/ package missing" && exit 1)

      - name: Ruff (lint)
        run: |
          ruff check ironforge tests

      - name: My<PERSON> (type-check)
        run: |
          mypy ironforge

      - name: Pyte<PERSON> (smoke)
        run: |
          pytest -q -m "smoke"

