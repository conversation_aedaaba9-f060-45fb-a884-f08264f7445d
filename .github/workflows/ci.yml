name: ci
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    strategy: {matrix: {python-version: ['3.10','3.11','3.12']}}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with: {python-version: ${{ matrix.python-version }}, cache: 'pip'}
      - run: python -m pip install -U pip
      - run: pip install -e ".[dev]" ruff black mypy pytest build twine
      - name: Ruff
        run: ruff check .
      - name: Black (check)
        run: black --check .
      - name: mypy
        run: mypy ironforge
      - name: Tests
        run: pytest -q
      - name: Build & Twine check
        run: python -m build && twine check dist/*
      - name: Upload wheel artifact
        if: matrix.python-version == '3.11'
        uses: actions/upload-artifact@v4
        with:
          name: ironforge-wheel-${{ github.sha }}
          path: dist/*.whl
          if-no-files-found: error
      - name: Smoke install
        run: |
          python -m pip install dist/*.whl
          python - <<'PY'
          import ironforge, ironforge.reporting as r
          print("ironforge:", ironforge.__version__, "| reporting:", r.__version__)
          PY
