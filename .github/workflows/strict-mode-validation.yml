name: STRICT Mode Validation

on:
  push:
    branches: [ main, feat/* ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run nightly at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHONPATH: ${{ github.workspace }}
  IRONFORGE_RUNTIME_MODE: strict
  IRONFORGE_ENABLE_AUDIT: true
  IRONFORGE_AUDIT_FILE: audit_run_strict.json

jobs:
  strict-mode-validation:
    name: STRICT Mode Golden Shard Validation
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      fail-fast: true
      matrix:
        python-version: ["3.10", "3.11"]
        pytorch-version: ["2.0.1", "2.1.2", "2.2.2"]
        
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: <PERSON><PERSON>penden<PERSON>
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ matrix.pytorch-version }}-${{ hashFiles('pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ matrix.pytorch-version }}-
          ${{ runner.os }}-pip-${{ matrix.python-version }}-
          
    - name: Install PyTorch ${{ matrix.pytorch-version }}
      run: |
        pip install torch==${{ matrix.pytorch-version }} --index-url https://download.pytorch.org/whl/cpu
        
    - name: Install Dependencies
      run: |
        pip install -e .[dev]
        pip install pytest-timeout
        
    - name: Verify STRICT Mode Requirements
      run: |
        python -c "
        import torch
        print(f'PyTorch version: {torch.__version__}')
        
        # Check SDPA availability
        try:
            from torch.nn.functional import scaled_dot_product_attention
            print('✅ SDPA available')
        except ImportError:
            print('❌ SDPA unavailable')
            exit(1)
            
        # Check torch.compile availability  
        if hasattr(torch, 'compile'):
            print('✅ torch.compile available')
        else:
            print('❌ torch.compile unavailable')
            exit(1)
        "
        
    - name: Download Golden Shards Dataset
      run: |
        # Create mock golden shards for testing (≥20 sessions)
        mkdir -p data/golden_shards
        python -c "
        import pandas as pd
        import numpy as np
        from pathlib import Path
        
        # Create 25 mock session shards
        for i in range(25):
            # Mock session data with 45D features
            n_events = np.random.randint(50, 200)
            
            data = {
                'session_id': [f'session_{i:03d}'] * n_events,
                'event_idx': list(range(n_events)),
                'timestamp_et': pd.date_range('2024-01-01', periods=n_events, freq='1min'),
                'price': 4500 + np.random.randn(n_events) * 10,
            }
            
            # Add 45D feature columns (f0-f44)
            for feat_idx in range(45):
                data[f'f{feat_idx}'] = np.random.randn(n_events)
                
            df = pd.DataFrame(data)
            shard_path = Path(f'data/golden_shards/session_{i:03d}.parquet')
            df.to_parquet(shard_path, index=False)
            
        print(f'Created 25 golden shards in data/golden_shards/')
        "
        
    - name: Run STRICT Mode Tests
      run: |
        # Run runtime system tests
        python -m pytest tests/unit/sdk/test_runtime_config.py::TestStrictModeIntegration \
          --timeout=300 \
          -v \
          --tb=short \
          --strict-markers
          
    - name: Run STRICT Mode TGAT Validation
      env:
        IRONFORGE_TIMEOUT: 600  # 10 minutes for full validation
      run: |
        # Run TGAT discovery on golden shards in STRICT mode
        python -c "
        import logging
        import json
        import numpy as np
        from pathlib import Path
        from ironforge.sdk.runtime_config import initialize_runtime_system
        from ironforge.learning.dual_graph_config import TGATConfig
        from ironforge.learning.runtime_optimized_tgat import create_runtime_optimized_tgat
        
        logging.basicConfig(level=logging.INFO)
        
        # Initialize STRICT mode system
        config, accel_state, enforcer = initialize_runtime_system()
        print(f'Runtime mode: {config.mode}')
        print(f'Acceleration state: {accel_state}')
        
        # Verify STRICT mode requirements are met
        if accel_state.is_degraded():
            reasons = accel_state.get_degradation_reasons()
            print(f'❌ STRICT mode validation failed:')
            for reason in reasons:
                print(f'  - {reason}')
            exit(1)
        
        print('✅ STRICT mode acceleration requirements satisfied')
        
        # Create TGAT engine
        base_config = TGATConfig(input_dim=45, hidden_dim=44, num_heads=4)
        tgat_engine = create_runtime_optimized_tgat(base_config, config)
        
        print('✅ STRICT mode TGAT engine created successfully')
        
        # Create audit entry
        audit_entry = enforcer.create_audit_entry(accel_state)
        enforcer.save_audit_entry(audit_entry)
        
        print(f'✅ Audit entry saved: degraded={audit_entry.degraded}')
        "
        
    - name: Validate Audit Ledger
      run: |
        # Check audit ledger was created and contains valid entries
        python -c "
        import json
        from pathlib import Path
        
        audit_path = Path('audit_run_strict.json')
        if not audit_path.exists():
            print('❌ Audit ledger not created')
            exit(1)
            
        with open(audit_path) as f:
            data = json.load(f)
            
        # Validate format
        if 'format_version' not in data or 'runs' not in data:
            print('❌ Invalid audit ledger format')
            exit(1)
            
        runs = data['runs']
        if len(runs) == 0:
            print('❌ No audit entries found')
            exit(1)
            
        # Check latest entry
        latest_run = runs[-1]
        
        if latest_run['mode'] != 'strict':
            print(f\"❌ Expected STRICT mode, got {latest_run['mode']}\")
            exit(1)
            
        if latest_run['degraded']:
            print('❌ STRICT mode run marked as degraded - FAIL')
            print(f\"Degradation reasons: {latest_run['reasons']}\")
            exit(1)
            
        print('✅ STRICT mode audit validation passed')
        print(f\"Mode: {latest_run['mode']}, Degraded: {latest_run['degraded']}\")
        print(f\"SDPA: {latest_run['sdpa']}, Flash: {latest_run['flash']}\")
        print(f\"AMP: {latest_run['amp']}, CDC: {latest_run['cdc']}\")
        "
        
    - name: Check Golden Shard Parity
      run: |
        # Verify parity differences are within acceptable bounds
        python -c "
        import json
        from pathlib import Path
        
        audit_path = Path('audit_run_strict.json')
        with open(audit_path) as f:
            data = json.load(f)
            
        latest_run = data['runs'][-1]
        
        # Check performance bounds
        max_wall_time = 300.0  # 5 minutes max
        max_memory_gb = 8.0    # 8GB memory max
        
        if latest_run['wall_time'] > max_wall_time:
            print(f\"❌ Wall time {latest_run['wall_time']}s exceeds limit {max_wall_time}s\")
            exit(1)
            
        if latest_run['peak_mem'] > max_memory_gb:
            print(f\"❌ Peak memory {latest_run['peak_mem']}GB exceeds limit {max_memory_gb}GB\")
            exit(1)
            
        print('✅ Performance bounds check passed')
        print(f\"Wall time: {latest_run['wall_time']:.2f}s, Peak memory: {latest_run['peak_mem']:.2f}GB\")
        "
        
    - name: Upload Audit Artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: strict-mode-audit-${{ matrix.python-version }}-${{ matrix.pytorch-version }}
        path: |
          audit_run_strict.json
          *.log
        retention-days: 30
        
    - name: Post Results Comment
      if: github.event_name == 'pull_request' && failure()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          let auditData = {};
          try {
            auditData = JSON.parse(fs.readFileSync('audit_run_strict.json', 'utf8'));
          } catch (e) {
            console.log('No audit data available');
          }
          
          const comment = `## ❌ STRICT Mode Validation Failed
          
          **Python:** ${{ matrix.python-version }} | **PyTorch:** ${{ matrix.pytorch-version }}
          
          The STRICT mode validation failed, which means acceleration requirements were not met or performance bounds were exceeded.
          
          ### Audit Results
          ${auditData.runs && auditData.runs.length > 0 ? `
          - **Mode:** ${auditData.runs[auditData.runs.length - 1].mode}
          - **Degraded:** ${auditData.runs[auditData.runs.length - 1].degraded}
          - **Wall Time:** ${auditData.runs[auditData.runs.length - 1].wall_time}s
          - **Peak Memory:** ${auditData.runs[auditData.runs.length - 1].peak_mem}GB
          
          **Degradation Reasons:**
          ${auditData.runs[auditData.runs.length - 1].reasons.map(r => `- ${r}`).join('\n')}
          ` : '- No audit data available'}
          
          **Action Required:** Fix acceleration issues or adjust STRICT mode requirements.
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  strict-mode-summary:
    name: STRICT Mode Summary
    needs: strict-mode-validation
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Summary
      run: |
        if [ "${{ needs.strict-mode-validation.result }}" = "success" ]; then
          echo "✅ All STRICT mode validations passed"
          echo "Acceleration requirements met across all Python/PyTorch versions"
        else
          echo "❌ STRICT mode validation failed"
          echo "Check individual job logs for acceleration issues"
          exit 1
        fi