#!/usr/bin/env python3
"""
Setup script for IRONFORGE Archaeological Discovery System
"""

import os
import re

from setuptools import find_packages, setup

# Read README if it exists
readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
long_description = ""
if os.path.exists(readme_path):
    with open(readme_path, encoding='utf-8') as f:
        long_description = f.read()

def _read_version():
    here = os.path.dirname(__file__)
    with open(os.path.join(here, "ironforge", "__version__.py"), encoding="utf-8") as f:
        m = re.search(r'__version__\s*=\s*"([^"]+)"', f.read())
        if not m:
            raise RuntimeError("Could not read __version__ from ironforge/__version__.py")
        return m.group(1)


setup(
    name="ironforge",
    version=_read_version(),
    author="IRON Ecosystem",
    author_email="<EMAIL>",
    description="Archaeological discovery system for market pattern analysis",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/iron-ecosystem/ironforge",
    packages=find_packages(include=['ironforge*']),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",


        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.10",
    install_requires=[
        "numpy>=1.20.0",
        "pandas>=1.3.0",
        "torch>=1.9.0",
        "matplotlib>=3.3.0",
        "seaborn>=0.11.0",
        "scikit-learn>=1.0.0",
        "networkx>=2.5",
        "tqdm>=4.60.0",
        "PyYAML>=6.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "black>=21.0",
            "ruff>=0.6.8",
            "mypy>=0.800",
            "bandit>=1.7.9",
            "coverage>=7.3.2",
            "pytest-benchmark>=4.0.0",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    entry_points={
        "console_scripts": [
            "ironforge=ironforge.sdk.cli:main",
            "ifg=ironforge.sdk.cli:main",
        ]
    },
)
